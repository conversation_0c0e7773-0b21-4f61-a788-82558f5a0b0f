<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>访问被拒绝 - 鱼苗总后台管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: #ff9a9e;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .error-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 3rem;
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        .error-code {
            font-size: 6rem;
            font-weight: bold;
            color: #ff6b6b;
            margin-bottom: 1rem;
        }
        .error-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 1rem;
        }
        .error-message {
            color: #666;
            margin-bottom: 2rem;
            font-size: 1.1rem;
        }
        .btn-home {
            background: #ff9a9e;
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            color: white;
            text-decoration: none;
            transition: all 0.3s;
        }
        .btn-home:hover {
            background: #ff888c;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 154, 158, 0.3);
            color: white;
        }
        .error-icon {
            color: #ff6b6b;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">
            <i class="fas fa-ban fa-4x"></i>
        </div>
        <div class="error-code">403</div>
        <h1 class="error-title">访问被拒绝</h1>
        <p class="error-message">
            抱歉，您没有权限访问此页面。<br>
            请确认您已正确登录，或联系管理员获取相应权限。
        </p>
        <div class="d-flex gap-3 justify-content-center">
            <a href="{{ url_for('auth.login') }}" class="btn btn-outline-secondary">
                <i class="fas fa-sign-in-alt me-2"></i>
                重新登录
            </a>
            <a href="{{ url_for('main.dashboard') }}" class="btn-home">
                <i class="fas fa-home me-2"></i>
                返回首页
            </a>
        </div>
    </div>
</body>
</html>
