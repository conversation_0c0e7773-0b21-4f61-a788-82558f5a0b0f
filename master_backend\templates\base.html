<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}鱼苗总后台管理系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-color: #1f2937;
            --primary-light: #374151;
            --secondary-color: #6b7280;
            --accent-color: #3b82f6;
            --accent-hover: #2563eb;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #06b6d4;
            --light-bg: #f9fafb;
            --white: #ffffff;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --border-radius: 8px;
            --border-radius-lg: 12px;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--light-bg);
            color: var(--gray-700);
            line-height: 1.6;
            font-size: 14px;
            margin: 0;
            padding: 0;
        }

        .sidebar {
            min-height: 100vh;
            background: var(--white);
            border-right: 1px solid var(--gray-200);
            box-shadow: var(--shadow-sm);
            width: 280px;
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1000;
        }

        .sidebar-brand {
            padding: 1.5rem 1.25rem;
            border-bottom: 1px solid var(--gray-200);
            background: var(--white);
        }

        .sidebar-brand h4 {
            color: var(--gray-800);
            font-weight: 700;
            margin: 0;
            font-size: 1.25rem;
        }

        .sidebar-brand small {
            color: var(--gray-500);
            font-weight: 500;
            font-size: 0.875rem;
        }

        .sidebar .nav {
            padding: 1rem 0;
        }

        .sidebar .nav-link {
            color: var(--gray-600);
            border-radius: var(--border-radius);
            margin: 0.125rem 1rem;
            padding: 0.75rem 1rem;
            transition: all 0.15s ease;
            font-weight: 500;
            font-size: 0.875rem;
            border: 1px solid transparent;
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .sidebar .nav-link:hover {
            color: var(--accent-color);
            background-color: #eff6ff;
            border-color: #dbeafe;
            text-decoration: none;
        }

        .sidebar .nav-link.active {
            color: var(--accent-color);
            background-color: #eff6ff;
            border-color: #bfdbfe;
            font-weight: 600;
        }

        .sidebar .nav-link i {
            width: 20px;
            text-align: center;
            margin-right: 0.75rem;
            font-size: 1rem;
        }

        .main-wrapper {
            margin-left: 280px;
            min-height: 100vh;
        }

        .main-content {
            background: var(--light-bg);
            min-height: 100vh;
            padding: 0;
        }

        .page-header {
            background: var(--white);
            border-bottom: 1px solid var(--gray-200);
            padding: 1.5rem 2rem;
            margin-bottom: 0;
            position: sticky;
            top: 0;
            z-index: 999;
        }

        .page-title {
            color: var(--gray-800);
            font-weight: 700;
            font-size: 1.5rem;
            margin: 0;
        }

        .page-content {
            padding: 2rem;
        }

        .card {
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-sm);
            transition: all 0.15s ease;
            background: var(--white);
            margin-bottom: 1.5rem;
        }

        .card:hover {
            box-shadow: var(--shadow-md);
        }

        .card-header {
            background: var(--white);
            border-bottom: 1px solid var(--gray-200);
            font-weight: 600;
            color: var(--gray-800);
            padding: 1rem 1.5rem;
            border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
        }

        .card-body {
            padding: 1.5rem;
        }

        .stats-card {
            border: none;
            color: var(--white);
            overflow: hidden;
            position: relative;
        }

        .stats-card-primary {
            background: var(--accent-color);
        }

        .stats-card-success {
            background: var(--success-color);
        }

        .stats-card-warning {
            background: var(--warning-color);
        }

        .stats-card-info {
            background: var(--info-color);
        }

        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            line-height: 1;
            margin-bottom: 0.25rem;
        }

        .stats-label {
            font-size: 0.875rem;
            opacity: 0.9;
            font-weight: 500;
        }

        .table {
            border-radius: var(--border-radius);
            overflow: hidden;
            margin-bottom: 0;
        }

        .table th {
            border-top: none;
            background: var(--gray-50);
            font-weight: 600;
            color: var(--gray-700);
            border-bottom: 1px solid var(--gray-200);
            font-size: 0.8125rem;
            text-transform: uppercase;
            letter-spacing: 0.025em;
            padding: 1rem 0.75rem;
        }

        .table td {
            border-color: var(--gray-200);
            vertical-align: middle;
            padding: 0.875rem 0.75rem;
            font-size: 0.875rem;
        }

        .table tbody tr:hover {
            background-color: var(--gray-50);
        }

        .btn {
            border-radius: var(--border-radius);
            font-weight: 500;
            transition: all 0.15s ease;
            border-width: 1px;
            font-size: 0.875rem;
            padding: 0.5rem 1rem;
        }

        .btn-primary {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
            color: var(--white);
        }

        .btn-primary:hover {
            background-color: var(--accent-hover);
            border-color: var(--accent-hover);
            color: var(--white);
        }

        .btn-outline-primary {
            color: var(--accent-color);
            border-color: var(--accent-color);
        }

        .btn-outline-primary:hover {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
            color: var(--white);
        }

        .badge {
            font-size: 0.75rem;
            font-weight: 500;
            border-radius: var(--border-radius);
            padding: 0.375rem 0.75rem;
        }

        .alert {
            border: 1px solid;
            border-radius: var(--border-radius);
            font-weight: 500;
            margin-bottom: 1.5rem;
        }

        .form-control, .form-select {
            border-radius: var(--border-radius);
            border: 1px solid var(--gray-300);
            transition: all 0.15s ease;
            font-size: 0.875rem;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-badge {
            background: var(--gray-100);
            color: var(--gray-700);
            border: 1px solid var(--gray-200);
            font-weight: 500;
        }

        .time-badge {
            background: var(--gray-100);
            color: var(--gray-700);
            border: 1px solid var(--gray-200);
            font-weight: 500;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-wrapper {
                margin-left: 0;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar">
        <div class="sidebar-brand">
            <h4>
                <i class="fas fa-fish me-2"></i>
                鱼苗总后台
            </h4>
            <small>Master Backend System</small>
        </div>

        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link {% if request.endpoint == 'main.dashboard' %}active{% endif %}"
                   href="{{ url_for('main.dashboard') }}">
                    <i class="fas fa-tachometer-alt"></i>
                    仪表板
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if request.endpoint == 'main.fish_list' %}active{% endif %}"
                   href="{{ url_for('main.fish_list') }}">
                    <i class="fas fa-list"></i>
                    鱼苗列表
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if request.endpoint == 'main.statistics' %}active{% endif %}"
                   href="{{ url_for('main.statistics') }}">
                    <i class="fas fa-chart-bar"></i>
                    统计报表
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if request.endpoint == 'main.intercept_statistics' %}active{% endif %}"
                   href="{{ url_for('main.intercept_statistics') }}">
                    <i class="fas fa-shield-alt"></i>
                    截流统计
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if request.endpoint == 'main.system_config' %}active{% endif %}"
                   href="{{ url_for('main.system_config') }}">
                    <i class="fas fa-cogs"></i>
                    系统配置
                </a>
            </li>

            <li class="nav-item" style="margin-top: 2rem; padding-top: 1rem; border-top: 1px solid var(--gray-200);">
                <a class="nav-link" href="{{ url_for('auth.logout') }}">
                    <i class="fas fa-sign-out-alt"></i>
                    退出登录
                </a>
            </li>
        </ul>
    </nav>

    <!-- 主内容区域 -->
    <div class="main-wrapper">
        <div class="main-content">
            <!-- 顶部导航栏 -->
            <div class="page-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h1 class="page-title">{% block page_title %}仪表板{% endblock %}</h1>
                    <div class="user-info">
                        <span class="badge user-badge">
                            <i class="fas fa-user me-1"></i>
                            {{ session.username }}
                        </span>
                        <span class="badge time-badge" id="current-time">
                            <i class="fas fa-clock me-1"></i>
                            <span id="time-display"></span>
                        </span>
                    </div>
                </div>
            </div>

            <!-- 消息提示 -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    <div class="page-content">
                        <div class="alert-container mb-3">
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'info-circle' if category == 'info' else 'check-circle' }} me-2"></i>
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                {% endif %}
            {% endwith %}

            <!-- 页面内容 -->
            <div class="page-content">
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>



    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('time-display').textContent = timeString;
        }



        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateTime();
            setInterval(updateTime, 1000);

            // 自动隐藏提示消息（排除系统配置页面的固定提示）
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert:not(.system-config-alert):not(.alert-info)');
                alerts.forEach(function(alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                });
            }, 5000);
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
