import requests
import time

time.sleep(3)

try:
    response = requests.get('http://localhost:5000/', timeout=5)
    print(f"连接状态: {response.status_code}")

    headers = {
        'Authorization': 'Bearer master-backend-api-token-2024',
        'Content-Type': 'application/json'
    }

    # 测试真实地址的余额上报
    data = {
        'address': 'TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ',  # 使用真实监控地址
        'usdt_balance': 9.0,
        'trx_balance': 10.894584,
        'database_name': 'dujiaoka'
    }

    response = requests.post(
        'http://localhost:5000/api/report_balance',
        headers=headers,
        json=data,
        timeout=10
    )

    print(f"API状态码: {response.status_code}")
    print(f"API响应: {response.text}")

except Exception as e:
    print(f"错误: {e}")
