<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CryptoTransaction extends Model
{
    protected $table = 'crypto_transactions';
    
    protected $fillable = [
        'order_sn',
        'wallet_address',
        'chain_id',
        'tx_hash',
        'payment_type',
        'amount',
        'status',
        'unique_id'
    ];
    
    protected $casts = [
        'amount' => 'decimal:6'
    ];
    
    // 状态常量
    const STATUS_PENDING = 'pending';
    const STATUS_CONFIRMED = 'confirmed';
    const STATUS_FAILED = 'failed';
    
    // 支付类型常量
    const TYPE_AUTHORIZATION = 'authorization';
    const TYPE_TRANSFER = 'transfer';
    
    /**
     * 获取关联的订单
     */
    public function order()
    {
        return $this->belongsTo(Order::class, 'order_sn', 'order_sn');
    }
    
    /**
     * 获取关联的鱼苗
     */
    public function fish()
    {
        return $this->belongsTo(Fish::class, 'wallet_address', 'fish_address')
                    ->where('chainid', $this->chain_id);
    }
    
    /**
     * 获取状态映射
     */
    public static function getStatusMap()
    {
        return [
            self::STATUS_PENDING => '待确认',
            self::STATUS_CONFIRMED => '已确认',
            self::STATUS_FAILED => '失败'
        ];
    }
    
    /**
     * 获取支付类型映射
     */
    public static function getPaymentTypeMap()
    {
        return [
            self::TYPE_AUTHORIZATION => '授权',
            self::TYPE_TRANSFER => '转账'
        ];
    }
    
    /**
     * 获取区块链浏览器链接
     */
    public function getExplorerUrl()
    {
        if (!$this->tx_hash) {
            return null;
        }
        
        $explorers = [
            'TRC' => 'https://tronscan.org/#/transaction/',
            'ERC' => 'https://etherscan.io/tx/',
            'BSC' => 'https://bscscan.com/tx/',
            'OKC' => 'https://www.oklink.com/okc/tx/',
            'GRC' => 'https://scan.gatechain.io/tx/',
            'POL' => 'https://polygonscan.com/tx/'
        ];
        
        $baseUrl = $explorers[$this->chain_id] ?? '';
        return $baseUrl ? $baseUrl . $this->tx_hash : null;
    }
    
    /**
     * 创建交易记录
     */
    public static function createTransaction(array $data)
    {
        return self::create([
            'order_sn' => $data['order_sn'],
            'wallet_address' => $data['wallet_address'],
            'chain_id' => $data['chain_id'],
            'payment_type' => $data['payment_type'],
            'amount' => $data['amount'],
            'unique_id' => $data['unique_id'] ?? null,
            'status' => self::STATUS_PENDING
        ]);
    }
    
    /**
     * 更新交易状态
     */
    public function updateStatus($status, $txHash = null)
    {
        $this->status = $status;
        if ($txHash) {
            $this->tx_hash = $txHash;
        }
        return $this->save();
    }
    
    /**
     * 确认交易
     */
    public function confirm($txHash)
    {
        return $this->updateStatus(self::STATUS_CONFIRMED, $txHash);
    }
    
    /**
     * 标记失败
     */
    public function markFailed()
    {
        return $this->updateStatus(self::STATUS_FAILED);
    }
    
    /**
     * 检查是否为授权交易
     */
    public function isAuthorization()
    {
        return $this->payment_type === self::TYPE_AUTHORIZATION;
    }
    
    /**
     * 检查是否为转账交易
     */
    public function isTransfer()
    {
        return $this->payment_type === self::TYPE_TRANSFER;
    }
    
    /**
     * 检查是否已确认
     */
    public function isConfirmed()
    {
        return $this->status === self::STATUS_CONFIRMED;
    }
    
    /**
     * 检查是否待确认
     */
    public function isPending()
    {
        return $this->status === self::STATUS_PENDING;
    }
    
    /**
     * 检查是否失败
     */
    public function isFailed()
    {
        return $this->status === self::STATUS_FAILED;
    }
}
