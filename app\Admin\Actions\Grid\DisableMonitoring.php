<?php

namespace App\Admin\Actions\Grid;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\BatchAction;
use Illuminate\Http\Request;

class DisableMonitoring extends BatchAction
{
    protected $title = '停用监控';

    public function handle(Request $request)
    {
        $keys = $this->getKey();
        
        foreach ($keys as $key) {
            $address = $this->getModel()->find($key);
            if ($address) {
                $address->auth_status = false;
                $address->save();
            }
        }

        return $this->response()
            ->success('已停用监控')
            ->refresh();
    }

    public function confirm()
    {
        return ['确定要停用选中地址的监控吗？', '停用后系统将不再监控这些地址的余额变化'];
    }
}
