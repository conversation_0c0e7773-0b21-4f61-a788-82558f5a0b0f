{% extends "base.html" %}

{% block title %}截流统计 - 鱼苗总后台管理系统{% endblock %}
{% block page_title %}截流统计{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 统计概览 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100 intercept-card intercept-card-primary">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-2">
                                <div class="icon-circle bg-primary bg-opacity-10 me-3">
                                    <i class="fas fa-shield-alt text-primary"></i>
                                </div>
                                <h6 class="text-muted mb-0 fw-normal">总截流次数</h6>
                            </div>
                            <div class="d-flex align-items-end">
                                <h2 class="mb-0 fw-bold text-dark">{{ total_intercept_count }}</h2>
                                <span class="text-muted ms-2 small">次</span>
                            </div>
                            <div class="mt-2">
                                <small class="text-success">
                                    <i class="fas fa-arrow-up me-1"></i>
                                    系统自动执行
                                </small>
                            </div>
                        </div>
                        <div class="chart-mini">
                            <div class="progress-circle" data-percentage="85">
                                <svg width="60" height="60">
                                    <circle cx="30" cy="30" r="25" fill="none" stroke="#e9ecef" stroke-width="4"/>
                                    <circle cx="30" cy="30" r="25" fill="none" stroke="#0d6efd" stroke-width="4"
                                            stroke-dasharray="157" stroke-dashoffset="23.55" stroke-linecap="round"/>
                                </svg>
                                <div class="progress-text">85%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100 intercept-card intercept-card-warning">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-2">
                                <div class="icon-circle bg-warning bg-opacity-10 me-3">
                                    <i class="fas fa-coins text-warning"></i>
                                </div>
                                <h6 class="text-muted mb-0 fw-normal">总截流金额</h6>
                            </div>
                            <div class="d-flex align-items-end">
                                <h2 class="mb-0 fw-bold text-dark">{{ "%.2f"|format(total_intercept_amount) }}</h2>
                                <span class="text-muted ms-2 small">USDT</span>
                            </div>
                            <div class="mt-2">
                                <small class="text-warning">
                                    <i class="fas fa-chart-line me-1"></i>
                                    累计收益
                                </small>
                            </div>
                        </div>
                        <div class="trend-chart">
                            <svg width="60" height="40" viewBox="0 0 60 40">
                                <polyline fill="none" stroke="#ffc107" stroke-width="2"
                                         points="0,35 10,30 20,25 30,20 40,15 50,10 60,5"/>
                                <circle cx="60" cy="5" r="3" fill="#ffc107"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100 intercept-card intercept-card-success">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-2">
                                <div class="icon-circle bg-success bg-opacity-10 me-3">
                                    <i class="fas fa-database text-success"></i>
                                </div>
                                <h6 class="text-muted mb-0 fw-normal">涉及数据库</h6>
                            </div>
                            <div class="d-flex align-items-end">
                                <h2 class="mb-0 fw-bold text-dark">{{ database_stats|length }}</h2>
                                <span class="text-muted ms-2 small">个</span>
                            </div>
                            <div class="mt-2">
                                <small class="text-success">
                                    <i class="fas fa-server me-1"></i>
                                    多库分布
                                </small>
                            </div>
                        </div>
                        <div class="database-visual">
                            <div class="d-flex flex-column">
                                {% for i in range(min(4, database_stats|length)) %}
                                <div class="db-bar mb-1" style="width: {{ 40 + (i * 5) }}px; height: 4px; background: #198754; opacity: {{ 1 - (i * 0.2) }};"></div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100 intercept-card intercept-card-info">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-2">
                                <div class="icon-circle bg-info bg-opacity-10 me-3">
                                    <i class="fas fa-calculator text-info"></i>
                                </div>
                                <h6 class="text-muted mb-0 fw-normal">平均截流金额</h6>
                            </div>
                            <div class="d-flex align-items-end">
                                <h2 class="mb-0 fw-bold text-dark">{{ "%.2f"|format(total_intercept_amount / total_intercept_count if total_intercept_count > 0 else 0) }}</h2>
                                <span class="text-muted ms-2 small">USDT</span>
                            </div>
                            <div class="mt-2">
                                <small class="text-info">
                                    <i class="fas fa-balance-scale me-1"></i>
                                    单次平均
                                </small>
                            </div>
                        </div>
                        <div class="avg-indicator">
                            <div class="circular-progress" data-value="{{ (total_intercept_amount / total_intercept_count if total_intercept_count > 0 else 0) | round(0) }}">
                                <div class="circle">
                                    <div class="mask full">
                                        <div class="fill"></div>
                                    </div>
                                    <div class="mask half">
                                        <div class="fill"></div>
                                    </div>
                                    <div class="inside-circle">
                                        <span class="percentage">{{ "%.0f"|format(total_intercept_amount / total_intercept_count if total_intercept_count > 0 else 0) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 按数据库分组统计 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-database me-2"></i>
                        按数据库分组统计
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>数据库名称</th>
                                    <th>截流次数</th>
                                    <th>截流金额 (USDT)</th>
                                    <th>成功次数</th>
                                    <th>失败次数</th>
                                    <th>成功率</th>
                                    <th>平均金额</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for db_name, stats in database_stats.items() %}
                                <tr>
                                    <td>
                                        <span class="badge bg-primary">{{ db_name }}</span>
                                    </td>
                                    <td>{{ stats.count }}</td>
                                    <td class="text-warning fw-bold">{{ "%.6f"|format(stats.total_amount) }}</td>
                                    <td class="text-success">{{ stats.success_count }}</td>
                                    <td class="text-danger">{{ stats.failed_count }}</td>
                                    <td>
                                        {% set success_rate = (stats.success_count / stats.count * 100) if stats.count > 0 else 0 %}
                                        <span class="badge bg-{{ 'success' if success_rate >= 90 else 'warning' if success_rate >= 70 else 'danger' }}">
                                            {{ "%.1f"|format(success_rate) }}%
                                        </span>
                                    </td>
                                    <td>{{ "%.6f"|format(stats.total_amount / stats.count if stats.count > 0 else 0) }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 最近截流记录 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>
                        最近截流记录
                    </h5>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="refreshInterceptData()">
                        <i class="fas fa-sync-alt me-1"></i>
                        刷新数据
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>源地址</th>
                                    <th>目标地址</th>
                                    <th>金额 (USDT)</th>
                                    <th>状态</th>
                                    <th>数据库</th>
                                    <th>交易哈希</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transfer in recent_intercepts %}
                                <tr>
                                    <td class="text-muted">
                                        {{ transfer.created_at|datetime }}
                                    </td>
                                    <td>
                                        <code class="text-primary">{{ transfer.from_address[:10] }}...{{ transfer.from_address[-6:] }}</code>
                                    </td>
                                    <td>
                                        <code class="text-success">{{ transfer.to_address[:10] }}...{{ transfer.to_address[-6:] }}</code>
                                    </td>
                                    <td class="text-warning fw-bold">{{ "%.6f"|format(transfer.amount) }}</td>
                                    <td>
                                        {% if transfer.status == 'success' %}
                                            <span class="badge bg-success">成功</span>
                                        {% elif transfer.status == 'failed' %}
                                            <span class="badge bg-danger">失败</span>
                                        {% elif transfer.status == 'pending' %}
                                            <span class="badge bg-warning">处理中</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ transfer.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ transfer.fish_address.database_name }}</span>
                                    </td>
                                    <td>
                                        {% if transfer.tx_hash %}
                                            <a href="https://tronscan.org/#/transaction/{{ transfer.tx_hash }}" 
                                               target="_blank" class="text-decoration-none">
                                                <code class="text-muted">{{ transfer.tx_hash[:10] }}...</code>
                                                <i class="fas fa-external-link-alt ms-1"></i>
                                            </a>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    {% if not recent_intercepts %}
                    <div class="text-center py-4">
                        <i class="fas fa-shield-alt fa-3x text-muted mb-3"></i>
                        <p class="text-muted">暂无截流记录</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>


</div>

<!-- 自定义样式 -->
<style>
/* 截流统计卡片样式 */
.intercept-card {
    transition: all 0.3s ease;
    border-radius: 12px;
    overflow: hidden;
    position: relative;
}

.intercept-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.intercept-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    z-index: 1;
}

.intercept-card-primary::before {
    background: linear-gradient(90deg, #0d6efd, #6610f2);
}

.intercept-card-warning::before {
    background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.intercept-card-success::before {
    background: linear-gradient(90deg, #198754, #20c997);
}

.intercept-card-info::before {
    background: linear-gradient(90deg, #0dcaf0, #6f42c1);
}

.icon-circle {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.progress-circle {
    position: relative;
    width: 60px;
    height: 60px;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    font-weight: bold;
    color: #0d6efd;
}

.trend-chart {
    opacity: 0.8;
}

.database-visual {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60px;
}

.db-bar {
    border-radius: 2px;
    transition: all 0.3s ease;
}

.intercept-card:hover .db-bar {
    transform: scaleX(1.1);
}

.circular-progress {
    width: 50px;
    height: 50px;
    position: relative;
}

.circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #f8f9fa;
    position: relative;
}

.circle .mask,
.circle .fill {
    width: 50px;
    height: 50px;
    position: absolute;
    border-radius: 50%;
}

.circle .mask {
    clip: rect(0px, 50px, 50px, 25px);
}

.circle .mask .fill {
    clip: rect(0px, 25px, 50px, 0px);
    background-color: #0dcaf0;
}

.circle .mask.full,
.circle .mask.full .fill {
    animation: fill ease-in-out 3s;
    transform: rotate(126deg);
}

.circle .mask.half .fill {
    animation: fill ease-in-out 3s;
    transform: rotate(126deg);
}

.inside-circle {
    width: 38px;
    height: 38px;
    border-radius: 50%;
    background: white;
    position: absolute;
    top: 6px;
    left: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.percentage {
    font-size: 10px;
    font-weight: bold;
    color: #0dcaf0;
}

@keyframes fill {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(126deg);
    }
}

/* 响应式优化 */
@media (max-width: 768px) {
    .intercept-card .card-body {
        padding: 1.5rem !important;
    }

    .icon-circle {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .progress-circle,
    .trend-chart,
    .database-visual,
    .circular-progress {
        display: none;
    }
}

/* 卡片动画效果 */
.intercept-card {
    animation: fadeInUp 0.6s ease-out;
}

.intercept-card:nth-child(1) { animation-delay: 0.1s; }
.intercept-card:nth-child(2) { animation-delay: 0.2s; }
.intercept-card:nth-child(3) { animation-delay: 0.3s; }
.intercept-card:nth-child(4) { animation-delay: 0.4s; }

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<!-- JavaScript -->
<script>
function refreshInterceptData() {
    // 刷新页面数据
    location.reload();
}

// 自动刷新数据（每5分钟）
setInterval(function() {
    // 可以通过AJAX获取最新数据而不刷新整个页面
    console.log('自动检查截流数据更新...');
}, 300000); // 5分钟

// 初始化动画效果
document.addEventListener('DOMContentLoaded', function() {
    // 为卡片添加悬停效果
    const cards = document.querySelectorAll('.intercept-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = (index * 0.1) + 's';

        // 添加点击波纹效果
        card.addEventListener('click', function(e) {
            const ripple = document.createElement('div');
            const rect = card.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255,255,255,0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
                z-index: 10;
            `;

            card.style.position = 'relative';
            card.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
});

// 添加波纹动画
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
