/* 鱼苗总后台管理系统自定义样式 */

/* 全局样式 */
:root {
    --primary-color: #1f2937;
    --primary-light: #374151;
    --secondary-color: #6b7280;
    --accent-color: #3b82f6;
    --accent-hover: #2563eb;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    --light-bg: #f9fafb;
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --transition: all 0.15s ease;
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 页面加载动画 */
.fade-in {
    animation: fadeInUp 0.6s ease-out;
}

.slide-in {
    animation: slideInLeft 0.5s ease-out;
}

/* 卡片悬停效果 */
.card-hover {
    transition: var(--transition);
    cursor: pointer;
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* 统计卡片特殊效果 */
.stats-card {
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.stats-card:hover::before {
    left: 100%;
}

/* 按钮特效 */
.btn-modern {
    background: var(--accent-color);
    border: 1px solid var(--accent-color);
    color: var(--white);
    transition: var(--transition);
    border-radius: var(--border-radius);
    font-weight: 500;
}

.btn-modern:hover {
    background: var(--accent-hover);
    border-color: var(--accent-hover);
    color: var(--white);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* 表格样式增强 */
.table-enhanced {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
}

.table-enhanced thead th {
    background: var(--gray-50);
    color: var(--gray-700);
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8125rem;
    letter-spacing: 0.025em;
}

.table-enhanced tbody tr {
    transition: var(--transition);
}

.table-enhanced tbody tr:hover {
    background-color: var(--gray-50);
}

/* 徽章样式增强 */
.badge-enhanced {
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.7rem;
    letter-spacing: 0.5px;
}

.badge-pulse {
    animation: pulse 2s infinite;
}

/* 进度条样式 */
.progress-enhanced {
    height: 8px;
    border-radius: 10px;
    background-color: rgba(0,0,0,0.1);
    overflow: hidden;
}

.progress-enhanced .progress-bar {
    background: var(--primary-gradient);
    border-radius: 10px;
    transition: width 0.6s ease;
}

/* 输入框增强 */
.form-control-enhanced {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.form-control-enhanced:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: translateY(-1px);
}

/* 侧边栏增强 */
.sidebar-enhanced {
    background: var(--primary-gradient);
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.sidebar-enhanced .nav-link {
    border-radius: 10px;
    margin: 0.25rem 0;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.sidebar-enhanced .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.1);
    transition: left 0.3s;
}

.sidebar-enhanced .nav-link:hover::before {
    left: 0;
}

.sidebar-enhanced .nav-link.active {
    background: rgba(255,255,255,0.2);
    transform: translateX(5px);
}

/* 模态框增强 */
.modal-enhanced .modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-enhanced .modal-header {
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

/* 提示框增强 */
.alert-enhanced {
    border: none;
    border-radius: var(--border-radius);
    border-left: 4px solid;
    box-shadow: var(--box-shadow);
}

.alert-enhanced.alert-info {
    border-left-color: #17a2b8;
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1), rgba(23, 162, 184, 0.05));
}

.alert-enhanced.alert-success {
    border-left-color: #28a745;
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.05));
}

.alert-enhanced.alert-warning {
    border-left-color: #ffc107;
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05));
}

.alert-enhanced.alert-danger {
    border-left-color: #dc3545;
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(220, 53, 69, 0.05));
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 响应式增强 */
@media (max-width: 768px) {
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        border-radius: var(--border-radius);
    }
    
    .btn-group-vertical .btn {
        margin-bottom: 0.5rem;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #1a1a1a;
        --text-color: #ffffff;
        --card-bg: #2d2d2d;
    }
    
    body {
        background-color: var(--bg-color);
        color: var(--text-color);
    }
    
    .card {
        background-color: var(--card-bg);
        border-color: #404040;
    }
    
    .table {
        color: var(--text-color);
    }
    
    .form-control {
        background-color: var(--card-bg);
        border-color: #404040;
        color: var(--text-color);
    }
}

/* 打印样式 */
@media print {
    .sidebar,
    .btn,
    .alert {
        display: none !important;
    }
    
    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
}
