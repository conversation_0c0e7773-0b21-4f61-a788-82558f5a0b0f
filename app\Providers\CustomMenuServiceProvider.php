<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Dcat\Admin\Admin;

class CustomMenuServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // 在后台启动时隐藏系统菜单
        Admin::booting(function () {
            // 方法1：通过CSS隐藏系统菜单
            Admin::style('
                /* 隐藏系统菜单 */
                .sidebar-menu li[data-id="2"] {
                    display: none !important;
                }
                
                /* 或者通过菜单标题隐藏 */
                .sidebar-menu li:has(span:contains("系统")) {
                    display: none !important;
                }
            ');
        });
    }
}
