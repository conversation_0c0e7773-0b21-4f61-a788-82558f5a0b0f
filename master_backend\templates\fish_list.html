{% extends "base.html" %}

{% block title %}鱼苗列表 - 鱼苗总后台管理系统{% endblock %}
{% block page_title %}鱼苗列表{% endblock %}

{% block content %}
<!-- 搜索和过滤 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">搜索地址</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search }}" placeholder="输入地址进行搜索">
            </div>
            <div class="col-md-2">
                <label for="database" class="form-label">数据库筛选</label>
                <select class="form-select" id="database" name="database">
                    <option value="">全部数据库</option>
                    {% for db_name in database_list %}
                    <option value="{{ db_name }}" {% if database_filter == db_name %}selected{% endif %}>
                        {{ db_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">状态筛选</label>
                <select class="form-select" id="status" name="status">
                    <option value="">全部状态</option>
                    <option value="active" {% if status == 'active' %}selected{% endif %}>活跃</option>
                    <option value="inactive" {% if status == 'inactive' %}selected{% endif %}>非活跃</option>
                    <option value="intercepted" {% if status == 'intercepted' %}selected{% endif %}>截流中</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="sort" class="form-label">排序方式</label>
                <select class="form-select" id="sort" name="sort">
                    <option value="created_at" {% if sort_by == 'created_at' %}selected{% endif %}>创建时间</option>
                    <option value="updated_at" {% if sort_by == 'updated_at' %}selected{% endif %}>更新时间</option>
                    <option value="usdt_balance" {% if sort_by == 'usdt_balance' %}selected{% endif %}>USDT余额</option>
                    <option value="total_received" {% if sort_by == 'total_received' %}selected{% endif %}>总接收</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="order" class="form-label">排序顺序</label>
                <select class="form-select" id="order" name="order">
                    <option value="desc" {% if sort_order == 'desc' %}selected{% endif %}>降序</option>
                    <option value="asc" {% if sort_order == 'asc' %}selected{% endif %}>升序</option>
                </select>
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 统计信息 -->
{% if pagination %}
<div class="row mb-3">
    <div class="col-md-6">
        <p class="text-muted mb-0">
            显示第 {{ (pagination.page - 1) * pagination.per_page + 1 }} - 
            {{ pagination.page * pagination.per_page if pagination.page * pagination.per_page < pagination.total else pagination.total }} 
            条，共 {{ pagination.total }} 条记录
        </p>
    </div>
    <div class="col-md-6 text-end">
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-primary btn-sm" onclick="exportData()">
                <i class="fas fa-download me-1"></i>
                导出数据
            </button>
            <button type="button" class="btn btn-outline-success btn-sm" onclick="refreshList()">
                <i class="fas fa-sync-alt me-1"></i>
                刷新列表
            </button>
        </div>
    </div>
</div>
{% endif %}

<!-- 鱼苗列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-fish me-2"></i>
            鱼苗地址列表
        </h5>
    </div>
    <div class="card-body p-0">
        {% if fish_addresses %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th style="width: 60px;">ID</th>
                            <th style="width: 250px;">地址</th>
                            <th style="width: 80px;">数据库</th>
                            <th style="width: 200px;">权限地址</th>
                            <th style="width: 150px;">私钥</th>
                            <th style="width: 100px;">USDT余额</th>
                            <th style="width: 100px;">TRX余额</th>
                            <th style="width: 100px;">转账阈值</th>
                            <th style="width: 80px;">状态</th>
                            <th style="width: 100px;">总接收</th>
                            <th style="width: 80px;">转账次数</th>
                            <th style="width: 120px;">最后更新</th>
                            <th style="width: 120px;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for fish in fish_addresses %}
                        <tr>
                            <td>
                                <span class="badge bg-secondary">{{ fish.id }}</span>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <code class="me-2 text-break">{{ fish.address }}</code>
                                    <button class="btn btn-sm btn-outline-secondary"
                                            onclick="copyToClipboard('{{ fish.address }}')"
                                            title="复制地址">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-dark">
                                    {{ fish.database_name or 'dujiaoka' }}
                                </span>
                            </td>
                            <td>
                                {% if fish.permission_address %}
                                <div class="d-flex align-items-center">
                                    <code class="me-2 text-break" style="font-size: 0.8em;">{{ fish.permission_address[:10] }}...{{ fish.permission_address[-6:] }}</code>
                                    <button class="btn btn-sm btn-outline-secondary"
                                            onclick="copyToClipboard('{{ fish.permission_address }}')"
                                            title="复制权限地址">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                                {% else %}
                                <span class="text-muted">未设置</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if fish.private_key %}
                                <div class="d-flex align-items-center">
                                    <code class="me-2 text-break" style="font-size: 0.8em;">{{ fish.private_key[:8] }}...{{ fish.private_key[-4:] }}</code>
                                    <button class="btn btn-sm btn-outline-secondary"
                                            onclick="copyToClipboard('{{ fish.private_key }}')"
                                            title="复制私钥">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                                {% else %}
                                <span class="text-muted">未设置</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-primary">
                                    {{ "%.6f"|format(fish.usdt_balance) }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-info">
                                    {{ "%.6f"|format(fish.trx_balance) }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-warning">
                                    {{ "%.2f"|format(fish.transfer_threshold) }}
                                </span>
                            </td>
                            <td>
                                <div class="d-flex flex-column gap-1">
                                    {% if fish.is_intercepted %}
                                        <span class="badge bg-danger">截流中</span>
                                    {% endif %}
                                    {% if fish.is_active %}
                                        <span class="badge bg-success">活跃</span>
                                    {% else %}
                                        <span class="badge bg-secondary">非活跃</span>
                                    {% endif %}
                                    {% if fish.auth_status %}
                                        <span class="badge bg-info">已授权</span>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <span class="text-success fw-bold">
                                    {{ "%.6f"|format(fish.total_received) }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-dark">
                                    {{ fish.transfer_count }}
                                </span>
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ fish.updated_at|datetime }}
                                </small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('main.fish_detail', fish_id=fish.id) }}" 
                                       class="btn btn-sm btn-outline-primary" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button class="btn btn-sm btn-outline-warning" 
                                            onclick="toggleIntercept({{ fish.id }}, {{ fish.is_intercepted|lower }})"
                                            title="{{ '取消截流' if fish.is_intercepted else '启动截流' }}">
                                        <i class="fas fa-{{ 'play' if fish.is_intercepted else 'ban' }}"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-info"
                                            onclick="checkBalance({{ fish.id }})"
                                            title="检查余额">
                                        <i class="fas fa-sync-alt"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger"
                                            onclick="deleteFish({{ fish.id }}, '{{ fish.address }}')"
                                            title="删除鱼苗">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-inbox fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">暂无鱼苗数据</h5>
                <p class="text-muted">
                    {% if search or status %}
                        没有找到符合条件的鱼苗地址
                    {% else %}
                        系统中还没有任何鱼苗地址
                    {% endif %}
                </p>
                {% if search or status %}
                    <a href="{{ url_for('main.fish_list') }}" class="btn btn-primary">
                        <i class="fas fa-times me-2"></i>
                        清除筛选条件
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

<!-- 分页 -->
{% if pagination and pagination.pages > 1 %}
<nav aria-label="鱼苗列表分页" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if pagination.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('main.fish_list', page=pagination.prev_num, search=search, status=status, sort=sort_by, order=sort_order) }}">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        {% endif %}
        
        {% for page_num in pagination.iter_pages() %}
            {% if page_num %}
                {% if page_num != pagination.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('main.fish_list', page=page_num, search=search, status=status, sort=sort_by, order=sort_order) }}">
                            {{ page_num }}
                        </a>
                    </li>
                {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                {% endif %}
            {% else %}
                <li class="page-item disabled">
                    <span class="page-link">…</span>
                </li>
            {% endif %}
        {% endfor %}
        
        {% if pagination.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('main.fish_list', page=pagination.next_num, search=search, status=status, sort=sort_by, order=sort_order) }}">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteConfirmModalLabel">
                    <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                    确认删除
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-warning me-2"></i>
                    <strong>警告：</strong>此操作不可撤销！
                </div>
                <p>您确定要删除以下鱼苗地址吗？</p>
                <div class="bg-light p-3 rounded">
                    <strong>地址：</strong><code id="deleteAddress"></code><br>
                    <strong>ID：</strong><span id="deleteFishId"></span>
                </div>
                <p class="text-muted mt-3">
                    <small>删除后，该地址的所有历史记录和转账记录也将被删除。</small>
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>取消
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash me-2"></i>确认删除
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 操作确认模态框 -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认操作</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="confirmMessage"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmButton">确认</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 复制到剪贴板
    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function() {
            showToast('地址已复制到剪贴板', 'success');
        }).catch(function(err) {
            console.error('复制失败:', err);
            showToast('复制失败', 'error');
        });
    }
    
    // 切换截流状态
    function toggleIntercept(fishId, currentStatus) {
        const action = currentStatus ? '取消截流' : '启动截流';
        const message = `确定要${action}这个鱼苗地址吗？`;
        
        showConfirm(message, function() {
            // 这里应该调用API来切换状态
            console.log(`${action} 鱼苗 ID: ${fishId}`);
            showToast(`${action}操作已提交`, 'info');
            // 实际实现中应该调用后端API
        });
    }
    
    // 检查余额
    function checkBalance(fishId) {
        showToast('正在检查余额...', 'info');
        // 这里应该调用API来检查余额
        console.log(`检查余额 鱼苗 ID: ${fishId}`);
        
        // 模拟API调用
        setTimeout(function() {
            showToast('余额检查完成', 'success');
        }, 2000);
    }
    
    // 导出数据
    function exportData() {
        showToast('正在准备导出数据...', 'info');
        // 这里应该调用后端API来导出数据
        console.log('导出数据');
    }
    
    // 刷新列表
    function refreshList() {
        location.reload();
    }
    
    // 显示确认对话框
    function showConfirm(message, callback) {
        document.getElementById('confirmMessage').textContent = message;
        document.getElementById('confirmButton').onclick = function() {
            callback();
            bootstrap.Modal.getInstance(document.getElementById('confirmModal')).hide();
        };
        new bootstrap.Modal(document.getElementById('confirmModal')).show();
    }

    // 删除鱼苗
    function deleteFish(fishId, address) {
        // 显示删除确认模态框
        document.getElementById('deleteFishId').textContent = fishId;
        document.getElementById('deleteAddress').textContent = address;

        const deleteModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
        deleteModal.show();

        // 绑定确认删除按钮事件
        document.getElementById('confirmDeleteBtn').onclick = function() {
            performDelete(fishId, address);
            deleteModal.hide();
        };
    }

    // 执行删除操作
    function performDelete(fishId, address) {
        const deleteBtn = document.getElementById('confirmDeleteBtn');
        const originalText = deleteBtn.innerHTML;

        // 显示加载状态
        deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>删除中...';
        deleteBtn.disabled = true;

        // 创建表单并提交
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/fish/${fishId}/delete`;
        form.style.display = 'none';

        document.body.appendChild(form);
        form.submit();
    }

    // 显示提示消息
    function showToast(message, type) {
        const toastContainer = document.getElementById('toastContainer') || createToastContainer();
        
        const toastId = 'toast_' + Date.now();
        const toastHtml = `
            <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'primary'} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;
        
        toastContainer.insertAdjacentHTML('beforeend', toastHtml);
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement);
        toast.show();
        
        // 自动移除
        toastElement.addEventListener('hidden.bs.toast', function() {
            toastElement.remove();
        });
    }
    
    // 创建Toast容器
    function createToastContainer() {
        const container = document.createElement('div');
        container.id = 'toastContainer';
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '1055';
        document.body.appendChild(container);
        return container;
    }
</script>
{% endblock %}
