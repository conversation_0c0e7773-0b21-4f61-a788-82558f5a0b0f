<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // 24小时地址监控任务 - 使用现有的monitor_interval配置（毫秒）
        $monitorIntervalMs = \App\Models\Options::getValue('monitor_interval', '300000'); // 默认300000毫秒(5分钟)
        $intervalMinutes = max(1, intval($monitorIntervalMs) / 60000); // 毫秒转换为分钟，最少1分钟

        $schedule->command('monitor:addresses')
                 ->cron('*/' . $intervalMinutes . ' * * * *') // 动态设置cron表达式
                 ->withoutOverlapping()
                 ->runInBackground();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
