<?php
namespace App\Admin\Controllers;

use App\Models\DailiGroup;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Grid;
use Dcat\Admin\Form;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Validator;

class DailiGroupController extends Controller
{
    public function __construct()
    {
        $this->middleware(function ($request, $next) {
            if (!auth('admin')->check()) {
                abort(403, '未授权访问');
            }
            return $next($request);
        });
    }
    
    public function index(Content $content)
    {
        return $content
            ->header('代理群组管理')
            ->description('管理所有代理群组')
            ->body($this->grid());
    }

    protected function grid()
    {
        $grid = new Grid(new DailiGroup());
        
        $grid->model()->orderBy('id', 'desc');
        
        $grid->column('id', 'ID')->sortable();
        $grid->column('groupid', '群组ID')->sortable();
        $grid->column('remark', '群组备注')->sortable();
        $grid->column('profit_sharing', '分润比例')->display(function ($value) {
            return $value . '%';
        })->sortable();
        $grid->column('status', '状态')->switch()->sortable();
        $grid->column('created_at', '创建时间')->sortable();
        $grid->column('updated_at', '更新时间')->sortable();
        
        $grid->quickSearch('groupid', 'remark');
        
        $grid->actions(function (Grid\Displayers\Actions $actions) {
            $actions->disableView();
        });
        
        return $grid;
    }

    public function create(Content $content)
    {
        return $content
            ->header('新增代理群组')
            ->description('创建新的代理群组')
            ->body($this->form());
    }

    public function edit($id, Content $content)
    {
        if (!is_numeric($id) || $id <= 0) {
            abort(400, '无效的ID参数');
        }
        
        return $content
            ->header('编辑代理群组')
            ->description('修改代理群组信息')
            ->body($this->form()->edit($id));
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'groupid' => 'required|string|max:191|unique:daili_group,groupid',
            'remark' => 'required|string|max:191',
            'profit_sharing' => 'required|integer|min:10|max:100',
            'status' => 'required|boolean',
        ], [
            'groupid.required' => '群组ID不能为空',
            'groupid.unique' => '群组ID已存在',
            'remark.required' => '群组备注不能为空',
            'profit_sharing.required' => '分润比例不能为空',
            'profit_sharing.min' => '分润比例不能低于10%',
            'profit_sharing.max' => '分润比例不能超过100%',
        ]);
        
        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }
        
        return $this->form()->store();
    }
    
    public function update($id, Request $request)
    {
        if (!is_numeric($id) || $id <= 0) {
            abort(400, '无效的ID参数');
        }
        
        $validator = Validator::make($request->all(), [
            'groupid' => 'required|string|max:191|unique:daili_group,groupid,' . $id,
            'remark' => 'required|string|max:191',
            'profit_sharing' => 'required|integer|min:10|max:100',
            'status' => 'required|boolean',
        ], [
            'groupid.required' => '群组ID不能为空',
            'groupid.unique' => '群组ID已存在',
            'remark.required' => '群组备注不能为空',
            'profit_sharing.required' => '分润比例不能为空',
            'profit_sharing.min' => '分润比例不能低于10%',
            'profit_sharing.max' => '分润比例不能超过100%',
        ]);
        
        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }
        
        return $this->form()->update($id);
    }

    public function destroy($id)
    {
        if (!is_numeric($id) || $id <= 0) {
            abort(400, '无效的ID参数');
        }
        
        return $this->form()->destroy($id);
    }

    protected function form()
    {
        $form = new Form(new DailiGroup());
        
        $form->text('groupid', '群组ID')
            ->required()
            ->maxLength(191)
            ->help('填写Telegram群组ID');
            
        $form->text('remark', '群组备注')
            ->required()
            ->maxLength(191)
            ->help('填写群组的备注信息');
            
        $form->select('profit_sharing', '分润比例')
            ->options([
                10 => '10%',
                15 => '15%',
                20 => '20%',
                25 => '25%',
                30 => '30%',
                35 => '35%',
                40 => '40%',
                45 => '45%',
                50 => '50%',
                55 => '55%',
                60 => '60%',
                65 => '65%',
                70 => '70%',
                75 => '75%',
                80 => '80%',
                85 => '85%',
                90 => '90%',
                95 => '95%',
                100 => '100%'
            ])
            ->default(50)
            ->required()
            ->help('设置该群组的分润比例');
            
        $form->switch('status', '状态')
            ->default(1)
            ->help('启用或禁用该群组');
        
        $form->tools(function (Form\Tools $tools) {
            $tools->disableView();
        });
        
        return $form;
    }
}
