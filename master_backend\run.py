#!/usr/bin/env python3
"""
鱼苗总后台管理系统启动脚本
"""
import os
import sys
from app import create_app

def main():
    """主函数"""
    print("=" * 60)
    print("🐟 鱼苗总后台管理系统")
    print("=" * 60)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"   当前版本: {sys.version}")
        sys.exit(1)
    
    # 设置环境变量
    os.environ.setdefault('FLASK_ENV', 'development')
    
    # 创建应用
    try:
        app = create_app()
        print("✅ 应用创建成功")
    except Exception as e:
        print(f"❌ 应用创建失败: {e}")
        sys.exit(1)
    
    # 显示启动信息
    print("\n📋 系统信息:")
    print(f"   Python版本: {sys.version.split()[0]}")
    print(f"   Flask环境: {os.environ.get('FLASK_ENV', 'production')}")
    print(f"   数据库: SQLite")
    print(f"   访问地址: http://localhost:5000")
    
    print("\n🔑 登录信息:")
    print(f"   访问码: 5588")
    print(f"   默认账户: admin")
    print(f"   默认密码: admin123")
    
    print("\n🔌 API信息:")
    print(f"   API Token: master-backend-api-token-2024")
    print(f"   API文档: http://localhost:5000/api/")
    
    print("\n" + "=" * 60)
    print("🚀 正在启动服务器...")
    print("   按 Ctrl+C 停止服务")
    print("=" * 60)
    
    try:
        # 启动应用
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n\n👋 服务器已停止")
    except Exception as e:
        print(f"\n❌ 服务器启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
