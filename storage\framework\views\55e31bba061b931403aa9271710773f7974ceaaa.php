<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_','-',strtolower(app()->getLocale())), false); ?>">
<head>
    <?php echo $__env->make('riniba_02.layouts._header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('riniba_02.layouts._css', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
</head>
<body>

    <div class="loader-mask">
        <div class="loader">
            <div></div>
            <div></div>
        </div>
    </div>
    <div class="overlay"></div>
    <div class="side-overlay"></div>
    <div class="progress-wrap">
        <svg class="progress-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
            <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98" />
        </svg>
    </div>

    <?php echo $__env->make('riniba_02.layouts._mobil_header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('riniba_02.layouts._nav_header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <div class="bodycontent">
    <?php echo $__env->yieldContent('content'); ?>
    </div>
    <?php echo $__env->make('riniba_02.layouts._footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

</body>
<?php echo $__env->make('riniba_02.layouts._script', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->startSection('js'); ?>

<?php echo $__env->yieldSection(); ?>

</html><?php /**PATH /mnt/dujiaoka/resources/views/riniba_02/layouts/default.blade.php ENDPATH**/ ?>