#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复require_login错误的脚本
清理Python缓存并确保代码正确
"""

import os
import shutil
import glob

def clean_python_cache():
    """清理Python缓存文件"""
    print("🧹 清理Python缓存文件...")
    
    # 清理__pycache__目录
    pycache_dirs = glob.glob('**/__pycache__', recursive=True)
    for pycache_dir in pycache_dirs:
        if os.path.exists(pycache_dir):
            shutil.rmtree(pycache_dir)
            print(f"   ✅ 删除: {pycache_dir}")
    
    # 清理.pyc文件
    pyc_files = glob.glob('**/*.pyc', recursive=True)
    for pyc_file in pyc_files:
        if os.path.exists(pyc_file):
            os.remove(pyc_file)
            print(f"   ✅ 删除: {pyc_file}")
    
    # 清理.pyo文件
    pyo_files = glob.glob('**/*.pyo', recursive=True)
    for pyo_file in pyo_files:
        if os.path.exists(pyo_file):
            os.remove(pyo_file)
            print(f"   ✅ 删除: {pyo_file}")
    
    print("✅ Python缓存清理完成")

def check_routes_file():
    """检查routes.py文件"""
    print("\n🔍 检查routes.py文件...")
    
    routes_file = 'master_backend/routes.py'
    
    if not os.path.exists(routes_file):
        print(f"❌ 文件不存在: {routes_file}")
        return False
    
    try:
        with open(routes_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有require_login
        if 'require_login' in content:
            print("❌ 发现require_login，需要替换")
            
            # 替换为require_auth
            new_content = content.replace('require_login', 'require_auth')
            
            with open(routes_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("✅ 已替换require_login为require_auth")
        else:
            print("✅ 未发现require_login")
        
        # 检查require_auth装饰器定义
        if 'def require_auth(' in content:
            print("✅ require_auth装饰器定义存在")
        else:
            print("❌ require_auth装饰器定义缺失")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 检查文件失败: {e}")
        return False

def create_restart_script():
    """创建重启脚本"""
    print("\n📝 创建重启脚本...")
    
    restart_script = '''#!/bin/bash
# Master Backend 重启脚本

echo "🔄 重启Master Backend服务..."

# 查找并终止现有进程
echo "🔍 查找现有进程..."
PIDS=$(ps aux | grep "python.*master_backend/run.py" | grep -v grep | awk '{print $2}')

if [ ! -z "$PIDS" ]; then
    echo "🛑 终止现有进程: $PIDS"
    kill -9 $PIDS
    sleep 2
else
    echo "ℹ️ 未发现运行中的进程"
fi

# 清理Python缓存
echo "🧹 清理Python缓存..."
find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name "*.pyc" -delete 2>/dev/null || true
find . -name "*.pyo" -delete 2>/dev/null || true

# 进入目录
cd master_backend

# 启动服务
echo "🚀 启动Master Backend服务..."
nohup python run.py > ../master_backend.log 2>&1 &

echo "✅ 服务已启动，日志文件: master_backend.log"
echo "📋 检查服务状态: ps aux | grep master_backend"
'''
    
    with open('restart_master_backend.sh', 'w') as f:
        f.write(restart_script)
    
    # 添加执行权限
    os.chmod('restart_master_backend.sh', 0o755)
    
    print("✅ 重启脚本已创建: restart_master_backend.sh")

def main():
    """主函数"""
    print("🚀 修复require_login错误")
    print("=" * 50)
    
    # 清理Python缓存
    clean_python_cache()
    
    # 检查routes.py文件
    if check_routes_file():
        print("✅ routes.py文件检查通过")
    else:
        print("❌ routes.py文件检查失败")
        return
    
    # 创建重启脚本
    create_restart_script()
    
    print("\n" + "=" * 50)
    print("🎉 修复完成！")
    print("\n📋 下一步操作:")
    print("1. 在服务器上运行此脚本:")
    print("   python fix_require_login_error.py")
    print("2. 重启Master Backend服务:")
    print("   ./restart_master_backend.sh")
    print("   或手动重启:")
    print("   cd master_backend && python run.py")
    
    print("\n💡 如果问题仍然存在:")
    print("1. 检查服务器上的文件是否已更新")
    print("2. 确认Python环境正确")
    print("3. 查看完整的错误日志")

if __name__ == '__main__':
    main()
