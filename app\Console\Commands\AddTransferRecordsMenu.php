<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Dcat\Admin\Models\Menu;

class AddTransferRecordsMenu extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:add-transfer-menu';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '添加转账记录管理菜单到后台';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('开始添加转账记录管理菜单...');

        try {
            // 1. 查找或创建监控系统主菜单
            $monitoringMenu = Menu::where('title', '监控系统')
                                ->where('parent_id', 0)
                                ->first();

            if (!$monitoringMenu) {
                $this->info('创建监控系统主菜单...');
                $monitoringMenu = Menu::create([
                    'parent_id' => 0,
                    'order' => 8,
                    'title' => '监控系统',
                    'icon' => 'fa-eye',
                    'uri' => '',
                    'show' => 1,
                ]);
                $this->info('✅ 监控系统主菜单创建成功');
            } else {
                $this->info('✅ 监控系统主菜单已存在');
            }

            // 2. 检查转账记录菜单是否已存在
            $transferMenu = Menu::where('title', '转账记录')
                              ->where('parent_id', $monitoringMenu->id)
                              ->first();

            if ($transferMenu) {
                $this->warn('⚠️ 转账记录菜单已存在，跳过创建');
            } else {
                // 3. 创建转账记录菜单
                $this->info('创建转账记录菜单...');
                Menu::create([
                    'parent_id' => $monitoringMenu->id,
                    'order' => 4,
                    'title' => '转账记录',
                    'icon' => 'fa-exchange',
                    'uri' => 'transfer-records',
                    'show' => 1,
                ]);
                $this->info('✅ 转账记录菜单创建成功');
            }

            // 4. 确保其他监控子菜单存在
            $subMenus = [
                [
                    'order' => 1,
                    'title' => '监控仪表板',
                    'icon' => 'fa-dashboard',
                    'uri' => 'monitor-dashboard',
                ],
                [
                    'order' => 2,
                    'title' => '授权记录',
                    'icon' => 'fa-list',
                    'uri' => 'authorizations',
                ],
                [
                    'order' => 3,
                    'title' => '授权地址监控',
                    'icon' => 'fa-desktop',
                    'uri' => 'authorized-addresses',
                ],
            ];

            foreach ($subMenus as $menuData) {
                $existingMenu = Menu::where('title', $menuData['title'])
                                  ->where('parent_id', $monitoringMenu->id)
                                  ->first();

                if (!$existingMenu) {
                    Menu::create([
                        'parent_id' => $monitoringMenu->id,
                        'order' => $menuData['order'],
                        'title' => $menuData['title'],
                        'icon' => $menuData['icon'],
                        'uri' => $menuData['uri'],
                        'show' => 1,
                    ]);
                    $this->info("✅ {$menuData['title']}菜单创建成功");
                }
            }

            // 5. 显示最终的菜单结构
            $this->info("\n📋 监控系统菜单结构:");
            $this->info("📁 监控系统");
            
            $subMenus = Menu::where('parent_id', $monitoringMenu->id)
                          ->orderBy('order')
                          ->get();

            foreach ($subMenus as $menu) {
                $this->info("  📄 {$menu->title} ({$menu->uri})");
            }

            $this->info("\n🎉 转账记录菜单添加完成！");
            $this->info("💡 请刷新后台页面查看新菜单");

            return 0;

        } catch (\Exception $e) {
            $this->error("❌ 添加菜单失败: " . $e->getMessage());
            return 1;
        }
    }
}
