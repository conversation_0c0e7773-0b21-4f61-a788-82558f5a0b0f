<?php
/**
 * The file was created by <PERSON>si<PERSON>.
 *
 * <AUTHOR>
 * @copyright assimon<<EMAIL>>
 * @link      http://utf8.hk/
 */

namespace App\Http\Controllers\Pay;


use App\Exceptions\RuleValidationException;
use App\Http\Controllers\PayController;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Request;

class EpusdtController extends PayController
{
    public function gateway(string $payway, string $orderSN)
    {
        try {
            // 加载网关
            $this->loadGateWay($orderSN, $payway);

            // 使用bill页面模板，像dao项目一样
            $result = [
                'order_sn' => $this->order->order_sn,
                'title' => $this->order->title,
                'goods_price' => $this->order->goods_price,
                'buy_amount' => $this->order->buy_amount,
                'actual_price' => (float)$this->order->actual_price,
                'info' => $this->order->info,
                'coupon' => null,
                'coupon_discount_price' => 0,
                'wholesale_discount_price' => 0
            ];

            return $this->render('static_pages/bill', $result, __('dujiaoka.confirm_order'));

        } catch (RuleValidationException $exception) {
            return $this->err($exception->getMessage());
        } catch (GuzzleException $exception) {
            return $this->err($exception->getMessage());
        }
    }


    private function epusdtSign(array $parameter, string $signKey)
    {
        ksort($parameter);
        reset($parameter); //内部指针指向数组中的第一个元素
        $sign = '';
        $urls = '';
        foreach ($parameter as $key => $val) {
            if ($val == '') continue;
            if ($key != 'signature') {
                if ($sign != '') {
                    $sign .= "&";
                    $urls .= "&";
                }
                $sign .= "$key=$val"; //拼接为url参数形式
                $urls .= "$key=" . urlencode($val); //拼接为url参数形式
            }
        }
        $sign = md5($sign . $signKey);//密码追加进入开始MD5签名
        return $sign;
    }

    public function notifyUrl(Request $request)
    {
        $data = $request->all();
        $order = $this->orderService->detailOrderSN($data['order_id']);
        if (!$order) {
            return 'fail';
        }
        $payGateway = $this->payService->detail($order->pay_id);
        if (!$payGateway) {
            return 'fail';
        }
        if($payGateway->pay_handleroute != 'pay/epusdt'){
            return 'fail';
        }
        $signature = $this->epusdtSign($data, $payGateway->merchant_id);
        if ($data['signature'] != $signature) { //不合法的数据
            return 'fail';  //返回失败 继续补单
        } else {
            //合法的数据
            //业务处理
            $this->orderProcessService->completedOrder($data['order_id'], $data['amount'], $data['trade_id']);
            return 'ok';
        }
    }

    public function returnUrl(Request $request)
    {
        $oid = $request->get('order_id');
        // 异步通知还没到就跳转了，所以这里休眠2秒
        sleep(2);
        return redirect(url('detail-order-sn', ['orderSN' => $oid]));
    }

}
