"""
总后台管理系统配置文件
"""
import os
from datetime import timedelta
import pytz

class Config:
    """基础配置类"""
    
    # 基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'master-backend-secret-key-2024'
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///master_backend.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Session配置
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    
    # 访问码配置（硬编码）
    ACCESS_CODE = '5588'
    
    # API Token配置
    API_TOKEN = 'master-backend-api-token-2024'
    
    # 管理员账户配置（MD5加密）
    # 默认用户名: admin, 密码: admin123
    # MD5('admin') = 21232f297a57a5a743894a0e4a801fc3
    # MD5('admin123') = 0192023a7bbd73250516f069df18b500
    ADMIN_USERS = {
        '21232f297a57a5a743894a0e4a801fc3': '0192023a7bbd73250516f069df18b500'  # admin:admin123
    }
    
    # 转账阈值配置（USDT）
    DEFAULT_TRANSFER_THRESHOLD = 10.0
    
    # 日志配置
    LOG_LEVEL = 'INFO'
    LOG_FILE = 'master_backend.log'
    
    # 分页配置
    ITEMS_PER_PAGE = 20
    
    # 区块链配置
    TRON_GRID_API_KEYS = [
        'd8794150-ec22-4e48-842d-ba26aad66a6d'  # 示例API Key
    ]
    
    # USDT合约地址
    USDT_CONTRACT_ADDRESS = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t'
    
    # 代理配置
    PROXY_ENABLED = True
    PROXY_URL = 'http://127.0.0.1:7891'

    # 时区配置
    TIMEZONE = 'Asia/Shanghai'

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    
class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    
# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
