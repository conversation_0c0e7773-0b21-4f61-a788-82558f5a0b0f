<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\AddressMonitorService;

class MonitorAddresses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'monitor:addresses';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '监控授权地址余额并执行自动转账';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('开始监控授权地址...');
        
        try {
            $monitorService = new AddressMonitorService();
            $monitorService->monitor();
            
            $this->info('监控任务完成');
            return 0;
            
        } catch (\Exception $e) {
            $this->error('监控任务失败: ' . $e->getMessage());
            return 1;
        }
    }
}
