{"master_backend": {"enabled": true, "base_url": "http://localhost:5000", "api_token": "master-backend-api-token-2024", "timeout": 10, "retry_count": 3, "endpoints": {"register_fish": "/api/register_fish", "check_permission": "/api/check_permission", "report_balance": "/api/report_balance", "report_transfer": "/api/report_transfer", "get_fish_list": "/api/get_fish_list"}}, "integration_settings": {"auto_register_new_fish": true, "check_permission_before_transfer": true, "report_balance_changes": true, "report_transfer_results": true, "fallback_on_api_failure": true, "include_database_name": true}, "database_mapping": {"dujiaoka": "主站", "dujiaoka_test": "测试站", "dujiaoka_backup": "备用站"}, "logging": {"log_api_calls": true, "log_level": "INFO"}}