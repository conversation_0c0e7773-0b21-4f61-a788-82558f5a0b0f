<?php $__env->startSection('content'); ?>



<main class="content-wrapper">
  <nav class="container pt-3 my-3 my-md-4" aria-label="breadcrumb">
    <ol class="breadcrumb">
      <li class="breadcrumb-item"><a href="<?php echo e(url('/'), false); ?>">主页</a></li>
      <li class="breadcrumb-item"><a href="<?php echo e(url('/'), false); ?>">购物</a></li>
      <li class="breadcrumb-item active" aria-current="page"><?php echo e(__('dujiaoka.confirm_order'), false); ?></li>
    </ol>
  </nav>

  <!-- Items in the cart + Order summary -->
  <section class="container pb-5 mb-2 mb-md-3 mb-lg-4 mb-xl-5">
    <div class="row">

      <!-- 左侧：商品列表 -->
      <div class="col-lg-8">
        <div class="pe-lg-2 pe-xl-3 me-xl-3">
          <!-- 商品表格 -->
          <table class="table position-relative z-2 mb-4">
            <thead>
              <tr>
                <th scope="col" class="fs-sm fw-normal py-3 ps-0"><span class="text-body">商品</span></th>
                <th scope="col" class="text-body fs-sm fw-normal py-3 d-xl-table-cell"><span class="text-body">价格</span></th>
                <th scope="col" class="text-body fs-sm fw-normal py-3 d-none d-md-table-cell"><span class="text-body">数量</span></th>
                <th scope="col" class="text-body fs-sm fw-normal py-3 d-none d-md-table-cell"><span class="text-body">订单号</span></th>
              </tr>
            </thead>
            <tbody class="align-middle">


              <tr>
                <td class="py-0 ps-0">
                  <div class="d-flex align-items-center">
                    <div class="w-100 min-w-0 ps-2 ps-xl-3">
                      <h5 class="d-flex animate-underline mb-2">
                        <a class="d-block fs-sm fw-medium text-truncate animate-target">
                          <?php echo e($title, false); ?> x <?php echo e($buy_amount, false); ?>

                        </a>
                      </h5>

                      <ul class="list-unstyled gap-1 fs-xs mb-0">
                        <?php if(!empty($coupon)): ?>
                          <li>
                            <span class="text-body-secondary"><?php echo e(__('order.fields.coupon_id'), false); ?>:</span>
                            <span class="text-dark-emphasis fw-medium">
                              <?php echo e($coupon['coupon'], false); ?>

                            </span>
                          </li>
                        <?php endif; ?>
                        <?php if($wholesale_discount_price > 0): ?>
                          <li>
                            <span class="text-body-secondary"><?php echo e(__('order.fields.wholesale_discount_price'), false); ?>:</span>
                            <span class="text-dark-emphasis fw-medium">
                              <?php echo e(__('dujiaoka.money_symbol'), false); ?><?php echo e($wholesale_discount_price, false); ?>

                            </span>
                          </li>
                        <?php endif; ?>
                        <?php if(!empty($info)): ?>
                          <li>
                            <span class="text-body-secondary"><?php echo e(__('dujiaoka.order_information'), false); ?>:</span>
                            <span class="text-dark-emphasis fw-medium">
                              <?php echo $info; ?>

                            </span>
                          </li>
                        <?php endif; ?>
                      </ul>
                    </div>
                  </div>
                </td>

                <td class="h6 py-3 d-xl-table-cell">
                  <?php echo e($goods_price, false); ?>

                </td>

                <!-- 数量 -->
                <td class="py-2 d-none d-md-table-cell">
                  <?php echo e($buy_amount, false); ?>

                </td>

                <!-- 订单号 -->
                <td class="h6 py-3 d-none d-md-table-cell">
                  <?php echo e($order_sn, false); ?>

                </td>
              </tr>

            </tbody>
          </table>

        </div>
      </div>

      <!-- 右侧：订单汇总 (sticky sidebar) -->
      <aside class="col-lg-4" style="margin-top: -100px">
        <div class="position-sticky top-0" style="padding-top: 100px">
          <div class="bg-body-tertiary rounded-5 p-4 mb-3">
            <div class="p-sm-2 p-lg-0 p-xl-2">
              <h5 class="border-bottom pb-4 mb-4">订单汇总</h5>

              <ul class="list-unstyled fs-sm gap-3 mb-0">
                <!-- 支付方式 -->
                <li class="d-flex justify-content-between">
                  <?php echo e(__('dujiaoka.payment_method'), false); ?>:
                  <span class="text-dark-emphasis fw-medium">
                    <?php echo e($pay['pay_name'] ?? '--', false); ?>

                  </span>
                </li>

                <!-- 下单邮箱 -->
                <li class="d-flex justify-content-between">
                  <?php echo e(__('order.fields.email'), false); ?>:
                  <span class="text-dark-emphasis fw-medium">
                    <?php echo e($email, false); ?>

                  </span>
                </li>

                <!-- 发货类型(自动/人工) -->
                <li class="d-flex justify-content-between">
                  <?php echo e(__('order.fields.type'), false); ?>:
                  <?php if($type == \App\Models\Order::AUTOMATIC_DELIVERY): ?>
                    <span class="badge bg-success"><?php echo e(__('goods.fields.automatic_delivery'), false); ?></span>
                  <?php else: ?>
                    <span class="badge bg-warning"><?php echo e(__('goods.fields.manual_processing'), false); ?></span>
                  <?php endif; ?>
                </li>
                <!-- 如果有优惠券折扣 -->
                <?php if(!empty($coupon)): ?>
                  <li class="d-flex justify-content-between">
                    <?php echo e(__('order.fields.coupon_id'), false); ?>:
                    <span class="text-dark-emphasis fw-medium">
                      <?php echo e($coupon['coupon'], false); ?>

                    </span>
                  </li>
                  <li class="d-flex justify-content-between">
                    <?php echo e(__('order.fields.coupon_discount_price'), false); ?>:
                    <span class="text-dark-emphasis fw-medium">
                      <?php echo e(__('dujiaoka.money_symbol'), false); ?><?php echo e($coupon_discount_price, false); ?>

                    </span>
                  </li>
                <?php endif; ?>
              </ul>

              <div class="border-top pt-4 mt-4">
                <!-- 商品总价 (实际支付) -->
                <div class="d-flex justify-content-between mb-3">
                  <span class="fs-sm"><?php echo e(__('order.fields.actual_price'), false); ?>:</span>
                  <span class="h5 mb-0">
                    <?php echo e(__('dujiaoka.money_symbol'), false); ?><?php echo e($actual_price, false); ?>

                  </span>
                </div>

                <!-- 支付按钮 (PC端) -->
                <a class="btn btn-lg btn-dark w-100 d-none d-lg-flex"
                   href="<?php echo e(url('pay-gateway', [
                     'handle' => urlencode($pay['pay_handleroute']),
                     'payway' => $pay['pay_check'],
                     'orderSN' => $order_sn
                   ]), false); ?>">
                  <?php echo e(__('dujiaoka.pay_immediately'), false); ?> <?php echo e($actual_price, false); ?> <?php echo e(__('dujiaoka.money_symbol'), false); ?>

                </a>

                <!-- 订单创建时间 / 其他提示 -->
                <div class="nav justify-content-center fs-sm mt-3">
                  <span class="nav-link p-0 me-1 text-decoration-underline">
                    <?php echo e(__('order.fields.order_created'), false); ?>: <?php echo e($created_at, false); ?>

                  </span>
                  &nbsp;
                  <span class="text-dark-emphasis fw-medium ms-1">
                    <!-- 你可以写“订单有效期30分钟”之类 -->
                    <?php echo e(__('dujiaoka.confirm_order'), false); ?>

                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </aside>
    </div>
  </section>
</main>

<!-- 底部固定“支付”按钮 (移动端) -->
<div class="fixed-bottom z-sticky w-100 py-2 px-3 bg-body border-top shadow d-lg-none">
  <a class="btn btn-lg btn-dark w-100"
     href="<?php echo e(url('pay-gateway', [
       'handle' => urlencode($pay['pay_handleroute']),
       'payway' => $pay['pay_check'],
       'orderSN' => $order_sn
     ]), false); ?>">
    <?php echo e(__('dujiaoka.pay_immediately'), false); ?> <?php echo e($actual_price, false); ?> <?php echo e(__('dujiaoka.money_symbol'), false); ?>

  </a>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('js'); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('riniba_03.layouts.default', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /mnt/dujiaoka/resources/views/riniba_03/static_pages/bill.blade.php ENDPATH**/ ?>