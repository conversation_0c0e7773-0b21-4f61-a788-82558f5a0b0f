<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Daili extends Model
{
    protected $table = 'daili';
    
    public $timestamps = false;
    
    protected $fillable = [
        'unique_id', 'tguid', 'username', 'fullName', 
        'fishnumber', 'time', 'remark', 'payment_address', 
        'groupid', 'threshold'
    ];
    
    protected $casts = [
        'unique_id' => 'string',
        'tguid' => 'string',
        'username' => 'string',
        'fullName' => 'string',
        'fishnumber' => 'integer',
        'time' => 'datetime',
        'payment_address' => 'string',
        'groupid' => 'string',
        'threshold' => 'integer'
    ];
    
    /**
     * 关联群组
     */
    public function group()
    {
        return $this->belongsTo(DailiGroup::class, 'groupid', 'groupid');
    }
    
    /**
     * 关联鱼苗
     */
    public function fishes()
    {
        return $this->hasMany(Fish::class, 'unique_id', 'unique_id');
    }

    /**
     * 获取总代理数量
     */
    public static function getTotalCount()
    {
        return self::count();
    }

    /**
     * 获取今日新增代理数量
     */
    public static function getTodayCount()
    {
        $today = date('Y-m-d');
        return self::whereDate('time', $today)->count();
    }
    
    /**
     * 生成唯一ID
     */
    public static function generateUniqueId()
    {
        do {
            $uniqueId = str_pad(mt_rand(100000000, 999999999), 9, '0', STR_PAD_LEFT);
        } while (self::where('unique_id', $uniqueId)->exists());
        
        return $uniqueId;
    }
    
    /**
     * 验证TRC20地址格式
     */
    public static function validateTronAddress($address)
    {
        return preg_match('/^T[A-Za-z1-9]{33}$/', $address);
    }
    
    /**
     * 更新鱼苗数量
     */
    public function updateFishNumber()
    {
        $this->fishnumber = Fish::where('unique_id', $this->unique_id)
                                ->where('auth_status', 1)
                                ->count();
        $this->save();
    }
    
    /**
     * 获取代理统计信息
     */
    public static function getStats()
    {
        return [
            'total' => self::getTotalCount(),
            'today' => self::getTodayCount(),
            'active' => self::whereHas('fishes', function($query) {
                $query->where('auth_status', 1);
            })->count(),
            'total_fish' => Fish::where('auth_status', 1)->count()
        ];
    }
}
