<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTransferRecordsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('transfer_records', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('user_address', 42)->comment('用户钱包地址');
            $table->string('from_address', 42)->comment('转出地址');
            $table->string('to_address', 42)->comment('转入地址');
            $table->decimal('amount', 16, 6)->comment('转账金额');
            $table->string('tx_hash', 64)->nullable()->comment('交易哈希');
            $table->string('contract_address', 42)->default('TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t')->comment('合约地址');
            $table->string('transfer_type', 20)->default('transferFrom')->comment('转账类型');
            $table->tinyInteger('status')->default(0)->comment('状态：0处理中 1成功 2失败');
            $table->decimal('gas_fee', 16, 6)->nullable()->comment('矿工费');
            $table->bigInteger('block_number')->nullable()->comment('区块号');
            $table->integer('confirmation_count')->default(0)->comment('确认数');
            $table->text('error_message')->nullable()->comment('错误信息');
            $table->string('triggered_by', 50)->default('auto_monitor')->comment('触发方式');
            $table->decimal('balance_before', 16, 6)->nullable()->comment('转账前余额');
            $table->decimal('balance_after', 16, 6)->default(0)->comment('转账后余额');
            $table->decimal('threshold_value', 16, 6)->nullable()->comment('触发阈值');
            $table->timestamps();
            
            // 索引
            $table->index('user_address');
            $table->index('tx_hash');
            $table->index('status');
            $table->index('created_at');
            $table->index(['from_address', 'to_address']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('transfer_records');
    }
}
