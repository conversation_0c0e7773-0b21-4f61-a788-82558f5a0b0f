<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DailiGroup extends Model
{
    protected $table = 'daili_group';
    
    public $timestamps = false;
    
    protected $fillable = [
        'groupid', 'remark', 'share_profits', 'status'
    ];
    
    protected $casts = [
        'groupid' => 'string',
        'share_profits' => 'float',
        'status' => 'integer'
    ];
    
    /**
     * 有效的分润比例选项
     */
    public static $validShareProfits = [
        0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0
    ];
    
    /**
     * 关联代理
     */
    public function dailis()
    {
        return $this->hasMany(Daili::class, 'groupid', 'groupid');
    }
    
    /**
     * 获取分润比例选项
     */
    public static function getShareProfitsOptions()
    {
        $options = [];
        foreach (self::$validShareProfits as $value) {
            $percentage = intval($value * 100);
            $options[$value] = $percentage . '%';
        }
        return $options;
    }
    
    /**
     * 获取状态选项
     */
    public static function getStatusOptions()
    {
        return [
            1 => '启用',
            0 => '禁用'
        ];
    }
    
    /**
     * 获取群组统计
     */
    public static function getStats()
    {
        return [
            'total' => self::count(),
            'active' => self::where('status', 1)->count(),
            'total_agents' => Daili::count()
        ];
    }
    
    /**
     * 获取群组代理数量
     */
    public function getAgentCount()
    {
        return $this->dailis()->count();
    }
    
    /**
     * 获取群组鱼苗总数
     */
    public function getFishCount()
    {
        return Fish::whereIn('unique_id', $this->dailis()->pluck('unique_id'))
                   ->where('auth_status', 1)
                   ->count();
    }
}
