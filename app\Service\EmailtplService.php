<?php
/**
 * The file was created by <PERSON>si<PERSON>.
 *
 * <AUTHOR>
 * @copyright assimon<<EMAIL>>
 * @link      http://utf8.hk/
 */

namespace App\Service;


use App\Models\Emailtpl;

class EmailtplService
{

    /**
     * 通过邮件标识获得邮件模板
     *
     * @param string $token 邮件标识
     * @return Emailtpl
     *
     * <AUTHOR>
     * @copyright assimon<<EMAIL>>
     * @link      http://utf8.hk/
     */
    public function detailByToken(string $token): Emailtpl
    {
        $tpl = Emailtpl::query()->where('tpl_token', $token)->first();
        return $tpl;
    }

}
