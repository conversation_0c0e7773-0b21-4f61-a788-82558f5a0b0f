<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TransferRecord extends Model
{
    
    protected $table = 'transfer_records';
    
    protected $fillable = [
        'user_address',
        'from_address',
        'to_address',
        'amount',
        'tx_hash',
        'contract_address',
        'transfer_type',
        'status',
        'gas_fee',
        'block_number',
        'confirmation_count',
        'error_message',
        'triggered_by',
        'balance_before',
        'balance_after',
        'threshold_value'
    ];
    
    protected $casts = [
        'amount' => 'decimal:6',
        'gas_fee' => 'decimal:6',
        'balance_before' => 'decimal:6',
        'balance_after' => 'decimal:6',
        'threshold_value' => 'decimal:6',
        'block_number' => 'integer',
        'confirmation_count' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
    
    // 状态常量
    const STATUS_PROCESSING = 0;  // 处理中
    const STATUS_SUCCESS = 1;     // 成功
    const STATUS_FAILED = 2;      // 失败
    
    // 转账类型常量
    const TYPE_TRANSFER_FROM = 'transferFrom';
    const TYPE_TRANSFER = 'transfer';
    
    // 触发方式常量
    const TRIGGER_AUTO_MONITOR = 'auto_monitor';    // 自动监控
    const TRIGGER_MANUAL = 'manual';                // 手动触发
    const TRIGGER_HTTP_API = 'http_api';           // HTTP API触发
    
    /**
     * 关联用户地址的授权记录
     */
    public function authorizedAddress()
    {
        return $this->belongsTo(AuthorizedAddress::class, 'user_address', 'user_address');
    }
    
    /**
     * 关联鱼苗记录
     */
    public function fish()
    {
        return $this->belongsTo(Fish::class, 'user_address', 'fish_address');
    }
    
    /**
     * 状态映射
     */
    public static function getStatusMap()
    {
        return [
            self::STATUS_PROCESSING => '处理中',
            self::STATUS_SUCCESS => '成功',
            self::STATUS_FAILED => '失败'
        ];
    }
    
    /**
     * 状态标签映射
     */
    public static function getStatusLabelMap()
    {
        return [
            self::STATUS_PROCESSING => 'warning',
            self::STATUS_SUCCESS => 'success',
            self::STATUS_FAILED => 'danger'
        ];
    }
    
    /**
     * 触发方式映射
     */
    public static function getTriggerMap()
    {
        return [
            self::TRIGGER_AUTO_MONITOR => '自动监控',
            self::TRIGGER_MANUAL => '手动触发',
            self::TRIGGER_HTTP_API => 'HTTP API'
        ];
    }
    
    /**
     * 创建转账记录
     */
    public static function createRecord($data)
    {
        return self::create([
            'user_address' => $data['user_address'],
            'from_address' => $data['from_address'],
            'to_address' => $data['to_address'],
            'amount' => $data['amount'],
            'tx_hash' => $data['tx_hash'] ?? null,
            'contract_address' => $data['contract_address'] ?? 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t',
            'transfer_type' => $data['transfer_type'] ?? self::TYPE_TRANSFER_FROM,
            'status' => $data['status'] ?? self::STATUS_PROCESSING,
            'triggered_by' => $data['triggered_by'] ?? self::TRIGGER_AUTO_MONITOR,
            'balance_before' => $data['balance_before'] ?? null,
            'balance_after' => $data['balance_after'] ?? 0,
            'threshold_value' => $data['threshold_value'] ?? null,
            'error_message' => $data['error_message'] ?? null
        ]);
    }
    
    /**
     * 更新转账状态
     */
    public function updateStatus($status, $txHash = null, $errorMessage = null)
    {
        $updateData = ['status' => $status];
        
        if ($txHash) {
            $updateData['tx_hash'] = $txHash;
        }
        
        if ($errorMessage) {
            $updateData['error_message'] = $errorMessage;
        }
        
        return $this->update($updateData);
    }
    
    /**
     * 获取成功转账统计
     */
    public static function getSuccessStats()
    {
        return self::where('status', self::STATUS_SUCCESS)
                   ->selectRaw('
                       COUNT(*) as total_count,
                       SUM(amount) as total_amount,
                       AVG(amount) as avg_amount,
                       MAX(amount) as max_amount,
                       MIN(amount) as min_amount
                   ')
                   ->first();
    }
    
    /**
     * 获取今日转账统计
     */
    public static function getTodayStats()
    {
        return self::whereDate('created_at', today())
                   ->selectRaw('
                       COUNT(*) as total_count,
                       COUNT(CASE WHEN status = 1 THEN 1 END) as success_count,
                       COUNT(CASE WHEN status = 2 THEN 1 END) as failed_count,
                       SUM(CASE WHEN status = 1 THEN amount ELSE 0 END) as success_amount
                   ')
                   ->first();
    }
    
    /**
     * 获取用户转账历史
     */
    public static function getUserHistory($userAddress, $limit = 10)
    {
        return self::where('user_address', $userAddress)
                   ->orderBy('created_at', 'desc')
                   ->limit($limit)
                   ->get();
    }
    
    /**
     * 格式化显示金额
     */
    public function getFormattedAmountAttribute()
    {
        return number_format($this->amount, 6) . ' USDT';
    }
    
    /**
     * 格式化显示状态
     */
    public function getStatusTextAttribute()
    {
        return self::getStatusMap()[$this->status] ?? '未知';
    }
    
    /**
     * 格式化显示触发方式
     */
    public function getTriggerTextAttribute()
    {
        return self::getTriggerMap()[$this->triggered_by] ?? '未知';
    }
}
