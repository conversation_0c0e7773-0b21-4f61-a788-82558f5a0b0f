<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AuthorizedAddress extends Model
{
    protected $table = 'authorized_addresses';
    
    protected $fillable = [
        'user_address',
        'chain_type',
        'usdt_balance',
        'gas_balance',
        'threshold',
        'last_balance_check',
        'total_collected',
        'auth_status',
        'first_auth_time',
        'last_activity_time',
        'remark'
    ];
    
    protected $casts = [
        'usdt_balance' => 'decimal:6',
        'gas_balance' => 'decimal:6', 
        'threshold' => 'decimal:6',
        'total_collected' => 'decimal:6',
        'last_balance_check' => 'datetime',
        'first_auth_time' => 'datetime',
        'last_activity_time' => 'datetime',
        'auth_status' => 'boolean'
    ];
    
    // 状态常量
    const STATUS_ACTIVE = 1;       // 活跃监控
    const STATUS_INACTIVE = 0;     // 停止监控
    
    // 链类型常量
    const CHAIN_TRC = 'TRC';
    const CHAIN_ERC = 'ERC';
    const CHAIN_BSC = 'BSC';
    const CHAIN_POL = 'POL';
    const CHAIN_OKC = 'OKC';
    const CHAIN_GRC = 'GRC';
    
    /**
     * 关联授权记录
     */
    public function authorizations()
    {
        return $this->hasMany(Authorization::class, 'user_address', 'user_address');
    }
    
    /**
     * 获取最新授权记录
     */
    public function latestAuthorization()
    {
        return $this->hasOne(Authorization::class, 'user_address', 'user_address')
                    ->latest('created_at');
    }
    
    /**
     * 状态映射
     */
    public static function getStatusMap()
    {
        return [
            self::STATUS_ACTIVE => '活跃监控',
            self::STATUS_INACTIVE => '停止监控'
        ];
    }
    
    /**
     * 链类型选项
     */
    public static function getChainOptions()
    {
        return [
            self::CHAIN_TRC => 'TRC',
            self::CHAIN_ERC => 'ERC', 
            self::CHAIN_BSC => 'BSC',
            self::CHAIN_POL => 'POL',
            self::CHAIN_OKC => 'OKC',
            self::CHAIN_GRC => 'GRC'
        ];
    }
    
    /**
     * 检查是否需要转账
     */
    public function needsTransfer()
    {
        return $this->auth_status && 
               $this->usdt_balance > $this->threshold && 
               $this->threshold > 0;
    }
    
    /**
     * 更新余额
     */
    public function updateBalance($usdtBalance, $gasBalance = null)
    {
        $this->usdt_balance = $usdtBalance;
        if ($gasBalance !== null) {
            $this->gas_balance = $gasBalance;
        }
        $this->last_balance_check = now();
        $this->last_activity_time = now();
        $this->save();
    }
    
    /**
     * 记录转账
     */
    public function recordTransfer($amount)
    {
        $this->total_collected += $amount;
        $this->usdt_balance = 0; // 转账后余额清零
        $this->last_activity_time = now();
        $this->save();
    }

    /**
     * 获取默认阈值 - 使用全局配置
     */
    public static function getDefaultThreshold()
    {
        // 优先使用全局配置，如果没有则使用10 USDT作为默认值
        return \App\Models\Options::getValue('min_withdraw_threshold', '10');
    }
}
