<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 管理后台</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #1f2937;
            --accent-color: #3b82f6;
            --accent-hover: #2563eb;
            --success-color: #10b981;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --white: #ffffff;
            --border-radius: 8px;
            --border-radius-lg: 12px;
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gray-50);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .login-container {
            background: var(--white);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            max-width: 420px;
            width: 100%;
            border: 1px solid var(--gray-200);
        }

        .login-header {
            background: var(--primary-color);
            color: var(--white);
            padding: 2rem;
            text-align: center;
        }

        .login-header h3 {
            margin: 0;
            font-weight: 700;
            font-size: 1.5rem;
        }

        .login-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.8;
            font-size: 0.875rem;
        }

        .login-body {
            padding: 2rem;
        }

        .form-control {
            border-radius: var(--border-radius);
            border: 1px solid var(--gray-300);
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
            transition: all 0.15s ease;
            height: 45px;
        }

        .form-control:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            outline: none;
        }

        .btn-login {
            background-color: var(--accent-color);
            border: 1px solid var(--accent-color);
            border-radius: var(--border-radius);
            padding: 0.75rem;
            font-weight: 600;
            width: 100%;
            color: var(--white);
            transition: all 0.15s ease;
            font-size: 0.875rem;
        }

        .btn-login:hover {
            background-color: var(--accent-hover);
            border-color: var(--accent-hover);
            color: var(--white);
        }

        .alert {
            border: 1px solid;
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            font-size: 0.875rem;
        }

        .input-group-text {
            background: var(--gray-50);
            border: 1px solid var(--gray-300);
            border-radius: var(--border-radius) 0 0 var(--border-radius);
            color: var(--gray-600);
            width: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-right: none;
        }

        .form-control.with-icon {
            border-radius: 0 var(--border-radius) var(--border-radius) 0;
            border-left: none;
        }

        .input-group {
            margin-bottom: 1rem;
        }

        .input-group .form-control:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .input-group .input-group-text {
            border-color: var(--gray-300);
        }

        .input-group .form-control:focus + .input-group-text,
        .input-group .input-group-text + .form-control:focus {
            border-color: var(--accent-color);
        }

        .login-footer {
            text-align: center;
            padding: 1rem 2rem 2rem;
            color: var(--gray-500);
            font-size: 0.875rem;
            background: var(--gray-50);
        }


    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h3 class="mb-0">
                <i class="fas fa-fish me-2"></i>
                管理后台
            </h3>
        </div>
        
        <div class="login-body">
            <!-- 消息提示 -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'info-circle' if category == 'info' else 'check-circle' }} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <form method="POST" id="loginForm">
                <!-- 用户名 -->
                <div class="mb-3">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-user"></i>
                        </span>
                        <input type="text"
                               class="form-control with-icon"
                               id="username"
                               name="username"
                               placeholder="用户名"
                               required>
                    </div>
                </div>

                <!-- 密码 -->
                <div class="mb-3">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input type="password"
                               class="form-control with-icon"
                               id="password"
                               name="password"
                               placeholder="密码"
                               required>
                    </div>
                </div>

                <!-- 访问码 -->
                <div class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-key"></i>
                        </span>
                        <input type="password"
                               class="form-control with-icon"
                               id="access_code"
                               name="access_code"
                               placeholder="访问码"
                               required>
                    </div>
                </div>

                <button type="submit" class="btn btn-login">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    登录
                </button>
            </form>
        </div>
        
        <div class="login-footer">
            <p class="mb-0">
                <i class="fas fa-shield-alt me-1"></i>
                安全登录系统
            </p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 聚焦到用户名输入框
            document.getElementById('username').focus();

            // 自动隐藏现有提示消息
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert');
                alerts.forEach(function(alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                });
            }, 5000);
        });
    </script>
</body>
</html>
