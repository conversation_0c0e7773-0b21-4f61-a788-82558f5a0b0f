{% extends "base.html" %}

{% block title %}鱼苗详情 - {{ fish.address[:10] }}... - 鱼苗总后台管理系统{% endblock %}
{% block page_title %}鱼苗详情{% endblock %}

{% block content %}
<!-- 返回按钮 -->
<div class="mb-3">
    <a href="{{ url_for('main.fish_list') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        返回列表
    </a>
</div>

<!-- 鱼苗基本信息 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-fish me-2"></i>
                    基本信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">鱼苗ID:</td>
                                <td><span class="badge bg-secondary">{{ fish.id }}</span></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">地址:</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <code class="me-2">{{ fish.address }}</code>
                                        <button class="btn btn-sm btn-outline-secondary" 
                                                onclick="copyToClipboard('{{ fish.address }}')"
                                                title="复制地址">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">USDT余额:</td>
                                <td>
                                    <span class="badge bg-primary fs-6">
                                        {{ "%.6f"|format(fish.usdt_balance) }} USDT
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">TRX余额:</td>
                                <td>
                                    <span class="badge bg-info fs-6">
                                        {{ "%.6f"|format(fish.trx_balance) }} TRX
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">转账阈值:</td>
                                <td>
                                    <span class="badge bg-warning fs-6">
                                        {{ "%.2f"|format(fish.transfer_threshold) }} USDT
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">状态:</td>
                                <td>
                                    <div class="d-flex flex-column gap-1">
                                        {% if fish.is_intercepted %}
                                            <span class="badge bg-danger">截流中</span>
                                        {% endif %}
                                        {% if fish.is_active %}
                                            <span class="badge bg-success">活跃</span>
                                        {% else %}
                                            <span class="badge bg-secondary">非活跃</span>
                                        {% endif %}
                                        {% if fish.auth_status %}
                                            <span class="badge bg-info">已授权</span>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">总接收金额:</td>
                                <td>
                                    <span class="text-success fw-bold fs-6">
                                        {{ "%.6f"|format(fish.total_received) }} USDT
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">总转出金额:</td>
                                <td>
                                    <span class="text-danger fw-bold fs-6">
                                        {{ "%.6f"|format(fish.total_transferred) }} USDT
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">转账次数:</td>
                                <td>
                                    <span class="badge bg-dark fs-6">
                                        {{ fish.transfer_count }} 次
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">创建时间:</td>
                                <td>{{ fish.created_at|datetime }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">最后更新:</td>
                                <td>{{ fish.updated_at|datetime }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                {% if fish.remarks %}
                <div class="mt-3">
                    <h6>备注信息:</h6>
                    <div class="alert alert-info">
                        {{ fish.remarks }}
                    </div>
                </div>
                {% endif %}
                
                <!-- 操作按钮 -->
                <div class="mt-3">
                    <div class="btn-group" role="group">
                        <button class="btn btn-outline-warning" 
                                onclick="toggleIntercept({{ fish.id }}, {{ fish.is_intercepted|lower }})">
                            <i class="fas fa-{{ 'play' if fish.is_intercepted else 'ban' }} me-2"></i>
                            {{ '取消截流' if fish.is_intercepted else '启动截流' }}
                        </button>
                        <button class="btn btn-outline-info" onclick="checkBalance({{ fish.id }})">
                            <i class="fas fa-sync-alt me-2"></i>
                            检查余额
                        </button>
                        <button class="btn btn-outline-success" onclick="manualTransfer({{ fish.id }})">
                            <i class="fas fa-paper-plane me-2"></i>
                            手动转账
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 转账记录 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-exchange-alt me-2"></i>
                    转账记录
                </h5>
                <span class="badge bg-primary">{{ transfers|length }} 条记录</span>
            </div>
            <div class="card-body p-0">
                {% if transfers %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>目标地址</th>
                                    <th>金额</th>
                                    <th>交易哈希</th>
                                    <th>状态</th>
                                    <th>类型</th>
                                    <th>创建时间</th>
                                    <th>完成时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transfer in transfers %}
                                <tr>
                                    <td>
                                        <span class="badge bg-secondary">{{ transfer.id }}</span>
                                    </td>
                                    <td>
                                        <code>{{ transfer.to_address[:10] }}...{{ transfer.to_address[-6:] }}</code>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">
                                            {{ "%.6f"|format(transfer.amount) }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if transfer.tx_hash %}
                                            <div class="d-flex align-items-center">
                                                <code class="me-2">{{ transfer.tx_hash[:10] }}...</code>
                                                <button class="btn btn-sm btn-outline-secondary" 
                                                        onclick="copyToClipboard('{{ transfer.tx_hash }}')"
                                                        title="复制交易哈希">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if transfer.status == 'success' %}
                                            <span class="badge bg-success">成功</span>
                                        {% elif transfer.status == 'pending' %}
                                            <span class="badge bg-warning">处理中</span>
                                        {% else %}
                                            <span class="badge bg-danger">失败</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if transfer.transfer_type == 'auto' %}
                                            <span class="badge bg-info">自动</span>
                                        {% else %}
                                            <span class="badge bg-secondary">手动</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>{{ transfer.created_at|datetime }}</small>
                                    </td>
                                    <td>
                                        {% if transfer.completed_at %}
                                            <small>{{ transfer.completed_at|datetime }}</small>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">暂无转账记录</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 余额历史 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    余额历史
                </h5>
                <span class="badge bg-primary">{{ balance_history|length }} 条记录</span>
            </div>
            <div class="card-body p-0">
                {% if balance_history %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>记录时间</th>
                                    <th>USDT余额</th>
                                    <th>TRX余额</th>
                                    <th>余额变化</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for history in balance_history %}
                                <tr>
                                    <td>
                                        <small>{{ history.recorded_at|datetime }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">
                                            {{ "%.6f"|format(history.usdt_balance) }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            {{ "%.6f"|format(history.trx_balance) }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if history.balance_change > 0 %}
                                            <span class="text-success">
                                                <i class="fas fa-arrow-up me-1"></i>
                                                +{{ "%.6f"|format(history.balance_change) }}
                                            </span>
                                        {% elif history.balance_change < 0 %}
                                            <span class="text-danger">
                                                <i class="fas fa-arrow-down me-1"></i>
                                                {{ "%.6f"|format(history.balance_change) }}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">
                                                <i class="fas fa-minus me-1"></i>
                                                0.000000
                                            </span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">暂无余额历史记录</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 操作确认模态框 -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认操作</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="confirmMessage"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmButton">确认</button>
            </div>
        </div>
    </div>
</div>

<!-- 手动转账模态框 -->
<div class="modal fade" id="transferModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">手动转账</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="transferForm">
                    <div class="mb-3">
                        <label for="toAddress" class="form-label">目标地址</label>
                        <input type="text" class="form-control" id="toAddress" required>
                    </div>
                    <div class="mb-3">
                        <label for="amount" class="form-label">转账金额</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="amount" step="0.000001" min="0" required>
                            <span class="input-group-text">USDT</span>
                        </div>
                        <small class="text-muted">当前余额: {{ "%.6f"|format(fish.usdt_balance) }} USDT</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitTransfer()">确认转账</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 复制到剪贴板
    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function() {
            showToast('已复制到剪贴板', 'success');
        }).catch(function(err) {
            console.error('复制失败:', err);
            showToast('复制失败', 'error');
        });
    }
    
    // 切换截流状态
    function toggleIntercept(fishId, currentStatus) {
        const action = currentStatus ? '取消截流' : '启动截流';
        const message = `确定要${action}这个鱼苗地址吗？`;
        
        showConfirm(message, function() {
            console.log(`${action} 鱼苗 ID: ${fishId}`);
            showToast(`${action}操作已提交`, 'info');
            // 实际实现中应该调用后端API并刷新页面
        });
    }
    
    // 检查余额
    function checkBalance(fishId) {
        showToast('正在检查余额...', 'info');
        console.log(`检查余额 鱼苗 ID: ${fishId}`);
        
        // 模拟API调用
        setTimeout(function() {
            showToast('余额检查完成', 'success');
            // 实际实现中应该刷新页面数据
        }, 2000);
    }
    
    // 手动转账
    function manualTransfer(fishId) {
        new bootstrap.Modal(document.getElementById('transferModal')).show();
    }
    
    // 提交转账
    function submitTransfer() {
        const toAddress = document.getElementById('toAddress').value;
        const amount = document.getElementById('amount').value;
        
        if (!toAddress || !amount) {
            showToast('请填写完整的转账信息', 'error');
            return;
        }
        
        if (parseFloat(amount) > {{ fish.usdt_balance }}) {
            showToast('转账金额不能超过当前余额', 'error');
            return;
        }
        
        showConfirm(`确定要转账 ${amount} USDT 到地址 ${toAddress.substring(0, 10)}... 吗？`, function() {
            // 调用后端API执行转账
            fetch(`/fish/{{ fish.id }}/manual_transfer`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    to_address: toAddress,
                    amount: parseFloat(amount)
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('转账请求已提交', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('transferModal')).hide();
                    // 刷新页面显示最新状态
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    showToast(`转账失败: ${data.error || '未知错误'}`, 'error');
                }
            })
            .catch(error => {
                console.error('转账请求失败:', error);
                showToast('转账请求失败，请检查网络连接', 'error');
            });
        });
    }
    
    // 显示确认对话框
    function showConfirm(message, callback) {
        document.getElementById('confirmMessage').textContent = message;
        document.getElementById('confirmButton').onclick = function() {
            callback();
            bootstrap.Modal.getInstance(document.getElementById('confirmModal')).hide();
        };
        new bootstrap.Modal(document.getElementById('confirmModal')).show();
    }
    
    // 显示提示消息
    function showToast(message, type) {
        const toastContainer = document.getElementById('toastContainer') || createToastContainer();
        
        const toastId = 'toast_' + Date.now();
        const toastHtml = `
            <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'primary'} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;
        
        toastContainer.insertAdjacentHTML('beforeend', toastHtml);
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement);
        toast.show();
        
        // 自动移除
        toastElement.addEventListener('hidden.bs.toast', function() {
            toastElement.remove();
        });
    }
    
    // 创建Toast容器
    function createToastContainer() {
        const container = document.createElement('div');
        container.id = 'toastContainer';
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '1055';
        document.body.appendChild(container);
        return container;
    }
</script>
{% endblock %}
