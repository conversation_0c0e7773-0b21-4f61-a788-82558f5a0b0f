<div class="col product-item">
    <div class="product-card animate-underline hover-effect-opacity bg-body rounded">
        <div class="position-relative mb-3">

            
            <?php if($goods['type'] == \App\Models\Goods::AUTOMATIC_DELIVERY): ?>
                <span class="badge text-bg-success position-absolute top-0 start-0 z-2 mt-2 mt-sm-3 ms-2 ms-sm-3">自动</span>
            <?php else: ?>
                <span class="badge text-bg-info position-absolute top-0 start-0 z-2 mt-2 mt-sm-3 ms-2 ms-sm-3">人工</span>
            <?php endif; ?>

            
            <?php if($goods['in_stock'] <= 0): ?>
                <span class="badge text-bg-danger position-absolute top-0 start-0 z-2 mt-2 mt-sm-3 ms-2 ms-sm-3" style="margin-left: 56px;">
                    缺货
                </span>
            <?php endif; ?>

            
            <a class="d-flex bg-body-tertiary rounded p-3"
               href="<?php if($goods['in_stock'] > 0): ?> <?php echo e(url("/buy/{$goods['id']}"), false); ?> <?php else: ?> javascript:void(0); <?php endif; ?>"
               <?php if($goods['in_stock'] <= 0): ?>
                  onclick="showToast('商品库存不足, 请联系客服补货.');"
               <?php endif; ?>
            >
                <div class="ratio" style="--cz-aspect-ratio: calc(308 / 274 * 100%)">
                    <img src="<?php echo e(picture_ulr($goods['picture']), false); ?>" alt="<?php echo e($goods['gd_name'], false); ?>">
                </div>
            </a>
        </div>

        
        <div class="nav mb-1">
            <a class="nav-link animate-target min-w-0 text-dark-emphasis p-0"
               href="<?php if($goods['in_stock'] > 0): ?> <?php echo e(url("/buy/{$goods['id']}"), false); ?> <?php else: ?> javascript:void(0); <?php endif; ?>"
               <?php if($goods['in_stock'] <= 0): ?>
                  onclick="showToast('商品库存不足, 请联系客服补货.');"
               <?php endif; ?>
            >
                <span class="text-truncate">
                    <?php echo e($goods['gd_name'], false); ?>

                </span>
            </a>
        </div>

        
        <div class="h6 mb-1">
            <?php echo e(number_format($goods['actual_price'], 2), false); ?> <?php echo e(__('dujiaoka.money_symbol'), false); ?>


            
            <?php if(!empty($goods['retail_price'])): ?>
                <del class="fs-sm fw-normal text-body-tertiary">
                    <?php echo e(number_format($goods['retail_price'], 2), false); ?> <?php echo e(__('dujiaoka.money_symbol'), false); ?>

                </del>
            <?php endif; ?>
        </div>

        
        <?php
            $maxStock = 50;
            $inStock = (int) $goods['in_stock'];
            $percent = 0;
            if($inStock > 0){
                $percent = ($inStock >= $maxStock) ? 100 : round(($inStock / $maxStock) * 100, 2);
            }
        ?>
        <div class="progress mb-2" role="progressbar" aria-label="Available in stock"
             aria-valuenow="<?php echo e($percent, false); ?>" aria-valuemin="0" aria-valuemax="100" style="height: 4px">
            <div class="progress-bar bg-dark rounded-pill d-none-dark" style="width: <?php echo e($percent, false); ?>%"></div>
            <div class="progress-bar bg-light rounded-pill d-none d-block-dark" style="width: <?php echo e($percent, false); ?>%"></div>
        </div>
    </div>
</div>
<?php /**PATH /mnt/dujiaoka/resources/views/riniba_03/layouts/_goods.blade.php ENDPATH**/ ?>