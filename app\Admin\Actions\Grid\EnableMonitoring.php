<?php

namespace App\Admin\Actions\Grid;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\BatchAction;
use Illuminate\Http\Request;

class EnableMonitoring extends BatchAction
{
    protected $title = '启用监控';

    public function handle(Request $request)
    {
        $keys = $this->getKey();
        
        foreach ($keys as $key) {
            $address = $this->getModel()->find($key);
            if ($address) {
                $address->auth_status = true;
                $address->save();
            }
        }

        return $this->response()
            ->success('已启用监控')
            ->refresh();
    }

    public function confirm()
    {
        return ['确定要启用选中地址的监控吗？', '启用后系统将开始24小时监控这些地址的余额变化'];
    }
}
