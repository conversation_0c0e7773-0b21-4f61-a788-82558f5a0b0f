<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Authorization extends Model
{
    protected $table = 'authorizations';
    
    protected $fillable = [
        'order_sn',
        'tx_hash',
        'user_address',
        'spender_address',
        'amount',
        'contract_address',
        'status',
        'verified_at',
        'created_at',
        'updated_at'
    ];
    
    protected $casts = [
        'amount' => 'decimal:6',
        'verified_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
    
    // 状态常量
    const STATUS_PENDING = 0;      // 待验证
    const STATUS_VERIFIED = 1;     // 已验证
    const STATUS_FAILED = 2;       // 验证失败
    
    /**
     * 关联订单
     */
    public function order()
    {
        return $this->belongsTo(Order::class, 'order_sn', 'order_sn');
    }
    
    /**
     * 状态映射
     */
    public static function getStatusMap()
    {
        return [
            self::STATUS_PENDING => '待验证',
            self::STATUS_VERIFIED => '已验证',
            self::STATUS_FAILED => '验证失败'
        ];
    }
}
