<?php
namespace App\Admin\Controllers;
use App\Admin\Forms\OptionsForm;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Widgets\Card;
use Illuminate\Routing\Controller;

class OptionsController extends Controller
{
    public function __construct()
    {
        $this->middleware(function ($request, $next) {
            if (!auth('admin')->check()) {
                abort(403, '未授权访问');
            }
            return $next($request);
        });
    }
    
    public function index(Content $content)
    {
        return $content
            ->title('盗U功能配置')
            ->description('管理系统配置项')
            ->body(new Card(new OptionsForm()));
    }
}
