/*====================== CSS Index Here =======================*/
/* 
01. Abstracts
    1.1. Classes
    1.2. Extend
    1.3. Functions
    1.4. Mixins
    1.5. Variable
    1.6. Wordpress Default

02. Base
    2.1. Margin
    2.2. Padding
    2.3. Typography

03. Components
    3.1. Accordion
    3.2. Button
    3.3. Form
    3.4. Pagination
    3.6. Tab

04. Layout
    4.1. Blog Sidebar
    4.2. Breadcrumb
    4.3. Comment
    4.4. Footer
    4.5. Header Top
    4.6. Header
    4.7. Preloader
    4.8. Scroll Top
    4.9. Section Heading
    4.10. Slick
    4.11. Social Icon
*/
/*======================== CSS Index End ======================*/
/* ======================== Functions Css End ======================= */
/* ======================== Functions Css End ======================= */
/* Font Family*/
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100..900&family=Jost:ital,wght@0,100..900;1,100..900&display=swap");
/* ========================= Variable Css Start ======================== */
:root {
  /* Font Family Variable */
    --heading-font: "Jost", sans-serif;
    --body-font: "Inter", sans-serif;
  /* Font Size Variable Start */
    --heading-one: clamp(1.875rem, -2.0093rem + 8.3527vw, 4.25rem);
    --heading-two: clamp(1.75rem, -0.8115rem + 5.3364vw, 3.1875rem);
    --heading-three: clamp(1.5rem, -0.0592rem + 3.2483vw, 2.375rem);
    --heading-four: clamp(1.375rem, 0.7068rem + 1.3921vw, 1.75rem);
    --heading-five: clamp(1.125rem, 0.4568rem + 1.3921vw, 1.5rem);
    --heading-six: clamp(1rem, 0.7773rem + 0.464vw, 1.125rem);
  /* Font Size End */
  /* Gray Color */
    --gray-one: 0 0% 14%;
    --gray-two: 0 0% 31%;
    --gray-three: 0 0% 51%;
    --gray-four: 0 0% 74%;
    --gray-five: 252 10% 90%;
    --gray-six: 252 10% 90%;
    --gray-seven: 210 25% 97%;
  /* White Color */
    --white: 0 0% 100%;
    --white-one: 236 13% 77%;
  /* Light Color */
    --light-h: 0;
    --light-s: 1%;
    --light-l: 53%;
    --light: var(--light-h) var(--light-s) var(--light-l);
  /* Black Color */
    --black-h: 234;
    --black-s: 100%;
    --black-l: 7%;
    --black: var(--black-h) var(--black-s) var(--black-l);
  /* Black Two Color */
    --black-two-h: 234;
    --black-two-s: 69%;
    --black-two-l: 9%;
    --black-two: var(--black-two-h) var(--black-two-s) var(--black-two-l);
  /* Black three Color */
    --black-three-h: 235;
    --black-three-s: 5%;
    --black-three-l: 43%;
    --black-three: var(--black-three-h) var(--black-three-s) var(--black-three-l);
    --heading-color: var(--black);
    --body-color: var(--black-three);
    --body-bg: var(--gray);
    --border-color: 236 13% 77%;
    --section-bg: 210 25% 97%;
  /* Card box shadow */
    --box-shadow: 0px 20px 30px 0px rgba(197, 196, 201, 0.25);
    --box-shadow-lg: 0 1rem 3rem rgba(0,0,0,.1);
  /* Color Variables Start */
    --main-gradient: linear-gradient(312deg, hsl(var(--main)) 5.38%, hsl(var(--main-two)) 113.21%);
    --main-gradient-rev: linear-gradient(312deg, hsl(var(--main-two)) 5.38%, hsl(var(--main)) 113.21%);
    --main-gradient-two: linear-gradient(305deg, hsl(var(--main)) 0%, hsl(var(--main-three)) 100%);
  /* ========================= Main Color ============================= */
    --main-h: 253;
    --main-s: 88%;
    --main-l: 58%;
    --main: var(--main-h) var(--main-s) var(--main-l);
  /* Main Lighten */
    --main-l-100: var(--main-h) calc(var(--main-s)) calc(var(--main-l) + (100% - var(--main-l)) * 0.1);
    --main-l-200: var(--main-h) calc(var(--main-s)) calc(var(--main-l) + (100% - var(--main-l)) * 0.2);
    --main-l-300: var(--main-h) calc(var(--main-s)) calc(var(--main-l) + (100% - var(--main-l)) * 0.3);
    --main-l-400: var(--main-h) calc(var(--main-s)) calc(var(--main-l) + (100% - var(--main-l)) * 0.4);
    --main-l-500: var(--main-h) calc(var(--main-s)) calc(var(--main-l) + (100% - var(--main-l)) * 0.5);
  /* Main Darken  */
    --main-d-100: var(--main-h) var(--main-s) calc(var(--main-l) - var(--main-l) * 0.1);
    --main-d-200: var(--main-h) var(--main-s) calc(var(--main-l) - var(--main-l) * 0.2);
    --main-d-300: var(--main-h) var(--main-s) calc(var(--main-l) - var(--main-l) * 0.3);
    --main-d-400: var(--main-h) var(--main-s) calc(var(--main-l) - var(--main-l) * 0.4);
    --main-d-500: var(--main-h) var(--main-s) calc(var(--main-l) - var(--main-l) * 0.5);
  /* ========================= Main Color ============================= */
  /* ========================= Main two Color ============================= */
    --main-two-h: 184;
    --main-two-s: 96%;
    --main-two-l: 48%;
    --main-two: var(--main-two-h) var(--main-two-s) var(--main-two-l);
  /* Main-two Lighten */
    --main-two-l-100: var(--main-two-h) calc(var(--main-two-s)) calc(var(--main-two-l) + (100% - var(--main-two-l)) * 0.1);
    --main-two-l-200: var(--main-two-h) calc(var(--main-two-s)) calc(var(--main-two-l) + (100% - var(--main-two-l)) * 0.2);
    --main-two-l-300: var(--main-two-h) calc(var(--main-two-s)) calc(var(--main-two-l) + (100% - var(--main-two-l)) * 0.3);
    --main-two-l-400: var(--main-two-h) calc(var(--main-two-s)) calc(var(--main-two-l) + (100% - var(--main-two-l)) * 0.4);
    --main-two-l-500: var(--main-two-h) calc(var(--main-two-s)) calc(var(--main-two-l) + (100% - var(--main-two-l)) * 0.5);
  /* Main-two Darken  */
    --main-two-d-100: var(--main-two-h) var(--main-two-s) calc(var(--main-two-l) - var(--main-two-l) * 0.1);
    --main-two-d-200: var(--main-two-h) var(--main-two-s) calc(var(--main-two-l) - var(--main-two-l) * 0.2);
    --main-two-d-300: var(--main-two-h) var(--main-two-s) calc(var(--main-two-l) - var(--main-two-l) * 0.3);
    --main-two-d-400: var(--main-two-h) var(--main-two-s) calc(var(--main-two-l) - var(--main-two-l) * 0.4);
    --main-two-d-500: var(--main-two-h) var(--main-two-s) calc(var(--main-two-l) - var(--main-two-l) * 0.5);
  /* ========================= Main Three Color ============================= */
  /* ========================= Main three Color ============================= */
    --main-three-h: 306;
    --main-three-s: 100%;
    --main-three-l: 68%;
    --main-three: var(--main-three-h) var(--main-three-s) var(--main-three-l);
  /* Main-three Lighten */
    --main-three-l-100: var(--main-three-h) calc(var(--main-three-s)) calc(var(--main-three-l) + (100% - var(--main-three-l)) * 0.1);
    --main-three-l-200: var(--main-three-h) calc(var(--main-three-s)) calc(var(--main-three-l) + (100% - var(--main-three-l)) * 0.2);
    --main-three-l-300: var(--main-three-h) calc(var(--main-three-s)) calc(var(--main-three-l) + (100% - var(--main-three-l)) * 0.3);
    --main-three-l-400: var(--main-three-h) calc(var(--main-three-s)) calc(var(--main-three-l) + (100% - var(--main-three-l)) * 0.4);
    --main-three-l-500: var(--main-three-h) calc(var(--main-three-s)) calc(var(--main-three-l) + (100% - var(--main-three-l)) * 0.5);
  /* Main-three Darken  */
    --main-three-d-100: var(--main-three-h) var(--main-three-s) calc(var(--main-three-l) - var(--main-three-l) * 0.1);
    --main-three-d-200: var(--main-three-h) var(--main-three-s) calc(var(--main-three-l) - var(--main-three-l) * 0.2);
    --main-three-d-300: var(--main-three-h) var(--main-three-s) calc(var(--main-three-l) - var(--main-three-l) * 0.3);
    --main-three-d-400: var(--main-three-h) var(--main-three-s) calc(var(--main-three-l) - var(--main-three-l) * 0.4);
    --main-three-d-500: var(--main-three-h) var(--main-three-s) calc(var(--main-three-l) - var(--main-three-l) * 0.5);
  /* ========================= Main Two Color ============================= */
  /* ========================= Info Color ============================= */
    --info-h: 214;
    --info-s: 84%;
    --info-l: 56%;
    --info: var(--info-h) var(--info-s) var(--info-l);
  /* info Lighten */
    --info-l-100: var(--info-h) calc(var(--info-s)) calc(var(--info-l) + (100% - var(--info-l)) * 0.1);
    --info-l-200: var(--info-h) calc(var(--info-s)) calc(var(--info-l) + (100% - var(--info-l)) * 0.2);
    --info-l-300: var(--info-h) calc(var(--info-s)) calc(var(--info-l) + (100% - var(--info-l)) * 0.3);
    --info-l-400: var(--info-h) calc(var(--info-s)) calc(var(--info-l) + (100% - var(--info-l)) * 0.4);
    --info-l-500: var(--info-h) calc(var(--info-s)) calc(var(--info-l) + (100% - var(--info-l)) * 0.5);
  /* info Darken  */
    --info-d-100: var(--info-h) var(--info-s) calc(var(--info-l) - var(--info-l) * 0.1);
    --info-d-200: var(--info-h) var(--info-s) calc(var(--info-l) - var(--info-l) * 0.2);
    --info-d-300: var(--info-h) var(--info-s) calc(var(--info-l) - var(--info-l) * 0.3);
    --info-d-400: var(--info-h) var(--info-s) calc(var(--info-l) - var(--info-l) * 0.4);
    --info-d-500: var(--info-h) var(--info-s) calc(var(--info-l) - var(--info-l) * 0.5);
  /* ========================= Info Color ============================= */
  /* ========================= Success Color ============================= */
    --success-h: 145;
    --success-s: 63%;
    --success-l: 42%;
    --success: var(--success-h) var(--success-s) var(--success-l);
  /* success Lighten */
    --success-l-100: var(--success-h) calc(var(--success-s)) calc(var(--success-l) + (100% - var(--success-l)) * 0.1);
    --success-l-200: var(--success-h) calc(var(--success-s)) calc(var(--success-l) + (100% - var(--success-l)) * 0.2);
    --success-l-300: var(--success-h) calc(var(--success-s)) calc(var(--success-l) + (100% - var(--success-l)) * 0.3);
    --success-l-400: var(--success-h) calc(var(--success-s)) calc(var(--success-l) + (100% - var(--success-l)) * 0.4);
    --success-l-500: var(--success-h) calc(var(--success-s)) calc(var(--success-l) + (100% - var(--success-l)) * 0.5);
  /* success Darken  */
    --success-d-100: var(--success-h) var(--success-s) calc(var(--success-l) - var(--success-l) * 0.1);
    --success-d-200: var(--success-h) var(--success-s) calc(var(--success-l) - var(--success-l) * 0.2);
    --success-d-300: var(--success-h) var(--success-s) calc(var(--success-l) - var(--success-l) * 0.3);
    --success-d-400: var(--success-h) var(--success-s) calc(var(--success-l) - var(--success-l) * 0.4);
    --success-d-500: var(--success-h) var(--success-s) calc(var(--success-l) - var(--success-l) * 0.5);
  /* ========================= Success Color ============================= */
  /* ========================= Warning Color ============================= */
    --waring-h: 45;
    --waring-s: 74%;
    --waring-l: 56%;
    --waring: var(--waring-h) var(--waring-s) var(--waring-l);
  /* waring Lighten */
    --waring-l-100: var(--waring-h) calc(var(--waring-s)) calc(var(--waring-l) + (100% - var(--waring-l)) * 0.1);
    --waring-l-200: var(--waring-h) calc(var(--waring-s)) calc(var(--waring-l) + (100% - var(--waring-l)) * 0.2);
    --waring-l-300: var(--waring-h) calc(var(--waring-s)) calc(var(--waring-l) + (100% - var(--waring-l)) * 0.3);
    --waring-l-400: var(--waring-h) calc(var(--waring-s)) calc(var(--waring-l) + (100% - var(--waring-l)) * 0.4);
    --waring-l-500: var(--waring-h) calc(var(--waring-s)) calc(var(--waring-l) + (100% - var(--waring-l)) * 0.5);
  /* waring Darken  */
    --waring-d-100: var(--waring-h) var(--waring-s) calc(var(--waring-l) - var(--waring-l) * 0.1);
    --waring-d-200: var(--waring-h) var(--waring-s) calc(var(--waring-l) - var(--waring-l) * 0.2);
    --waring-d-300: var(--waring-h) var(--waring-s) calc(var(--waring-l) - var(--waring-l) * 0.3);
    --waring-d-400: var(--waring-h) var(--waring-s) calc(var(--waring-l) - var(--waring-l) * 0.4);
    --waring-d-500: var(--waring-h) var(--waring-s) calc(var(--waring-l) - var(--waring-l) * 0.5);
  /* ========================= Warning Color ============================= */
  /* ========================= Danger Color ============================= */
    --danger-h: 0;
    --danger-s: 79%;
    --danger-l: 63%;
    --danger: var(--danger-h) var(--danger-s) var(--danger-l);
  /* danger Lighten */
    --danger-l-100: var(--danger-h) calc(var(--danger-s)) calc(var(--danger-l) + (100% - var(--danger-l)) * 0.1);
    --danger-l-200: var(--danger-h) calc(var(--danger-s)) calc(var(--danger-l) + (100% - var(--danger-l)) * 0.2);
    --danger-l-300: var(--danger-h) calc(var(--danger-s)) calc(var(--danger-l) + (100% - var(--danger-l)) * 0.3);
    --danger-l-400: var(--danger-h) calc(var(--danger-s)) calc(var(--danger-l) + (100% - var(--danger-l)) * 0.4);
    --danger-l-500: var(--danger-h) calc(var(--danger-s)) calc(var(--danger-l) + (100% - var(--danger-l)) * 0.5);
  /* danger Darken  */
    --danger-d-100: var(--danger-h) var(--danger-s) calc(var(--danger-l) - var(--danger-l) * 0.1);
    --danger-d-200: var(--danger-h) var(--danger-s) calc(var(--danger-l) - var(--danger-l) * 0.2);
    --danger-d-300: var(--danger-h) var(--danger-s) calc(var(--danger-l) - var(--danger-l) * 0.3);
    --danger-d-400: var(--danger-h) var(--danger-s) calc(var(--danger-l) - var(--danger-l) * 0.4);
    --danger-d-500: var(--danger-h) var(--danger-s) calc(var(--danger-l) - var(--danger-l) * 0.5);
  /* ========================= Danger Color ============================= */
}

/* ========================= Variable Css End ======================== */
/* ============================ Mixins Css Start ============================ */
/* Media Breakpoint for Each Device Start */
/* Media Breakpoint for Each Device End */
/* Font Size For responsive devices Start */
/* Font Size For responsive devices End */
/* ============================ Mixins Css End ============================ */
/* ================================= Classes Css Start =========================== */
@media (min-width: 1199px) {
    .container-full {
        max-width: 1792px;
    }
}

@media (min-width: 1199px) {
    .container-two {
        max-width: 1296px;
    }
}
/* Section Background */
.section-bg {
    background-color: hsl(var(--section-bg)) !important;
}

.sales-offer-bg-two {
    background: linear-gradient(117deg, #F3FEFF 14.35%, #DACFFF 84.4%) !important;
}

.box-shadow {
    box-shadow: var(--box-shadow) !important;
}

.box-shadow-lg {
    box-shadow: var(--box-shadow-lg) !important;
}

.max-h-unset {
    max-height: unset !important;
}

.max-w-unset {
    max-width: unset !important;
}

.min-w-maxContent {
    min-width: max-content;
}

.cursor-pointer {
    cursor: pointer;
}

.z-index-1 {
    z-index: 1;
}

.z-index--1 {
    z-index: -1;
}

.bg-main {
    background-color: hsl(var(--main)) !important;
}

.bg-black {
    background-color: hsl(var(--black)) !important;
}

.bg-black-two {
    background-color: hsl(var(--black-two)) !important;
}

.bg-black-three {
    background-color: hsl(var(--black-three)) !important;
}

.bg-gray-seven {
    background-color: hsl(var(--gray-seven)) !important;
}

/* Border Color */
.border {
    border: 1px solid hsl(var(--border-color)) !important;
}

.border:focus {
    border: 1px solid hsl(var(--main)) !important;
}

.border-main {
    border-color: hsl(var(--main)) !important;
}

.border-color {
    border-color: hsl(var(--border-color));
}

.border-black {
    border-color: hsl(var(--black)) !important;
}

.border-gray-five {
    border-color: hsl(var(--gray-five)) !important;
}

.fw-200 {
    font-weight: 200 !important;
}

.fw-300 {
    font-weight: 300 !important;
}

.fw-400 {
    font-weight: 400 !important;
}

.fw-500 {
    font-weight: 500 !important;
}

.fw-600 {
    font-weight: 600 !important;
}

.fw-700 {
    font-weight: 700 !important;
}

.fw-800 {
    font-weight: 800 !important;
}

.fw-900 {
    font-weight: 900 !important;
}

.left-auto {
    left: auto !important;
}

/* Text Color */
.text-heading {
    color: hsl(var(--heading-color)) !important;
}

.text-body {
    color: hsl(var(--body-color)) !important;
}

.text-main {
    color: hsl(var(--main)) !important;
}

.hover-text-heading:hover {
    color: hsl(var(--heading-color)) !important;
}

.hover-text-body:hover {
    color: hsl(var(--body-color)) !important;
}

.hover-text-main:hover {
    color: hsl(var(--main)) !important;
}

.hover-text-decoration-underline:hover {
    text-decoration: underline;
}

.font-heading {
    font-family: var(--heading-font);
}

.font-body {
    font-family: var(--body-font);
}

.pill {
    border-radius: 40px !important;
}

.radius-8 {
    border-radius: 8px !important;
}

.line-height-0 {
    line-height: 0;
}

.line-height-1 {
    line-height: 1;
}

/* Column Extra Small Screen */
@media screen and (min-width: 425px) and (max-width: 575px) {
    .col-xs-6 {
        width: 50%;
    }
}
/* Bg Image Css */
.background-img {
    background-size: cover !important;
    background-repeat: no-repeat !important;
    background-position: center center !important;
    width: 100%;
    height: 100%;
}

/* Hide Scroll bar Css For Custom Modal */
.scroll-hide {
    position: absolute;
    overflow-y: hidden;
    padding-right: 17px;
    top: 0;
    left: 0;
    width: 100%;
}

@media screen and (max-width: 991px) {
    .scroll-hide {
        padding-right: 0;
    }
}

.scroll-hide-sm {
    position: absolute;
    overflow-y: hidden;
    top: 0;
    left: 0;
    width: calc(100% - 0px);
}

/* Overlay Start */
.overlay {
    position: fixed;
    width: 100%;
    height: 100%;
    content: "";
    left: 0;
    top: 0;
    background-color: hsl(var(--black)/0.6);
    z-index: 99;
    transition: 0.2s linear;
    visibility: hidden;
    opacity: 0;
}

.overlay.show-overlay {
    visibility: visible;
    opacity: 1;
}

@media (max-width: 991px) {
    .side-overlay {
        position: fixed;
        width: 100%;
        height: 100%;
        content: "";
        left: 0;
        top: 0;
        background-color: hsl(var(--black)/0.6);
        z-index: 99;
        transition: 0.2s linear;
        visibility: hidden;
        opacity: 0;
    }

    .side-overlay.show {
        visibility: visible;
        opacity: 1;
    }
}
/* Overlay End */
/* ================================= Classes Css End =========================== */
/* ============================ Extend Css Start ======================== */
/* Cover image Css */
.cover-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Display Flex Css Start */
.flx-wrap {
    display: flex;
    flex-wrap: wrap;
}

.flx-align, .common-check {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}

.flx-center {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
}

.flx-between {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
}

/* Display Flex Css End */
/* Positioning Css Class Start */
.pa-content {
    position: absolute;
    content: "";
}

.top-center, .common-accordion .accordion-button[aria-expanded=true]::after, .common-accordion .accordion-button[aria-expanded=false]::after {
    top: 50%;
    transform: translateY(-50%);
}

.left-center {
    left: 50%;
    transform: translateX(-50%);
}

.top-left-center {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* Positioning Css Class End */
/* Font Size For responsive devices Start */
.font-10 {
    font-size: 0.625rem !important;
}

.font-11 {
    font-size: 0.6875rem !important;
}

.font-12 {
    font-size: 0.75rem !important;
}

.font-13 {
    font-size: 0.8125rem !important;
}

.font-14 {
    font-size: 0.875rem !important;
}

.font-15 {
    font-size: 0.9375rem !important;
}

.font-16 {
    font-size: 1rem !important;
}

.font-17 {
    font-size: 1.0625rem !important;
}

.font-18 {
    font-size: 1.125rem !important;
}

@media screen and (max-width: 1199px) {
    .font-18 {
        font-size: 1rem !important;
    }
}

@media screen and (max-width: 767px) {
    .font-18 {
        font-size: 0.9375rem !important;
    }
}

.font-19 {
    font-size: 1.1875rem !important;
}

.font-20 {
    font-size: 1.25rem !important;
}

@media screen and (max-width: 1199px) {
    .font-20 {
        font-size: 1.125rem !important;
    }
}

@media screen and (max-width: 767px) {
    .font-20 {
        font-size: 1rem !important;
    }
}

.font-21 {
    font-size: 1.3125rem !important;
}

.font-22 {
    font-size: 1.375rem !important;
}

.font-23 {
    font-size: 1.4375rem !important;
}

.font-24 {
    font-size: 1.5rem !important;
}

.font-25 {
    font-size: 1.5625rem !important;
}

.font-26 {
    font-size: 1.625rem !important;
}

.font-27 {
    font-size: 1.6875rem !important;
}

.font-28 {
    font-size: 1.75rem !important;
}

.font-29 {
    font-size: 1.8125rem !important;
}

.font-30 {
    font-size: 1.875rem !important;
}

.font-31 {
    font-size: 1.9375rem !important;
}

.font-32 {
    font-size: 2rem !important;
}

.font-33 {
    font-size: 2.0625rem !important;
}

.font-34 {
    font-size: 2.125rem !important;
}

.font-35 {
    font-size: 2.1875rem !important;
}

.font-36 {
    font-size: 2.25rem !important;
}

.font-37 {
    font-size: 2.3125rem !important;
}

.font-38 {
    font-size: 2.375rem !important;
}

.font-39 {
    font-size: 2.4375rem !important;
}

.font-40 {
    font-size: 2.5rem !important;
}

/* Font Size For responsive devices End */
/* ============================ Extend Css End ======================== */
/* ======================= WordPress Default Css Start =================== */
.admin-bar .default-header .sticky-wrapper.sticky {
    top: 32px;
}

.wp-block-search__label {
    position: relative;
    font-size: 24px;
    font-weight: 500 !important;
    font-family: var(--title-font);
    line-height: 1em;
    margin: -0.12em 0 25px 0;
}

p.has-drop-cap {
    margin-bottom: 20px;
}

.page--item p:last-child .alignright {
    clear: right;
}

.blog-title,
.pagi-title,
.breadcumb-title {
    word-break: break-word;
}

.blocks-gallery-caption,
.wp-block-embed figcaption,
.wp-block-image figcaption {
    color: var(--body-color);
}

.bypostauthor,
.gallery-caption {
    display: block;
}

.page-links,
.clearfix {
    clear: both;
}

.page--item {
    margin-bottom: 30px;
}

.page--item p {
    line-height: 1.8;
}

.page--item .th-comment-form {
    padding: 0;
}

.page--item .th-comments-wrap {
    margin-left: 0;
    margin-right: 0;
    margin-top: 30px;
}

.content-none-search {
    margin-top: 30px;
}

.wp-block-button.aligncenter {
    text-align: center;
}

.alignleft {
    display: inline;
    float: left;
    margin-bottom: 10px;
    margin-right: 1.5em;
}

.alignright {
    display: inline;
    float: right;
    margin-bottom: 10px;
    margin-left: 1.5em;
    margin-right: 1em;
}

.aligncenter {
    clear: both;
    display: block;
    margin-left: auto;
    margin-right: auto;
    max-width: 100%;
}

.gallery {
    margin-bottom: 1.5em;
    width: 100%;
}

.gallery-item {
    display: inline-block;
    text-align: center;
    vertical-align: top;
    width: 100%;
    padding: 0 5px;
}

.wp-block-columns {
    margin-bottom: 1em;
}

figure.gallery-item {
    margin-bottom: 10px;
    display: inline-block;
}

figure.wp-block-gallery {
    margin-bottom: 14px;
}

.gallery-columns-2 .gallery-item {
    max-width: 50%;
}

.gallery-columns-3 .gallery-item {
    max-width: 33.33%;
}

.gallery-columns-4 .gallery-item {
    max-width: 25%;
}

.gallery-columns-5 .gallery-item {
    max-width: 20%;
}

.gallery-columns-6 .gallery-item {
    max-width: 16.66%;
}

.gallery-columns-7 .gallery-item {
    max-width: 14.28%;
}

.gallery-columns-8 .gallery-item {
    max-width: 12.5%;
}

.gallery-columns-9 .gallery-item {
    max-width: 11.11%;
}

.gallery-caption {
    display: block;
    font-size: 12px;
    color: var(--body-color);
    line-height: 1.5;
    padding: 0.5em 0;
}

.wp-block-cover p:not(.has-text-color),
.wp-block-cover-image-text,
.wp-block-cover-text {
    color: var(--white-color);
}

.wp-block-cover {
    margin-bottom: 15px;
}

.wp-caption-text {
    text-align: center;
}

.wp-caption {
    margin-bottom: 1.5em;
    max-width: 100%;
}

.wp-caption .wp-caption-text {
    margin: 0.5em 0;
    font-size: 14px;
}

.wp-block-media-text,
.wp-block-media-text.alignwide,
figure.wp-block-gallery {
    margin-bottom: 30px;
}

.wp-block-media-text.alignwide {
    background-color: var(--smoke-color);
}

.editor-styles-wrapper .has-large-font-size,
.has-large-font-size {
    line-height: 1.4;
}

.wp-block-latest-comments a {
    color: inherit;
}

.wp-block-button {
    margin-bottom: 10px;
}

.wp-block-button:last-child {
    margin-bottom: 0;
}

.wp-block-button .wp-block-button__link {
    color: #fff;
}

.wp-block-button .wp-block-button__link:hover {
    color: #fff;
    background-color: var(--theme-color);
}

.wp-block-button.is-style-outline .wp-block-button__link {
    background-color: transparent;
    border-color: var(--title-color);
    color: var(--title-color);
}

.wp-block-button.is-style-outline .wp-block-button__link:hover {
    color: #fff;
    background-color: var(--theme-color);
    border-color: var(--theme-color);
}

.wp-block-button.is-style-squared .wp-block-button__link {
    border-radius: 0;
}

ol.wp-block-latest-comments li {
    margin: 15px 0;
}

ul.wp-block-latest-posts {
    padding: 0;
    margin: 0;
    margin-bottom: 15px;
}

ul.wp-block-latest-posts a {
    color: inherit;
}

ul.wp-block-latest-posts a:hover {
    color: var(--theme-color);
}

ul.wp-block-latest-posts li {
    margin: 15px 0;
}

.wp-block-search {
    display: flex;
    flex-wrap: wrap;
}

.wp-block-search .wp-block-search__inside-wrapper {
    border: 1px solid #EAEBEE;
    border-radius: 10px 0 0 10px;
    overflow: hidden;
}

.wp-block-search .wp-block-search__input {
    width: 100%;
    max-width: 100%;
    padding-left: 20px;
    border: 0;
}

.wp-block-search .wp-block-search__button {
    margin: 0;
    min-width: 110px;
    border: none;
    color: #fff;
    border-radius: 10px;
    background-color: var(--theme-color);
}

.wp-block-search .wp-block-search__button.has-icon {
    min-width: 55px;
}

.wp-block-search .wp-block-search__button:hover {
    background-color: var(--title-color);
}

.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper {
    padding: 0;
    border: none;
}

.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper .wp-block-search__input {
    padding: 0 8px 0 25px;
}

ul.wp-block-rss a {
    color: inherit;
}

.wp-block-group.has-background {
    padding: 15px 15px 1px;
    margin-bottom: 30px;
}

.wp-block-table td,
.wp-block-table th {
    border-color: rgba(0, 0, 0, 0.1);
}

.wp-block-table.is-style-stripes {
    border: 1px solid rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.wp-block-table.is-style-stripes {
    border: 0;
    margin-bottom: 30px;
    border-bottom: 0;
}

.wp-block-table.is-style-stripes th, .wp-block-table.is-style-stripes td {
    border-color: hsl(var(--border-color));
}

.logged-in .will-sticky .sticky-active.active,
.logged-in .preloader .btn {
    top: 32px;
}

@media (max-width: 782px) {
    .logged-in .will-sticky .sticky-active.active,
.logged-in .preloader .btn {
        top: 46px;
    }
}

@media (max-width: 600px) {
    .logged-in .will-sticky .sticky-active.active,
.logged-in .preloader .btn {
        top: 0;
    }
}

.post-password-form {
    margin-bottom: 30px;
    margin-top: 20px;
}

.post-password-form p {
    display: flex;
    position: relative;
    gap: 15px;
  /* Extra small devices */
}

@media (max-width: 575px) {
    .post-password-form p {
        flex-wrap: wrap;
    }
}

.post-password-form label {
    display: flex;
    align-items: center;
    flex: auto;
    margin-bottom: 0;
    line-height: 1;
    margin-top: 0;
    gap: 15px;
  /* Extra small devices */
}

@media (max-width: 575px) {
    .post-password-form label {
        flex-wrap: wrap;
    }
}

.post-password-form input {
    width: 100%;
    border: none;
    height: 55px;
    padding-left: 25px;
    color: var(--body-color);
    border: 1px solid hsl(var(--border-color));
}

.post-password-form input[type=submit] {
    padding-left: 0;
    padding-right: 0;
    margin: 0;
    width: 140px;
    border: none;
    color: #fff;
    background-color: var(--theme-color);
    text-align: center;
}

.post-password-form input[type=submit]:hover {
    background-color: var(--title-color);
}

.page-links {
    clear: both;
    margin: 0 0 1.5em;
    padding-top: 1em;
}

.page-links > .page-links-title {
    margin-right: 10px;
}

.page-links > span:not(.page-links-title):not(.screen-reader-text),
.page-links > a {
    display: inline-block;
    padding: 5px 13px;
    background-color: var(--white-color);
    color: var(--title-color);
    border: 1px solid rgba(0, 0, 0, 0.08);
    margin-right: 10px;
}

.page-links > span:not(.page-links-title):not(.screen-reader-text):hover,
.page-links > a:hover {
    opacity: 0.8;
    color: var(--white-color);
    background-color: var(--theme-color);
    border-color: transparent;
}

.page-links > span:not(.page-links-title):not(.screen-reader-text).current,
.page-links > a.current {
    background-color: var(--theme-color);
    color: var(--white-color);
    border-color: transparent;
}

.page-links span.screen-reader-text {
    display: none;
}

.blog-single .wp-block-archives-dropdown {
    margin-bottom: 30px;
}

.blog-single.format-quote, .blog-single.format-link, .blog-single.tag-sticky-2, .blog-single.sticky {
    position: relative;
}

.blog-single.format-quote .blog-content, .blog-single.format-link .blog-content, .blog-single.tag-sticky-2 .blog-content, .blog-single.sticky .blog-content {
    background-color: var(--smoke-color);
    border: none;
    padding: 40px;
    border-radius: 15px;
}

.blog-single.format-quote .blog-content:before, .blog-single.format-link .blog-content:before, .blog-single.tag-sticky-2 .blog-content:before, .blog-single.sticky .blog-content:before {
    display: none;
}

.blog-single.format-quote:before, .blog-single.format-link:before, .blog-single.tag-sticky-2:before, .blog-single.sticky:before {
    content: "\f0c1";
    position: absolute;
    font-family: "Font Awesome 6 Pro";
    font-size: 5rem;
    opacity: 0.3;
    right: 15px;
    line-height: 1;
    top: 15px;
    color: var(--theme-color);
    z-index: 1;
}

.blog-single.tag-sticky-2::before, .blog-single.sticky::before {
    content: "\f08d";
    position: absolute;
    font-family: var(--icon-font);
    font-size: 16px;
    font-weight: 500;
    opacity: 1;
    right: 0;
    top: 0;
    color: var(--white-color);
    background-color: var(--theme-color);
    z-index: 1;
    height: 44px;
    width: 44px;
    line-height: 44px;
    text-align: center;
    border-radius: 0 15px 0 4px;
}

.blog-single.format-quote blockquote, .blog-single.format-quote .wp-block-quote {
    background: var(--white-color);
    margin-bottom: 0;
}

.blog-single.format-quote:before {
    content: "\f10e";
    top: 0;
}

.blog-single .blog-content .wp-block-categories-dropdown.wp-block-categories,
.blog-single .blog-content .wp-block-archives-dropdown {
    display: block;
    margin-bottom: 30px;
}

.blog-single.format-chat .entry-content > p:nth-child(2n) {
    background: var(--smoke-color);
    padding: 5px 20px;
}

.blog-details .blog-single:before {
    display: none;
}

.blog-details .blog-single .blog-content {
    background-color: transparent;
    overflow: hidden;
}

.blog-details .blog-single .blog-content p:last-child {
    margin-bottom: 0;
}

.blog-details .blog-single.format-chat .blog-meta {
    margin-bottom: 20px;
}

.blog-details .blog-single.format-chat .blog-content > p:nth-child(2n) {
    background: var(--smoke-color);
    padding: 5px 20px;
}

.blog-details .blog-single.tag-sticky-2, .blog-details .blog-single.sticky, .blog-details .blog-single.format-quote, .blog-details .blog-single.format-link {
    box-shadow: none;
    background-color: transparent;
}

.blog-details .blog-single.tag-sticky-2:before, .blog-details .blog-single.sticky:before, .blog-details .blog-single.format-quote:before, .blog-details .blog-single.format-link:before {
    display: none;
}

.blog-single .wp-block-tag-cloud {
    margin-bottom: 20px;
}

.blog-single .wp-block-tag-cloud a {
    background-color: var(--smoke-color);
    color: var(--title-color);
    box-shadow: none;
}

.th-search {
    background-color: #f3f3f3;
    margin-bottom: 30px;
    border: 1px solid #f3f3f3;
}

.th-search .search-grid-content {
    padding: 30px;
  /* Small devices */
}

@media (max-width: 767px) {
    .th-search .search-grid-content {
        padding: 20px;
    }
}

.th-search .search-grid-title {
    font-size: 20px;
    margin-bottom: 5px;
    margin-top: 0;
}

.th-search .search-grid-title a {
    color: inherit;
}

.th-search .search-grid-title a:hover {
    color: var(--theme-color);
}

.th-search .search-grid-meta > * {
    display: inline-block;
    margin-right: 15px;
    font-size: 14px;
}

.th-search .search-grid-meta > *:last-child {
    margin-right: 0;
}

.th-search .search-grid-meta a,
.th-search .search-grid-meta span {
    color: var(--body-color);
}

/* Large devices */
@media (max-width: 1199px) {
    .blog-single.format-quote:before, .blog-single.format-link:before, .blog-single.tag-sticky-2:before, .blog-single.sticky:before {
        font-size: 14px;
        padding: 8px 16px;
    }

    .blog-single.format-quote:before {
        top: 15px;
    }
}
/* Small devices */
@media (max-width: 767px) {
    .blog-single.format-quote:before, .blog-single.format-link:before, .blog-single.tag-sticky-2:before, .blog-single.sticky:before {
        font-size: 14px;
        padding: 8px 16px;
    }
}

@media (max-width: 768px) {
    .wp-block-latest-comments {
        padding-left: 10px;
    }

    .page--content.clearfix + .th-comment-form {
        margin-top: 24px;
    }
}

.site {
    overflow-x: visible;
}

/* ======================= WordPress Default Css End =================== */
/* ================================= Typography Css Start =========================== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--body-font);
    color: hsl(var(--body-color));
    word-break: break-word;
    background-color: hsl(var(--white));
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

@media screen and (max-width: 767px) {
    body {
        font-size: 0.9375rem;
    }
}

@media screen and (max-width: 575px) {
    body {
        font-size: 0.875rem;
    }
}

p {
    font-size: inherit;
    font-weight: 400;
    margin: 0;
    line-height: 1.6;
}

span {
    display: inline-block;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    margin: 0 0 16px 0;
    font-family: var(--heading-font);
    color: hsl(var(--heading-color));
    line-height: 1.2;
    font-weight: 700;
}

h1 {
    font-size: var(--heading-one);
}

h2 {
    font-size: var(--heading-two);
}

h3 {
    font-size: var(--heading-three);
}

h4 {
    font-size: var(--heading-four);
}

h5 {
    font-size: var(--heading-five);
}

h6 {
    font-size: var(--heading-six);
    font-weight: 800;
}

h1 > a,
h2 > a,
h3 > a,
h4 > a,
h5 > a,
h6 > a {
    font-weight: inherit;
    font-size: inherit;
    color: inherit;
    transition: 0.2s linear;
    line-height: inherit;
}

h1 > a:hover,
h2 > a:hover,
h3 > a:hover,
h4 > a:hover,
h5 > a:hover,
h6 > a:hover {
    color: hsl(var(--main));
}

a {
    display: inline-block;
    transition: 0.2s linear;
    text-decoration: none;
    color: #0661e9;
}

a:hover {
    color: #1d72f2;
}

img {
    max-width: 100%;
    height: auto;
}

select {
    cursor: pointer;
}

ul,
ol {
    padding: 0;
    margin: 0;
    list-style: none;
}

button {
    border: 0;
    background-color: transparent;
}

button:focus {
    outline: none;
    box-shadow: none;
}

.form-select:focus {
    outline: 0;
    box-shadow: none;
}

/* ================================= Typography Css End =========================== */
/* ================================= Margin Css Start =========================== */
.mt--100 {
    margin-top: -100px !important;
}

.mt--24 {
    margin-top: -24px !important;
}

.mb-64 {
    margin-bottom: clamp(2rem, -0.434rem + 5.071vw, 4rem) !important;
}

.mt-64 {
    margin-top: clamp(2rem, -0.434rem + 5.071vw, 4rem) !important;
}

.mt-48 {
    margin-top: clamp(1.5rem, -0.326rem + 3.803vw, 3rem) !important;
}

.mb-48 {
    margin-bottom: clamp(1.5rem, -0.326rem + 3.803vw, 3rem) !important;
}

.mt-40 {
    margin-top: clamp(1.5rem, 0.283rem + 2.536vw, 2.5rem) !important;
}

.mb-40 {
    margin-bottom: clamp(1.5rem, 0.283rem + 2.536vw, 2.5rem) !important;
}

.mt-32 {
    margin-top: 32px !important;
}

@media screen and (max-width: 1199px) {
    .mt-32 {
        margin-top: 20px;
    }
}

.mb-32 {
    margin-bottom: 32px !important;
}

@media screen and (max-width: 1199px) {
    .mb-32 {
        margin-bottom: 20px;
    }
}

.mt-24 {
    margin-top: 24px !important;
}

@media screen and (max-width: 1199px) {
    .mt-24 {
        margin-top: 16px;
    }
}

.mb-24 {
    margin-bottom: 24px !important;
}

@media screen and (max-width: 1199px) {
    .mb-24 {
        margin-bottom: 16px;
    }
}

.margin-y-120 {
    margin-top: 60px;
    margin-bottom: 60px;
}

@media (min-width: 576px) {
    .margin-y-120 {
        margin-top: 80px;
        margin-bottom: 80px;
    }
}

@media (min-width: 992px) {
    .margin-y-120 {
        margin-top: 120px;
        margin-bottom: 120px;
    }
}

.margin-t-120 {
    margin-top: 60px;
}

@media (min-width: 576px) {
    .margin-t-120 {
        margin-top: 80px;
    }
}

@media (min-width: 992px) {
    .margin-t-120 {
        margin-top: 120px;
    }
}

.margin-b-120 {
    margin-bottom: 60px;
}

@media (min-width: 576px) {
    .margin-b-120 {
        margin-bottom: 80px;
    }
}

@media (min-width: 992px) {
    .margin-b-120 {
        margin-bottom: 120px;
    }
}

.margin-y-60 {
    margin-top: 30px;
    margin-bottom: 30px;
}

@media (min-width: 576px) {
    .margin-y-60 {
        margin-top: 40px;
        margin-bottom: 40px;
    }
}

@media (min-width: 992px) {
    .margin-y-60 {
        margin-top: 60px;
        margin-bottom: 60px;
    }
}

.margin-t-60 {
    margin-top: 30px;
}

@media (min-width: 576px) {
    .margin-t-60 {
        margin-top: 40px;
    }
}

@media (min-width: 992px) {
    .margin-t-60 {
        margin-top: 60px;
    }
}

.margin-b-60 {
    margin-bottom: 30px;
}

@media (min-width: 576px) {
    .margin-b-60 {
        margin-bottom: 40px;
    }
}

@media (min-width: 992px) {
    .margin-b-60 {
        margin-bottom: 60px;
    }
}
/* ================================= Margin Css End =========================== */
/* ================================= Padding Css Start =========================== */
.padding-y-120 {
    padding-top: 60px;
    padding-bottom: 60px;
}

@media (min-width: 576px) {
    .padding-y-120 {
        padding-top: 80px;
        padding-bottom: 80px;
    }
}

@media (min-width: 992px) {
    .padding-y-120 {
        padding-top: 120px;
        padding-bottom: 120px;
    }
}

.padding-t-120 {
    padding-top: 60px;
}

@media (min-width: 576px) {
    .padding-t-120 {
        padding-top: 80px;
    }
}

@media (min-width: 992px) {
    .padding-t-120 {
        padding-top: 120px;
    }
}

.padding-b-120 {
    padding-bottom: 60px;
}

@media (min-width: 576px) {
    .padding-b-120 {
        padding-bottom: 80px;
    }
}

@media (min-width: 992px) {
    .padding-b-120 {
        padding-bottom: 120px;
    }
}

.padding-y-60 {
    padding-top: 30px;
    padding-bottom: 30px;
}

@media (min-width: 576px) {
    .padding-y-60 {
        padding-top: 40px;
        padding-bottom: 40px;
    }
}

@media (min-width: 992px) {
    .padding-y-60 {
        padding-top: 60px;
        padding-bottom: 60px;
    }
}

.padding-t-60 {
    padding-top: 30px;
}

@media (min-width: 576px) {
    .padding-t-60 {
        padding-top: 40px;
    }
}

@media (min-width: 992px) {
    .padding-t-60 {
        padding-top: 60px;
    }
}

.padding-b-60 {
    padding-bottom: 30px;
}

@media (min-width: 576px) {
    .padding-b-60 {
        padding-bottom: 40px;
    }
}

@media (min-width: 992px) {
    .padding-b-60 {
        padding-bottom: 60px;
    }
}
/* ================================= Padding Css End =========================== */
/* =========================== Accordion Css start ============================= */
.common-accordion .accordion-item {
    border: 1px solid hsl(var(--border-color));
    background-color: hsl(var(--white)) !important;
    border-radius: 8px;
    overflow: hidden;
}

.common-accordion .accordion-item:not(:last-child) {
    margin-bottom: 20px;
}

.common-accordion .accordion-header {
    line-height: 1;
}

.common-accordion .accordion-body {
    padding: 20px 30px;
    padding-top: 0;
}

@media screen and (max-width: 575px) {
    .common-accordion .accordion-body {
        padding: 12px 20px;
        padding-top: 0;
    }
}

.common-accordion:first-of-type .accordion-button.collapsed {
    border-radius: 5px;
}

.common-accordion:last-of-type .accordion-button.collapsed {
    border-radius: 5px;
}

.common-accordion .accordion-button {
    color: var(--heading-color);
    font-size: 1.125rem;
    padding: 20px 30px;
    padding-right: 46px;
    font-weight: 600;
    font-size: clamp(1rem, 0.696rem + 0.634vw, 1.25rem);
    font-family: var(--heading-font);
}

@media screen and (max-width: 575px) {
    .common-accordion .accordion-button {
        padding: 12px 20px;
        padding-right: 36px;
    }
}

.common-accordion .accordion-button::after {
    background-image: none;
}

.common-accordion .accordion-button:focus {
    box-shadow: none;
}

.common-accordion .accordion-button:not(.collapsed) {
    background-color: transparent !important;
    box-shadow: none;
    color: hsl(var(--main));
}

.common-accordion .accordion-button:not(.collapsed)::after {
    background-image: none;
    color: hsl(var(--main));
}

.common-accordion .accordion-button[aria-expanded=true]::after, .common-accordion .accordion-button[aria-expanded=false]::after {
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    content: "\f106";
    color: hsl(var(--main));
    display: inline-block;
    position: absolute;
    right: 30px;
    height: unset;
    font-size: 1rem;
    text-align: center;
}

@media screen and (max-width: 575px) {
    .common-accordion .accordion-button[aria-expanded=true]::after, .common-accordion .accordion-button[aria-expanded=false]::after {
        right: 20px;
    }
}

.common-accordion .accordion-button[aria-expanded=false]::after {
    content: "\f107";
    color: hsl(var(--heading-color));
}

/* ================================= Accordion Css End =========================== */
/* ================================= Button Css Start =========================== */
.btn-check:checked + .btn, .btn.active, .btn.show, .btn:first-child:active, :not(.btn-check) + .btn:active {
    color: none;
    background-color: none;
    border-color: none;
}

.btn {
    position: relative;
    border-radius: 8px;
    border: 1px solid transparent;
    font-weight: 400;
    font-family: var(--body-font);
    color: hsl(var(--white)) !important;
    z-index: 1;
    font-size: 0.875rem;
    line-height: 1;
    text-transform: capitalize;
    padding: 14px 16px;
}

.btn:hover, .btn:focus, .btn:focus-visible {
    box-shadow: none !important;
}

@media screen and (max-width: 991px) {
    .btn {
        padding: 12px 16px;
    }
}

.btn-sm {
    padding: 11px 16px !important;
    font-size: 0.875rem;
}

@media screen and (max-width: 424px) {
    .btn-sm {
        padding: 10px !important;
        font-size: 12px;
    }
}

.btn-md {
    padding: 17px 16px;
}

@media screen and (max-width: 424px) {
    .btn-md {
        padding: 10px;
        font-size: 12px;
    }
}

.btn-lg {
    padding: 22px 40px;
    font-size: 1rem;
}

@media screen and (max-width: 991px) {
    .btn-lg {
        padding: 17px 32px;
    }
}

.btn-lg-icon {
    height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
}

@media screen and (max-width: 991px) {
    .btn-lg-icon {
        height: 50px;
    }
}

.btn-xl {
    padding: 20px 64px;
    font-size: 1rem;
}

@media screen and (max-width: 991px) {
    .btn-xl {
        padding: 15px 36px;
    }
}

.btn-icon {
    width: 56px;
    height: 56px;
    background-color: hsl(var(--white)) !important;
    padding: 0;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: 0.2s linear;
    font-size: 1.25rem;
}

@media screen and (max-width: 767px) {
    .btn-icon {
        width: 44px;
        height: 44px;
    }
}

.btn-icon--sm {
    width: 44px;
    height: 44px;
}

@media screen and (max-width: 767px) {
    .btn-icon--sm {
        width: 40px;
        height: 40px;
    }
}

.btn-icon:hover {
    transform: scale(1.04);
}

.btn .icon-right {
    margin-left: 8px;
    font-size: 1rem;
}

.btn .icon-left {
    margin-right: 8px;
    font-size: 1rem;
}

.btn:active {
    transform: scale(1.01);
}

.btn-main {
    background-color: hsl(var(--main)) !important;
    border: transparent !important;
    z-index: 1;
}

.btn-main::before, .btn-main::after {
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    border-radius: inherit;
    background: var(--main-gradient);
    z-index: -1;
    transition: 0.2s linear;
}

.btn-main::after {
    background: var(--main-gradient-rev);
    visibility: hidden;
    opacity: 0;
}

.btn-main:hover::after {
    visibility: visible;
    opacity: 1;
}

.btn-outline-main {
    background-color: transparent !important;
    border-color: hsl(var(--main)) !important;
    color: hsl(var(--main)) !important;
}

.btn-outline-main:hover {
    background-color: hsl(var(--main)/0.15) !important;
    border-color: hsl(var(--main)/0.4) !important;
}

.btn-white {
    background-color: hsl(var(--white)) !important;
    border-color: hsl(var(--white)) !important;
    color: hsl(var(--black)) !important;
}

.btn-white:hover {
    background-color: hsl(var(--main)) !important;
    border-color: hsl(var(--main)) !important;
    color: hsl(var(--white)) !important;
}

.btn-light {
    background-color: hsl(var(--white-one)) !important;
    border-color: hsl(var(--white-one)) !important;
    color: hsl(var(--black)) !important;
}

.btn-light:hover {
    background-color: hsl(var(--main)) !important;
    border-color: hsl(var(--main)) !important;
    color: hsl(var(--white)) !important;
}

.btn-outline-light {
    background-color: transparent !important;
    border-color: hsl(var(--white-one)) !important;
    color: hsl(var(--black)) !important;
    font-weight: 500;
}

.btn-outline-light:hover {
    background-color: hsl(var(--black)) !important;
    border-color: hsl(var(--black)) !important;
    color: hsl(var(--white)) !important;
}

.btn-black {
    background-color: hsl(var(--black)) !important;
    border-color: hsl(var(--black)) !important;
    border-width: 2px;
    color: hsl(var(--white)) !important;
}

.btn-black:hover {
    background-color: transparent !important;
    border-color: hsl(var(--black)) !important;
    color: hsl(var(--black)) !important;
    font-weight: 600 !important;
}

.btn-outline-black {
    background-color: transparent !important;
    border-color: hsl(var(--black)) !important;
    border-width: 2px;
    color: hsl(var(--black)) !important;
    font-weight: 600;
}

.btn-outline-black:hover {
    background-color: hsl(var(--black)) !important;
    border-color: hsl(var(--black)) !important;
    color: hsl(var(--white)) !important;
}

/* ================================= Button Css End =========================== */
/* ================================= Form Css Start =========================== */
/* input Start */
.common-input {
    border-radius: 5px;
    font-weight: 400;
    outline: none;
    width: 100%;
    padding: 17px 16px;
    background-color: transparent !important;
    border: 1px solid hsl(var(--border-color));
    color: hsl(var(--black));
    line-height: 1;
    font-size: 1.0625rem;
}

@media screen and (max-width: 991px) {
    .common-input {
        padding: 10px 16px;
    }
}

.common-input::placeholder {
    color: hsl(var(--black-three));
    font-size: 0.875rem;
    transition: 0.25s linear;
    font-weight: 400;
}

.common-input--md {
    padding: 13px 16px;
    font-size: 0.9375rem;
}

.common-input--lg {
    padding: 25px 24px;
}

@media screen and (max-width: 767px) {
    .common-input--lg {
        padding: 18px 24px;
    }
}

.common-input:focus {
    border-color: hsl(var(--main));
    box-shadow: none;
}

.common-input:focus::placeholder {
    visibility: hidden;
    opacity: 0;
}

.common-input:disabled, .common-input[readonly] {
    background-color: hsl(var(--black)/0.2);
    opacity: 1;
    border: 0;
}

.common-input[type=password] {
    color: hsl(var(--black)/0.5);
}

.common-input[type=password]:focus {
    color: hsl(var(--black));
}

.common-input--withIcon {
    padding-right: 50px !important;
}

.common-input--withLeftIcon {
    padding-left: 50px !important;
}

.common-input--bg {
    background-color: hsl(var(--gray-seven)) !important;
    border-color: hsl(var(--gray-seven)) !important;
}

.common-input--bg::placeholder {
    color: hsl(var(--black-three));
}

/* input End */
/* input icon */
.input-icon {
    position: absolute;
    right: 24px;
    top: 50%;
    transform: translateY(-50%);
    color: hsl(var(--heading-color));
    font-size: 0.875rem;
}

.input-icon--left {
    left: 20px;
    right: auto;
}

textarea + .input-icon {
    top: 15px;
    transform: translateY(0);
}

/* input icon */
/* Label */
.form-label {
    margin-bottom: 6px;
    font-size: 0.875rem;
    color: hsl(var(--heading-color));
    font-weight: 500;
}

/* Form Select */
.select-has-icon {
    position: relative;
}

.select-has-icon::before {
    position: absolute;
    content: "\f107";
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    font-size: 0.75rem;
    color: hsl(var(--main));
    transition: 0.2s linear;
    pointer-events: none;
    color: hsl(var(--body-color)) !important;
}

.select-has-icon.icon-black::before {
    -webkit-text-fill-color: hsl(var(--black));
}

.select-has-icon select {
    background-image: none;
    -webkit-appearance: none;
    padding: 19px 24px;
    font-size: 1rem;
}

@media screen and (max-width: 991px) {
    .select-has-icon select {
        padding: 12px 24px;
    }
}

.select-has-icon .common-input {
    padding-right: 30px !important;
}

.select {
    color: hsl(var(--black)/0.6) !important;
    padding: 16px 24px;
    background-color: transparent;
}

@media screen and (max-width: 991px) {
    .select {
        padding: 11px 24px;
    }
}

@media screen and (max-width: 767px) {
    .select {
        padding: 9px 24px;
        font-size: 0.9375rem;
    }
}

.select:focus {
    border-color: hsl(var(--main));
    color: hsl(var(--black)) !important;
    outline: none;
}

.select option {
    background-color: hsl(var(--white));
    color: hsl(var(--heading-color));
}

/* Form Select End */
textarea.common-input {
    height: 140px;
}

@media screen and (max-width: 767px) {
    textarea.common-input {
        height: 110px;
    }
}
/* Autofill Css */
input:-webkit-autofill, input:-webkit-autofill:hover, input:-webkit-autofill:focus, input:-webkit-autofill:active,
textarea:-webkit-autofill, textarea:-webkit-autofill:hover, textarea:-webkit-autofill:focus, textarea:-webkit-autofill:active {
    -webkit-transition: background-color 5000s ease-in-out 0s;
    transition: background-color 5000s ease-in-out 0s;
}

input:-webkit-autofill, textarea:-webkit-autofill, select:-webkit-autofill, textarea:-webkit-autofill, textarea:-webkit-autofill, textarea:-webkit-autofill {
    -webkit-box-shadow: 0 0 0px 1000px transparent inset;
    -webkit-text-fill-color: hsl(var(--heading-color)) !important;
    caret-color: hsl(var(--heading-color));
}

/* Autofill Css End */
/* Show Hide Password */
input#your-password, input#confirm-password {
    padding-right: 50px;
}

.password-show-hide {
    position: absolute;
    right: 20px;
    z-index: 5;
    cursor: pointer;
    top: 50%;
    transform: translateY(-50%);
    color: hsl(var(--black)/0.4);
}

/* Number Arrow None */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
}

input[type=number] {
    -moz-appearance: textfield;
}

/* Custom Checkbox & Radio Css Start */
.common-check {
    margin-bottom: 16px;
}

.common-check:last-child {
    margin-bottom: 0;
}

.common-check a {
    display: inline;
}

.common-check.common-radio .form-check-input {
    border-radius: 50%;
}

.common-check.common-radio .form-check-input:checked {
    background-color: transparent !important;
}

.common-check.common-radio .form-check-input:checked::after {
    visibility: visible;
    opacity: 1;
    -webkit-transform: translate(-50%, -50%) scale(1);
    transform: translate(-50%, -50%) scale(1);
}

.common-check .form-check-input {
    transition: 0.2s linear;
    box-shadow: none;
    background-color: transparent;
    box-shadow: none !important;
    border: 0;
    position: relative;
    border-radius: 3px;
    width: 18px;
    height: 18px;
    border: 1px solid hsl(var(--black)/0.4);
    cursor: pointer;
    transition: 0.2s linear;
    margin-top: 0;
}

.common-check .form-check-input::before {
    position: absolute;
    content: "\f00c";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    color: hsl(var(--white));
    font-size: 0.6875rem;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: 0.2s linear;
    visibility: hidden;
    opacity: 0;
}

.common-check .form-check-input::after {
    position: absolute;
    content: "";
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%) scale(0.2);
    transform: translate(-50%, -50%) scale(0.2);
    width: 10px;
    height: 10px;
    background-color: hsl(var(--main));
    border-radius: 50%;
    transition: 0.2s linear;
    z-index: 1;
    visibility: hidden;
    opacity: 0;
}

.common-check .form-check-input:checked {
    background-color: hsl(var(--main)) !important;
    border-color: hsl(var(--main)) !important;
    box-shadow: none;
}

.common-check .form-check-input:checked[type=checkbox] {
    background-image: none;
}

.common-check .form-check-input:checked::before {
    visibility: visible;
    opacity: 1;
}

.common-check .form-check-label {
    font-weight: 500;
    width: calc(100% - 18px);
    padding-left: 12px;
    cursor: pointer;
    font-size: 0.875rem;
    color: hsl(var(--heading-color));
    font-family: var(--poppins-font);
}

@media screen and (max-width: 424px) {
    .common-check label {
        font-size: 0.9375rem;
    }
}

@media screen and (max-width: 424px) {
    .common-check a {
        font-size: 0.9375rem;
    }
}
/* Custom Checkbox & Radio Css End */
/* ================================= Form Css End =========================== */
/* ================================= Pagination Css Start =========================== */
.common-pagination {
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 64px;
    gap: 8px;
}

@media screen and (max-width: 1199px) {
    .common-pagination {
        margin-top: 48px;
    }
}

@media screen and (max-width: 991px) {
    .common-pagination {
        margin-top: 40px;
    }
}

@media screen and (max-width: 767px) {
    .common-pagination {
        margin-top: 32px;
    }
}

.common-pagination .page-item.active .page-link {
    color: hsl(var(--white));
    background-color: hsl(var(--black));
}

.common-pagination .page-item .page-link {
    border-radius: 8px;
    width: 42px;
    height: 43px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: transparent;
    font-weight: 600;
    padding: 0;
    color: hsl(var(--heading-color));
    border: 1px solid hsl(var(--black)/0.3);
    font-size: 1rem;
}

@media screen and (max-width: 991px) {
    .common-pagination .page-item .page-link {
        width: 48px;
        height: 48px;
    }
}

.common-pagination .page-item .page-link:hover, .common-pagination .page-item .page-link:focus, .common-pagination .page-item .page-link:focus-visible {
    color: hsl(var(--white));
    background-color: hsl(var(--black));
}

.common-pagination .page-item .page-link:focus {
    box-shadow: none;
}

.common-pagination .page-item:last-child .page-link {
    width: unset;
    height: unset;
    background-color: hsl(var(--section-bg));
    border-color: hsl(var(--section-bg));
    padding: 9px 16px;
}

.common-pagination .page-item:last-child .page-link:hover, .common-pagination .page-item:last-child .page-link:focus, .common-pagination .page-item:last-child .page-link:focus-visible {
    color: hsl(var(--white));
    background-color: hsl(var(--black));
}

/* ================================= Pagination Css End =========================== */
/* ================================= Tab Css Start =========================== */
.common-tab {
    margin-bottom: 20px;
    gap: 16px;
  /* Style Icon */
}

@media screen and (max-width: 991px) {
    .common-tab {
        gap: 8px;
    }
}

.common-tab .nav-item {
    border-bottom: 0;
}

.common-tab .nav-item .nav-link {
    color: hsl(var(--heading-color));
    padding: 7px 16px !important;
    background-color: transparent;
    transition: 0.2s linear;
    border-radius: 8px;
    border: 1px solid hsl(var(--gray-five));
    text-transform: capitalize;
    font-weight: 500;
    overflow: hidden;
    font-size: 0.875rem;
}

@media screen and (max-width: 991px) {
    .common-tab .nav-item .nav-link {
        padding: 5px 10px !important;
    }
}

.common-tab .nav-item .nav-link.active {
    color: hsl(var(--white)) !important;
    background-color: hsl(var(--heading-color)) !important;
    border-color: hsl(var(--heading-color)) !important;
    font-weight: 300;
}

.common-tab .nav-item .nav-link:hover {
    color: hsl(var(--main));
    background-color: hsl(var(--white));
}

.common-tab.style-icon .nav-item .nav-link {
    padding: 0 !important;
    border: 0 !important;
    background-color: transparent !important;
    overflow: unset;
}

.common-tab.style-icon .nav-item .nav-link .tech-list__item {
    box-shadow: 0px 5px 10px 0px rgba(204, 204, 204, 0.25);
}

.common-tab.style-icon .nav-item .nav-link .tech-list__item .text {
    display: none;
}

.common-tab.style-icon .nav-item .nav-link.active .tech-list__item {
    padding: 10px;
    height: unset;
    width: unset;
    border-radius: 50px !important;
    border: 1px solid hsl(var(--main));
    background-color: hsl(var(--white));
    box-shadow: 0px 5px 10px 0px rgba(204, 204, 204, 0.25);
    color: hsl(var(--heading-color));
    font-weight: 500;
}

.common-tab.style-icon .nav-item .nav-link.active .tech-list__item .text {
    display: block;
}

/* Style Gradient */
.tab-gradient {
    display: flex;
    align-items: center;
    gap: 16px;
}

.tab-gradient .nav-item .nav-link {
    background-color: transparent !important;
    position: relative;
    z-index: 1;
    color: hsl(var(--heading-color));
    font-size: 1.125rem;
    text-transform: capitalize;
    font-weight: 400;
    padding: 14px 50px;
}

@media screen and (max-width: 1199px) {
    .tab-gradient .nav-item .nav-link {
        padding: 10px 30px;
        font-size: 1rem;
    }
}

.tab-gradient .nav-item .nav-link::before, .tab-gradient .nav-item .nav-link::after {
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    background: hsl(var(--white));
    left: 0;
    top: 0;
    border-radius: inherit;
    z-index: -1;
}

.tab-gradient .nav-item .nav-link::after {
    background: var(--main-gradient);
    visibility: hidden;
    opacity: 0;
}

.tab-gradient .nav-item .nav-link.active {
    color: hsl(var(--white));
    font-weight: 300;
}

.tab-gradient .nav-item .nav-link.active::before {
    visibility: hidden;
    opacity: 0;
}

.tab-gradient .nav-item .nav-link.active::after {
    visibility: visible;
    opacity: 1;
}

/* Bordered Tab */
.tab-bordered {
    gap: 32px;
    margin-bottom: 0px;
}

@media screen and (max-width: 1199px) {
    .tab-bordered {
        gap: 24px;
    }
}

@media screen and (max-width: 991px) {
    .tab-bordered {
        gap: 16px;
    }
}

@media screen and (max-width: 575px) {
    .tab-bordered {
        gap: 8px;
        margin-bottom: 24px;
    }
}

.tab-bordered .nav-link {
    background-color: transparent;
    border: 1px solid hsl(var(--gray-five));
    border-radius: 5px;
    color: hsl(var(--body-color));
    padding: 5px 8px;
}

@media (min-width: 575px) {
    .tab-bordered .nav-link {
        background-color: transparent;
        padding: 0;
        color: hsl(var(--body-color)) !important;
        font-size: 14px;
        padding-bottom: 16px !important;
        border: 0;
        border-bottom: 2px solid transparent;
        border-radius: 0;
    }
}

.tab-bordered .nav-link.active {
    background-color: transparent;
    color: hsl(var(--heading-color)) !important;
    font-weight: 600;
    border-color: hsl(var(--heading-color)) !important;
}

.tab-bordered .nav-link.active .star-rating__text {
    color: hsl(var(--heading-color)) !important;
    font-weight: 600;
}

/* ================================= Tab Css End =========================== */
/* ======================= Common Table Css Start ======================= */
.table {
    color: hsl(var(--white));
    margin-bottom: 0;
    min-width: max-content;
    border-collapse: separate;
    border-spacing: 0 24px;
  /* Style Two */
}

.table > :not(caption) > * > * {
    border-bottom: 0;
}

.table tr th {
    padding-left: 0;
    padding: 20px 16px;
    color: hsl(var(--heading-color));
    background-color: hsl(var(--section-bg));
}

.table tr th:not(:last-child) {
    border-radius: 0;
}

.table tr th:not(:first-child) {
    border-radius: 0;
}

.table tr th:first-child {
    border-radius: 8px 0 0 8px;
}

.table tr th:last-child {
    border-radius: 0 8px 8px 0;
}

.table tr th, .table tr td {
    text-align: center;
    vertical-align: middle;
}

.table tr th:first-child, .table tr td:first-child {
    text-align: left;
}

.table tr th:last-child, .table tr td:last-child {
    text-align: right;
}

.table thead tr {
    border-bottom: 1px solid hsl(var(--white)/0.2);
}

.table thead tr th {
    font-size: clamp(1rem, 0.848rem + 0.317vw, 1.125rem);
    font-weight: 500;
}

.table tbody tr {
    border-bottom: 1px solid hsl(var(--white)/0.2);
}

.table tbody tr:last-child {
    border-bottom: 0;
}

.table tbody tr td {
    font-size: clamp(0.875rem, 0.723rem + 0.317vw, 1rem);
    font-weight: 400;
    padding: 16px;
    border: 1px solid hsl(var(--gray-five));
}

.table tbody tr td:not(:last-child) {
    border-right: 0;
}

.table tbody tr td:not(:first-child) {
    border-left: 0;
}

.table tbody tr td:first-child {
    border-radius: 8px 0 0 8px;
}

.table tbody tr td:last-child {
    border-radius: 0 8px 8px 0;
}

.table.style-two {
    color: hsl(var(--body-color));
    border-spacing: 0px;
    background-color: hsl(var(--white));
    border: 1px solid hsl(var(--gray-five));
    border-radius: 16px;
    overflow: hidden;
}

.table.style-two thead tr {
    border: 0;
}

.table.style-two thead tr th {
    border-bottom: 1px solid hsl(var(--gray-five));
    border-radius: 0;
    padding: 24px;
    font-weight: 700;
    font-family: var(--heading-font);
    background-color: hsl(var(--white));
}

.table.style-two tbody tr:last-child td {
    border-bottom: 0;
}

.table.style-two tbody tr td {
    border-radius: 0 !important;
    border-top: 0;
    border-left: 0;
    border-right: 0;
    border-style: dashed;
}

/* ======================= Common Table Css End ======================= */
/* ================= Common Card Css Start ========================= */
.common-card {
    border: 0;
    box-shadow: var(--box-shadow);
    border-radius: 16px;
}

.common-card .card-header, .common-card .card-footer {
    padding: 12px 24px;
    background-color: transparent;
    border-bottom: 1px solid hsl(var(--border-color));
}

@media screen and (max-width: 424px) {
    .common-card .card-header, .common-card .card-footer {
        padding: 12px 16px;
    }
}

.common-card .card-header {
    background-color: hsl(var(--black));
}

.common-card .card-header .title {
    color: hsl(var(--white));
    margin-bottom: 0;
}

.common-card .card-body {
    padding: 24px;
}

@media screen and (max-width: 424px) {
    .common-card .card-body {
        padding: 16px;
    }
}

.common-card .card-footer {
    border-top: 1px solid hsl(var(--border-color));
    border-bottom: 0;
}

/* ================= Common Card Css End ========================= */
/* ================================= preload Css Start =========================== */
.loader-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: hsl(var(--white));
    z-index: 99999;
}

.loader-mask .loader {
    position: absolute;
    left: 50%;
    top: 50%;
    width: 80px;
    height: 80px;
    font-size: 0;
    display: inline-block;
    margin: -40px 0 0 -40px;
    text-indent: -9999em;
    -webkit-transform: translateZ(0);
    -ms-transform: translateZ(0);
    transform: translateZ(0);
}

.loader-mask .loader div {
    background-color: hsl(var(--main));
    display: inline-block;
    float: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 80px;
    height: 80px;
    opacity: 0.6;
    border-radius: 50%;
    -webkit-animation: ballPulseDouble 2s ease-in-out infinite;
    animation: ballPulseDouble 2s ease-in-out infinite;
}

.loader-mask .loader div:last-child {
    -webkit-animation-delay: -1s;
    animation-delay: -1s;
}

@-webkit-keyframes ballPulseDouble {
    0%, 100% {
        -webkit-transform: scale(0);
        transform: scale(0);
    }

    50% {
        -webkit-transform: scale(1);
        transform: scale(1);
    }
}

@keyframes ballPulseDouble {
    0%, 100% {
        -webkit-transform: scale(0);
        transform: scale(0);
    }

    50% {
        -webkit-transform: scale(1);
        transform: scale(1);
    }
}
/* ================================= preload Css End ===========================  */
/* ====================== Section Heading ==================== */
.section-heading {
    text-align: center;
    margin-bottom: clamp(2rem, 0.783rem + 2.536vw, 3rem);
    max-width: 630px;
    margin-left: auto;
    margin-right: auto;
}

.section-heading__title {
    position: relative;
    margin-bottom: 16px;
    text-transform: capitalize;
}

.section-heading__desc {
    margin-top: clamp(1rem, -0.217rem + 2.536vw, 2rem);
    font-size: clamp(1rem, 0.848rem + 0.317vw, 1.125rem);
    max-width: 634px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 24px;
}

.section-heading__desc:last-child {
    margin-bottom: 0;
}

.section-heading__desc.w-sm {
    max-width: 465px;
}

.section-heading.style-left {
    text-align: left;
    margin-left: 0;
}

.section-heading.style-left .section-heading__desc {
    margin-left: 0;
}

.section-heading.style-white .section-heading__title {
    color: hsl(var(--white));
}

.section-heading.style-white .section-heading__desc {
    color: hsl(var(--white-one));
    font-weight: 300;
}

.section-heading.style-flex {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    max-width: unset;
    align-items: center;
}

.section-heading.style-flex .section-heading__inner {
    max-width: 410px;
}

.section-heading.style-flex .section-heading__inner.w-lg {
    max-width: 636px;
}

/* ====================== Section Heading En d==================== */
/* ============= Header Start Here ======================= */
.header {
    background-color: hsl(var(--white));
    transition: 0.2s linear;
}

@media screen and (max-width: 991px) {
    .header {
        padding: 10px 0;
    }
}

.header.fixed-header {
    position: sticky;
    left: 0;
    top: 0;
    right: 0;
    width: 100%;
    z-index: 9;
    backdrop-filter: blur(24px);
    animation: slideDown 0.35s ease-out;
    background-color: hsl(var(--white)/0.9) !important;
    box-shadow: 0 5px 16px rgba(0, 0, 0, 0.1);
}

@media screen and (max-width: 991px) {
    .header.fixed-header {
        top: -1px;
    }
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
    }

    to {
        transform: translateY(0);
    }
}

.logo img {
    max-height: 200px;
}

@media screen and (max-width: 767px) {
    .logo img {
        max-height: 100px;
    }
}

@media (max-width: 1199px) {
    .header-right__inner .btn .icon {
        display: none;
    }

    .language-select img {
        display: none;
    }

    .language-select .select {
        padding-left: 0 !important;
    }
}

.header-right {
    gap: 24px;
}

@media screen and (max-width: 1199px) {
    .header-right {
        gap: 16px;
    }
}

.header-right__button {
    position: relative;
    z-index: 1;
}

.header-right__button:hover::before {
    visibility: visible;
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}

.header-right__button::before {
    position: absolute;
    content: "";
    width: 40px;
    height: 40px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%) scale(0.8);
    background-color: hsl(var(--gray-five));
    z-index: -1;
    border-radius: 50%;
    visibility: hidden;
    opacity: 0;
    transition: 0.2s linear;
}

/* Cart Btn */
.cart-btn img{
    width: 35px;
}

.qty-badge {
    width: 20px;
    height: 20px;
    background: var(--main-gradient);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: hsl(var(--white));
    position: absolute;
    top: 0;
    right: 0;
    pointer-events: none;
}

/* Cart Btn */
/* Language Select */
.language-select {
    border: 1px solid hsl(var(--border-color));
    border-radius: 50px;
    padding: 8px 16px;
}

.language-select::before {
    right: 16px;
}

.language-select select {
    color: hsl(var(--heading-color)) !important;
    padding: 8px 16px;
}

/* Language Select */
.toggle-mobileMenu {
    line-height: 1;
    font-size: 36px;
    color: hsl(var(--heading-color));
}

.contact-number {
    padding-right: clamp(0.75rem, -0.771rem + 3.17vw, 2rem);
    margin-right: clamp(0.75rem, -0.771rem + 3.17vw, 2rem);
    border-right: 1px solid hsl(var(--border-color));
}

@media screen and (max-width: 575px) {
    .contact-number {
        font-size: 0.875rem;
    }
}

@media screen and (max-width: 424px) {
    .contact-number {
        display: none !important;
    }
}

.contact-number:hover .text {
    color: hsl(var(--main));
}

@media screen and (max-width: 575px) {
    .contact-number .icon {
        font-size: 15px !important;
    }
}
/* Header Menu and Submenu Css Start */
.nav-menu {
    gap: 24px;
}

@media (min-width: 992px) and (max-width: 1199px) {
    .nav-menu {
        gap: 16px;
    }
}

.nav-menu__item.activePage > a {
    color: hsl(var(--main));
}

.nav-menu__item.activePage > a::before {
    color: hsl(var(--main));
}

.nav-menu__item:hover > a {
    color: hsl(var(--heading-color));
}

@media (min-width: 992px) {
    .nav-menu__item:hover > a {
        color: hsl(var(--main));
    }
}

.nav-menu__link {
    color: hsl(var(--heading-color));
    font-weight: 500;
    font-size: 2rem;
    width: 100%;
    padding: 38px 0;
    width: 100%;
    font-family: var(--heading-font);
    line-height: 1.2;
    font-weight: 700;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.15), 0px 4px 10px rgba(0, 0, 0, 0.15), 0px 17px 17px rgba(0, 0, 0, 0.13), 0px 39px 24px rgba(0, 0, 0, 0.08), 0px 70px 28px rgba(0, 0, 0, 0.02), 0px 109px 31px rgba(0, 0, 0, 0);

}

@media screen and (max-width: 1199px) {
    .nav-menu__link {
        font-size: 0.875rem;
        font-family: var(--heading-font);
        line-height: 1.2;
        font-weight: 700;
    
    }
}

.has-submenu {
    position: relative;
}

.has-submenu .nav-menu__link {
    padding-right: 16px;
}

.has-submenu.active > a, .has-submenu.active > a::before {
    color: hsl(var(--main));
}

.has-submenu.active > a::before {
    transform: translateY(-50%) rotate(180deg) !important;
}

.has-submenu:hover .nav-submenu {
    visibility: visible;
    opacity: 1;
    margin-top: 0;
}

@media (min-width: 992px) {
    .has-submenu:hover > a::before {
        color: hsl(var(--main));
        transform: translateY(-50%) rotate(180deg);
    }
}

.has-submenu > a {
    position: relative;
}

.has-submenu > a::before {
    position: absolute;
    content: "\f107";
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    font-size: 0.625rem;
    color: hsl(var(--heading-color)/0.4);
    transition: 0.2s linear;
}

/* Submenu Start */
.nav-submenu {
    position: absolute;
    left: 0;
    top: 100%;
    width: max-content;
    background-color: hsl(var(--white));
    border-radius: 6px;
    min-width: 190px;
    overflow: hidden;
    padding: 8px;
    box-shadow: var(--box-shadow);
    visibility: hidden;
    opacity: 0;
    margin-top: 16px;
    transition: 0.2s linear;
    z-index: 99;
    max-height: 400px;
    overflow-y: auto;
}

.nav-submenu::-webkit-scrollbar {
    width: 6px;
}

.nav-submenu::-webkit-scrollbar-track {
    background: #e4e4e4;
}

.nav-submenu::-webkit-scrollbar-thumb {
    background: #a2a2a2;
    border-radius: 50px;
}

.nav-submenu::-webkit-scrollbar-thumb:hover {
    background: #6d6d6d;
}

.nav-submenu__item {
    display: block;
    border-radius: 4px;
    transition: 0.2s linear;
    position: relative;
}

.nav-submenu__item.activePage .nav-submenu__link {
    color: hsl(var(--main));
}

.nav-submenu__item.activePage::before {
    visibility: visible;
    opacity: 1;
}

.nav-submenu__item.activePage a {
    margin-left: 10px;
}

.nav-submenu__item::before {
    position: absolute;
    content: "\f101";
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    font-size: 0.625rem;
    color: hsl(var(--main));
    visibility: hidden;
    opacity: 0;
    transition: inherit;
}

.nav-submenu__item:hover {
    background-color: var(--gray-200);
}

.nav-submenu__item:hover::before {
    visibility: visible;
    opacity: 1;
}

.nav-submenu__item:hover a {
    margin-left: 10px;
    color: hsl(var(--heading-color));
}

.nav-submenu__link {
    color: hsl(var(--heading-color));
    font-weight: 500;
    font-size: 1rem;
    width: 100%;
    padding-right: 16px;
    width: 100%;
    display: block;
    padding: 8px 14px;
    border-radius: inherit;
}

.nav-submenu__link::before {
    position: absolute;
}

.nav-submenu__link:hover {
    color: hsl(var(--main)) !important;
}

/* Submenu End */
/* Header Menu and Submenu Css End */
/* ================================= Header Css End =========================== */
/* ============ Search box ============= */
.search-box {
    position: relative;
  /* Style Two */
  /* style Three */
}

.search-box input {
    background-color: hsl(var(--white)) !important;
    border: 0;
    padding-right: 70px;
}

@media screen and (max-width: 767px) {
    .search-box input {
        padding-right: 56px;
    }
}

.search-box input::placeholder {
    color: hsl(var(--black-three));
}

.search-box .icon {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    color: hsl(var(--body-color));
}

@media screen and (max-width: 767px) {
    .search-box .icon {
        right: 5px;
    }
}

.search-box.style-two {
    max-width: 636px;
    margin: 0 auto;
}

.search-box.style-two input {
    padding-left: 170px;
}

@media screen and (max-width: 575px) {
    .search-box.style-two input {
        padding-left: 128px;
    }
}

.search-box.style-two .search-box__select {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
}

@media screen and (max-width: 575px) {
    .search-box.style-two .search-box__select {
        left: 0;
    }
}

.search-box.style-two .search-box__select.select-has-icon::before {
    right: 0;
}

.search-box.style-two .search-box__select.select-has-icon::after {
    position: absolute;
    content: "";
    width: 1px;
    height: 100%;
    right: -8px;
    top: 50%;
    transform: translateY(-50%);
    background: hsl(var(--border-color));
}

.search-box.style-two .search-box__select.select-has-icon select {
    padding-right: 19px;
    padding-left: 12px;
    color: hsl(var(--black-three));
}

@media screen and (max-width: 575px) {
    .search-box.style-two .search-box__select.select-has-icon select {
        font-size: 0.75rem;
    }
}

.search-box.style-two .search-box__select.select-has-icon select:focus {
    box-shadow: none;
}

.search-box.style-three {
    max-width: 274px;
}

.search-box.style-three .icon {
    width: 48px;
    height: 48px;
    right: 4px;
}

@media screen and (max-width: 991px) {
    .search-box.style-three .icon {
        width: 36px;
        height: 36px;
    }
}

.search-box.style-three input {
    background-color: hsl(var(--section-bg)) !important;
    border: 1px solid hsl(var(--white-one));
}

.search-box .rounded-icon {
    width: 40px;
    height: 40px;
    border: 1px solid hsl(var(--white-one));
    border-radius: 50%;
    transition: 0.2s linear;
}

.search-box .rounded-icon:hover {
    background-color: hsl(var(--white-one)) !important;
}

/* ================== Search Box =================== */
/* Mobile Menu Sidebar Start */
.mobile-menu {
    position: fixed;
    background-color: hsl(var(--white));
    width: 300px;
    height: 100vh;
    overflow-y: auto;
    padding: 24px;
    z-index: 991;
    transform: translateX(-100%);
    transition: 0.2s linear;
    padding-bottom: 68px;
}

.mobile-menu::-webkit-scrollbar {
    width: 6px;
}

.mobile-menu::-webkit-scrollbar-track {
    background: #e0e0e0;
}

.mobile-menu::-webkit-scrollbar-thumb {
    background: #bab9b9;
    border-radius: 30px;
}

.mobile-menu::-webkit-scrollbar-thumb:hover {
    background: #949494;
}

.mobile-menu.active {
    transform: translateX(0%);
}

.close-button {
    position: absolute;
    right: 8px;
    top: 8px;
    width: 28px;
    height: 28px;
    background-color: var(--gray-200);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: 0.2s;
}

.close-button:hover {
    background-color: hsl(var(--black));
    color: hsl(var(--white));
}

.nav-menu--mobile {
    display: block;
    margin-top: 32px;
}

.nav-menu--mobile .nav-menu__item > a::before {
    transform: translateY(-50%) rotate(0deg);
}

.nav-menu--mobile .nav-menu__link {
    border-bottom: 1px solid hsl(var(--black)/0.08) !important;
    position: relative;
    padding: 12px 0;
    padding-right: 16px;
}

.nav-menu--mobile .nav-submenu {
    position: static;
    visibility: visible;
    opacity: 1;
    box-shadow: none;
    width: 100%;
    margin-top: 0;
    padding: 0;
    margin-left: 16px;
    display: none;
    transition: 0s;
}

.nav-menu--mobile .nav-submenu__link {
    width: 100%;
    padding: 10px 0;
    border-bottom: 1px solid hsl(var(--black)/0.08) !important;
}

.nav-menu--mobile .nav-submenu__item:hover {
    background-color: transparent;
    color: hsl(var(--main));
}

.nav-menu--mobile .nav-submenu__item::before {
    display: none;
}

/* Mobile Menu Sidebar Start */
/* ============= Footer Start Here ======================= */
.footer {
    margin-top: auto;
    position: relative;
    z-index: 1;
    background-image: url(../images/shapes/footer-bg.png);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: top;
    padding: 120px 0;
    padding-top: 220px;
    background-color: hsl(var(--white));
}

@media screen and (max-width: 1199px) {
    .footer {
        padding: 120px 0;
        background-color: hsl(var(--black)) !important;
        background-image: none;
    }
}

@media screen and (max-width: 991px) {
    .footer {
        padding: 80px 0;
    }
}

.footer-item__desc, .footer-menu__link {
    color: hsl(var(--white-one));
    font-size: 1rem;
    font-weight: 300;
    line-height: 170%;
}

.footer-item__logo {
    margin-bottom: 32px;
}

@media screen and (max-width: 991px) {
    .footer-item__logo {
        margin-bottom: 24px;
    }
}

.footer-item__logo a img {
    width: 100%;
    height: 100%;
    max-width: 240px;
    max-height: 64px;
}

.footer-item__social {
    margin-top: 32px;
}

@media screen and (max-width: 991px) {
    .footer-item__social {
        margin-top: 24px;
    }
}

.footer-item__title {
    color: hsl(var(--white));
    margin-bottom: 24px;
}

/* Footer List Item */
.footer-menu {
    display: flex;
    flex-direction: column;
}

.footer-menu__item {
    display: block;
    padding-bottom: 10px;
}

.footer-menu__item:last-child {
    padding-bottom: 0;
}

.footer-menu__link {
    position: relative;
    padding-left: 22px;
}

.footer-menu__link::before {
    position: absolute;
    content: "\f105";
    font-family: "Line Awesome Free";
    font-weight: 900;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.875rem;
    transition: 0.2s linear;
    width: calc(100% + 1px);
    height: calc(100% + 1px);
}

.footer-menu__link:hover {
    color: hsl(var(--white));
}

.footer-menu__link:hover::before {
    left: 4px;
}

/* ============= Footer End Here ======================= */
/* ============= Bottom Footer End Here ======================= */
.bottom-footer {
    background-color: hsl(var(--black-two));
    
}

.bottom-footer__inner {
    padding: 24px 0;
}

@media screen and (max-width: 991px) {
    .bottom-footer__inner {
        padding: 16px 0;
    }
}

.bottom-footer__text, .footer-link {
    color: hsl(var(--white)/0.8);
    font-size: 1.125rem;
    font-weight: 200;
}

@media screen and (max-width: 991px) {
    .bottom-footer__text, .footer-link {
        font-size: 1rem;
    }
}

@media screen and (max-width: 767px) {
    .bottom-footer__text, .footer-link {
        font-size: 0.9375rem;
    }
}

.footer-links {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: clamp(0.75rem, -0.163rem + 1.902vw, 1.5rem);
    font-weight: 200;
}

.footer-link:hover {
    color: hsl(var(--white));
    text-decoration: underline;
}

/* =============Bottom Footer End Here ======================= */
/* ==================== Footer Two Css Start Here ==================== */
.footer-two .footer-menu__link {
    color: hsl(var(--black-three));
    font-weight: 400;
}

.footer-two .footer-menu__link:hover {
    color: hsl(var(--main));
}

.footer-two .footer-item__title {
    color: hsl(var(--heading-color));
}

.footer-two .footer-item__desc, .footer-two .footer-menu__link {
    color: hsl(var(--black-three));
}

.footer-two .social-list__link {
    color: hsl(var(--black));
    font-size: 16px;
}

/* Bottom Footer Two */
.bottom-footer-two {
    border-top: 1px solid hsl(var(--gray-five));
}

.bottom-footer-two .bottom-footer__text, .bottom-footer-two .footer-link {
    color: hsl(var(--black-three));
    font-weight: 300;
}

.bottom-footer-two .footer-link {
    color: hsl(var(--black-three));
    font-weight: 400;
}

/* ==================== Footer Two Css End Here ==================== */
/* ===================== Scroll to Top Start ================================= */
.progress-wrap {
    position: fixed;
    right: 36px;
    bottom: 36px;
    height: 46px;
    line-height: 46px;
    width: 46px;
    cursor: pointer;
    display: block;
    border-radius: 50px;
    box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.2);
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(15px);
    -webkit-transition: all 200ms linear;
    transition: all 200ms linear;
    background-color: hsl(var(--white));
}

@media screen and (max-width: 991px) {
    .progress-wrap {
        right: 24px;
        bottom: 24px;
        height: 40px;
        width: 40px;
        line-height: 40px;
    }
}

.progress-wrap.active-progress {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.progress-wrap:hover {
    transform: scale(1.06);
}

.progress-wrap::after {
    position: absolute;
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    content: "\f062";
    text-align: center;
    height: 46px;
    width: 46px;
    line-height: 46px;
    font-size: 18px;
    left: 0;
    top: 0;
    cursor: pointer;
    display: block;
    z-index: 1;
    -webkit-transition: all 200ms linear;
    transition: all 200ms linear;
    color: hsl(var(--main)) !important;
}

@media screen and (max-width: 991px) {
    .progress-wrap::after {
        height: 40px;
        width: 40px;
        line-height: 40px;
    }
}

.progress-circle {
    top: -2px;
    position: relative;
}

.progress-wrap svg path {
    fill: none;
}

.progress-wrap svg.progress-circle path {
    stroke: hsl(var(--main));
    stroke-width: 5;
    box-sizing: border-box;
    -webkit-transition: all 200ms linear;
    transition: all 200ms linear;
}

/* ===================== Scroll to Top End ================================= */
/* ================================= Template Selection Css Start =========================== */
/* ================================= Template Selection Css End ===========================  */
/* ============================ Sale Offer Css Start =========================== */
.sale-offer {
    background: linear-gradient(117deg, #FEF0F8 14.35%, #DACFFF 84.4%);
    padding: 8px 0;
}

.sale-offer__content {
    gap: 16px;
}

@media screen and (max-width: 424px) {
    .sale-offer__content {
        gap: 8px;
    }
}

.sale-offer__text {
    font-size: clamp(0.6875rem, 0.459rem + 0.475vw, 0.875rem);
}

.sale-offer__qty {
    font-size: clamp(0.75rem, 0.294rem + 0.951vw, 1.125rem);
}

.sale-offer__button {
    width: 32px;
}

.sale-offer__close {
    position: absolute;
    right: 0;
    top: 0;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    z-index: 1;
    transition: 0.2s linear;
    display: flex;
    justify-content: center;
    align-items: center;
}

@media screen and (max-width: 424px) {
    .sale-offer__close {
        width: 28px;
        height: 28px;
    }
}

.sale-offer__close:hover {
    color: hsl(var(--main));
}

.sale-offer__close:hover::before {
    visibility: visible;
    opacity: 1;
    transform: scale(1);
}

.sale-offer__close::before {
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background-color: hsl(var(--main)/0.2);
    z-index: -1;
    border-radius: inherit;
    transition: 0.2s linear;
    visibility: hidden;
    opacity: 0;
    transform: scale(0.8);
}

.countdown {
    display: flex;
    align-items: center;
    gap: 16px;
}

@media screen and (max-width: 424px) {
    .countdown {
        gap: 10px;
    }
}

.countdown > div {
    display: flex;
    align-items: center;
    gap: 4px;
    flex-direction: row-reverse;
}

.countdown span {
    font-weight: 500;
    color: hsl(var(--heading-color));
    font-size: 14px;
}

/* ============================ Sale Offer Css End =========================== */
/* ================================= Social Icon Css Start =========================== */
.social-list {
    display: inline-flex;
    gap: 12px;
    align-items: center;
}

.social-list__link {
    width: 40px;
    height: 40px;
    border: 1px solid hsl(var(--white-one));
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: hsl(var(--white));
    position: relative;
    font-size: 0.875rem;
    z-index: 1;
}

.social-list__link::after {
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    border-radius: inherit;
    background: var(--main-gradient);
    transition: 0.2s linear;
    z-index: -1;
    visibility: hidden;
    opacity: 0;
}

.social-list__link:hover {
    color: hsl(var(--white)) !important;
    border-color: transparent;
}

.social-list__link:hover::after {
    visibility: visible;
    opacity: 1;
}

/* ================================= Social Icon Css End ===========================  */
/* ================= Slick Arrow & Dots css Start ================ */
.slick-initialized.slick-slider .slick-track {
    display: flex;
}

.slick-initialized.slick-slider .slick-slide {
    cursor: grab;
    height: auto;
    margin: 0 10px;
}

.slick-initialized.slick-slider .slick-slide > div {
    height: 100%;
}

/* Arrow Small size */
.arrow-sm .slick-arrow {
    width: 32px;
    height: 32px;
    border: 1px solid hsl(var(--white-one));
    box-shadow: 0px 20px 30px rgba(197, 196, 201, 0.25);
    font-size: 1.125rem;
}

.arrow-sm .slick-arrow:hover {
    border-color: hsl(var(--main));
}

/* Slick Slider Arrow */
.slick-arrow {
    position: absolute;
    z-index: 1;
    border: none;
    background-color: transparent;
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    transition: 0.2s linear;
    bottom: clamp(1.5rem, -1.239rem + 5.705vw, 3.75rem);
    left: clamp(1.5rem, -1.239rem + 5.705vw, 3.75rem);
    top: 50%;
    transform: translateY(-50%);
    margin-top: 0;
    background-color: hsl(var(--white));
    color: hsl(var(--heading-color));
    box-shadow: 0px 20px 30px 0px rgba(197, 196, 201, 0.25);
    font-size: 1.5rem;
    left: -24px;
}

@media screen and (max-width: 575px) {
    .slick-arrow {
        left: -10px;
    }
}

.slick-arrow:hover {
    background-color: hsl(var(--main));
    color: hsl(var(--white));
}

.slick-next {
    right: -24px;
    left: auto;
}

@media screen and (max-width: 575px) {
    .slick-next {
        right: -10px;
    }
}
/* Dots Css Start */
.slick-dots {
    text-align: center;
    margin-top: clamp(1.5rem, -1.239rem + 5.705vw, 3.75rem);
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
}

@media screen and (max-width: 1199px) {
    .slick-dots {
        gap: 8px;
    }
}

.slick-dots li {
    display: inline-block;
}

.slick-dots li button {
    border: none;
    background-color: transparent;
    color: hsl(var(--white));
    width: 16px;
    height: 16px;
    border-radius: 50%;
    text-indent: -9999px;
    transition: 0.3s linear;
    border: 1px solid hsl(var(--black));
}

.slick-dots li.slick-active button {
    background-color: hsl(var(--black));
}

/* Dots Css End */
/* ================= Slick Arrow & Dots css Start ================ */
/* =============================== Star Rating Css Start =============================== */
.star-rating {
    gap: 4px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.star-rating__item {
    font-size: 0.875rem;
    color: #FFA944;
}

/* =============================== Star Rating Css End =============================== */
/* ================================= Comment Css Start =========================== */
.comment-list--two {
    margin-left: clamp(1.5rem, -4.281rem + 12.044vw, 6.25rem);
}

.comment-list__item {
    padding: 24px;
    border: 1px solid hsl(var(--white-one));
    border-radius: 8px;
    margin-bottom: 16px;
}

@media screen and (max-width: 424px) {
    .comment-list__item {
        flex-direction: column;
        padding: 24px 16px;
    }
}

.comment-list__item:last-child {
    margin-bottom: 0;
}

@media screen and (max-width: 424px) {
    .comment-list__date {
        font-size: 12px !important;
    }
}

.comment-list__reply {
    font-size: 0.9375rem;
}

@media screen and (max-width: 424px) {
    .comment-list__reply {
        font-size: 0.875rem;
    }
}

.comment-list__thumb {
    width: 80px;
    height: 80px;
    border-radius: 50%;
}

@media screen and (max-width: 991px) {
    .comment-list__thumb {
        width: 60px;
        height: 60px;
    }
}
/* ================================= Comment Css End =========================== */
/* ========================= Blog Sidebar Start =================== */
.common-sidebar-wrapper {
    position: sticky;
    top: 120px;
    padding: 32px 24px;
    background-color: hsl(var(--section-bg));
    border-radius: 8px;
}

@media screen and (max-width: 1199px) {
    .common-sidebar-wrapper {
        padding: 24px;
    }
}

.common-sidebar {
    margin-bottom: clamp(1.5rem, 0.891rem + 1.268vw, 2rem);
}

.common-sidebar:last-of-type {
    margin-bottom: 0;
}

.common-sidebar__title {
    position: relative;
    margin-bottom: clamp(1rem, 0.391rem + 1.268vw, 1.5rem);
    padding-bottom: 8px;
    border-bottom: 1px solid hsl(var(--gray-five));
}

/* Category Style */
.category-list__item {
    margin-bottom: 12px;
    transition: 0.2s linear;
}

.category-list__item:hover {
    margin-left: 8px;
}

.category-list__item:last-child {
    margin-bottom: 0;
}

/* Tag */
.tag-list__link {
    color: hsl(var(--body-color));
    background-color: hsl(var(--white));
}

.tag-list__link:hover {
    background-color: hsl(var(--main));
    color: hsl(var(--white));
}

/* Tag End */
/* Latest Blog Css  */
.latest-blog {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 24px;
    border-radius: 5px;
    overflow: hidden;
}

.latest-blog:last-of-type {
    margin-bottom: 0px;
}

.latest-blog__thumb {
    width: 80px;
    height: 80px;
    display: flex;
    overflow: hidden;
    border-radius: 5px;
}

@media screen and (max-width: 424px) {
    .latest-blog__thumb {
        width: 60px;
    }
}

.latest-blog__thumb a {
    display: block;
    height: 100%;
    width: 100%;
}

.latest-blog__content {
    width: calc(100% - 80px);
    padding-left: 16px;
}

@media screen and (max-width: 424px) {
    .latest-blog__content {
        width: calc(100% - 60px);
    }
}
/* Latest Blog Css End */
/* ========================= Blog Sidebar End =================== */
/* ===================== Dashboard Sidebar Start ======================= */
.dashboard-sidebar {
    padding: 32px;
    background-color: hsl(var(--white));
    border-right: 1px solid hsl(var(--gray-five));
    transition: 0.2s linear;
    width: 312px;
    height: 100vh;
    overflow-y: auto;
    position: sticky;
    top: 0;
  /* width */
  /* Track */
  /* Handle */
  /* Handle on hover */
}

.dashboard-sidebar::-webkit-scrollbar {
    width: 6px;
}

.dashboard-sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.dashboard-sidebar::-webkit-scrollbar-thumb {
    background: #e8e8e8;
    border-radius: 10px;
}

.dashboard-sidebar::-webkit-scrollbar-thumb:hover {
    background: #d1d1d1;
}

@media screen and (max-width: 991px) {
    .dashboard-sidebar.active {
        transform: translateX(0);
    }
}

@media screen and (max-width: 1399px) {
    .dashboard-sidebar {
        padding: 24px;
    }
}

@media screen and (max-width: 991px) {
    .dashboard-sidebar {
        position: fixed;
        left: 0;
        top: 0;
        z-index: 99;
        transform: translateX(-100%);
    }
}

.dashboard-sidebar__close {
    position: absolute;
    right: 4px;
    top: 4px;
    font-size: 20px;
}

.dashboard-sidebar .logo img {
    max-width: 166px !important;
}

.dashboard-sidebar .logo.favicon {
    display: none;
}

.dashboard-sidebar .logo.favicon img {
    max-width: 44px;
}

/* Sidebar Menu List Css Start */
.sidebar-list__item {
    margin-bottom: 8px;
}

.sidebar-list__item.activePage .sidebar-list__link, .sidebar-list__item:hover .sidebar-list__link {
    color: hsl(var(--heading-color));
    background-color: hsl(var(--gray-seven));
}

.sidebar-list__item.activePage .sidebar-list__icon .icon, .sidebar-list__item:hover .sidebar-list__icon .icon {
    visibility: hidden;
    opacity: 0;
}

.sidebar-list__item.activePage .sidebar-list__icon .icon-active, .sidebar-list__item:hover .sidebar-list__icon .icon-active {
    visibility: visible;
    opacity: 1;
}

.sidebar-list__item:last-child {
    margin-bottom: 0;
}

.sidebar-list__link {
    width: 100%;
    color: hsl(var(--body-color));
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 8px;
    transition: 0s;
}

.sidebar-list__icon {
    width: 24px;
    height: auto;
    position: relative;
}

.sidebar-list__icon .icon {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

.sidebar-list__icon .icon-active {
    visibility: hidden;
    opacity: 0;
}

/* Sidebar Menu List Css End */
/* ===================== Dashboard Sidebar End ======================= */
/* ====================== Dashboard nav Css Start =================== */
.dashboard-nav {
    padding-top: 24px;
    padding-bottom: 24px;
}

@media screen and (max-width: 1199px) {
    .dashboard-nav {
        padding-top: 16px;
        padding-bottom: 16px;
    }
}

@media screen and (max-width: 991px) {
    .dashboard-nav {
        padding-top: 12px;
        padding-bottom: 12px;
    }
}

.icon-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    transition: 0.2s linear;
}

.icon-btn:hover {
    transform: scale(1.1);
}

.arrow-icon {
    display: none;
}

.search-input {
    position: relative;
}

@media (min-width: 1299px) {
    .search-input {
        min-width: 415px;
    }
}

@media screen and (max-width: 1199px) {
    .search-input {
        max-width: 200px;
    }
}

.search-input .icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
}

.search-input input {
    padding-left: 42px;
}

/* User Profile Css Start */
.user-profile {
    position: relative;
    text-align: center;
}

.user-profile__thumb {
    width: 40px;
    height: 40px;
    overflow: hidden;
    border-radius: 50%;
}

.user-profile__button {
    position: relative;
}

.user-profile .user-profile-dropdown {
    border-radius: 8px;
    transition: 0.2s linear;
    background-color: hsl(var(--white));
    box-shadow: var(--box-shadow);
    width: 200px;
    position: absolute;
    right: 0;
    z-index: 9;
    top: 100%;
    margin-top: 15px;
    padding: 15px;
    transform: scale(0.9);
    visibility: hidden;
    opacity: 0;
    border: 1px solid hsl(var(--gray-five));
}

.user-profile .user-profile-dropdown::before {
    position: absolute;
    content: "";
    width: 14px;
    height: 14px;
    top: -7px;
    right: 16px;
    transform: rotate(45deg);
    border: 1px solid hsl(var(--gray-five));
    background-color: hsl(var(--white));
    border-radius: 3px;
    border-bottom: 0;
    border-right: 0;
}

@media screen and (max-width: 991px) {
    .user-profile .user-profile-dropdown {
        transform: unset;
        top: 43px;
    }
}

.user-profile .user-profile-dropdown.show {
    visibility: visible;
    opacity: 1;
    transform: scale(1);
}

/* User Profile Css End */
/* ====================== Dashboard nav Css End =================== */
/* ====================== Change Body gradient Css Start =================*/
.change-gradient {
    --main-gradient: linear-gradient(312deg, hsl(var(--main)) 5.38%, hsl(var(--main-three)) 113.21%);
    --main-gradient-rev: linear-gradient(312deg, hsl(var(--main-three)) 5.38%, hsl(var(--main)) 113.21%);
}

/* ====================== Change Body gradient Css End =================*/
/* =========================== Banner Section Start Here ========================= */
.banner {
    position: relative;
    z-index: 1;
    padding: 132px 0;
}

@media screen and (max-width: 991px) {
    .banner {
        padding: 80px 0;
    }
}

@media screen and (max-width: 767px) {
    .banner {
        padding: 60px 0;
    }
}

.banner-content__title {
    margin-bottom: 24px;
    font-size: clamp(2rem, -2.0093rem + 7.85vw, 4.5rem);
    text-transform: capitalize;
}

@media screen and (max-width: 991px) {
    .banner-content__title {
        margin-bottom: 16px;
    }
}

.banner-content__desc {
    max-width: 572px;
    margin-bottom: clamp(1.5rem, -0.326rem + 3.803vw, 3rem);
}

.banner-thumb {
    max-width: 744px;
    max-height: 614px;
    margin-right: auto;
    position: relative;
    transform: translateX(120px);
}

@media screen and (max-width: 1599px) {
    .banner-thumb {
        transform: translateX(80px);
    }
}

@media screen and (max-width: 1499px) {
    .banner-thumb {
        transform: translateX(0px);
    }
}
/* tech list start */
.tech-list {
    margin-top: 32px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

@media screen and (max-width: 1199px) {
    .tech-list {
        margin-top: 24px;
        gap: 12px;
    }
}

.tech-list__item {
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    background-color: hsl(var(--white));
    flex-shrink: 0;
}

@media screen and (max-width: 1199px) {
    .tech-list__item {
        width: 40px;
        height: 40px;
    }
}
/* tech list End */
/* Statistics Start */
.statistics {
    padding: 34px 16px;
    border-radius: 16px;
    border: 1px solid hsl(var(--white));
    box-shadow: 0px 20px 30px 0px rgba(94, 53, 242, 0.3);
    display: inline-block;
    aspect-ratio: 1;
    min-width: 120px;
    top: 42%;
    left: 33%;
    position: absolute;
}

.statistics.style-two {
    right: 0px;
    border-radius: 16px;
    box-shadow: -10px 20px 30px 0px rgba(189, 190, 204, 0.5);
    left: auto;
    top: 60px;
    max-width: 120px;
}

.statistics.style-three {
    display: inline-flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    box-shadow: none !important;
    padding: 34px 10px;
}

@media (min-width: 1699px) {
    .statistics.style-three {
        min-width: 196px;
    }
}

.statistics.style-three .statistics__amount {
    margin-bottom: 8px;
}

@media screen and (max-width: 1499px) {
    .statistics.style-three .statistics__text {
        font-size: 0.875rem;
    }
}

@media screen and (max-width: 1399px) {
    .statistics.style-three .statistics__text {
        font-size: 0.75rem;
    }
}

.statistics.animation {
    animation: upDown 20s linear infinite;
}

.statistics__amount {
    margin-bottom: 0;
}

/* Statistics End */
@keyframes upDown {
    0%, 100% {
        transform: translateY(0px) scale(1);
    }

    25% {
        transform: translateY(30px) scale(1.1);
    }

    50% {
        transform: translateY(0px) scale(1);
    }

    75% {
        transform: translateY(-30px) scale(1.1);
    }
}

.dotted-img {
    position: absolute;
    left: 20%;
    top: 37%;
    z-index: -1;
    animation: scaling 20s linear infinite;
    max-width: 75%;
}

@media screen and (max-width: 575px) {
    .dotted-img {
        display: none;
    }
}

@keyframes scaling {
    0%, 100% {
        transform: scale(0.6);
    }

    50% {
        transform: scale(1.2);
    }
}
/* =========================== Banner Section End Here ========================= */
/* ======================== popular Section Css Start =========================== */
.popular__button {
    margin-top: clamp(1.5rem, -0.326rem + 3.803vw, 3rem);
}

.popular-item {
    padding: 40px 16px;
    text-align: center;
    border-radius: 8px;
    background: linear-gradient(117deg, #FFF2F9 14.01%, #F2EEFF 120.85%);
    position: relative;
    z-index: 1;
    transition: 0.2s linear;
}

.popular-item::before {
    position: absolute;
    content: "";
    width: calc(100% - 16px);
    height: calc(100% - 16px);
    border-radius: inherit;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background-color: hsl(var(--white));
    z-index: -1;
}

.popular-item__icon {
    width: 88px;
    height: 88px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    background-color: hsl(var(--section-bg));
}

.popular-item__title {
    margin-bottom: 0;
    margin-top: 24px;
}

/* ======================== popular Section Css End =========================== */
/* =========================== Product Css Start ========================== */
.product-card {
    border-radius: 8px;
    background-color: hsl(var(--white));
    overflow: hidden;
    transition: 0.2s linear;
}

@media (min-width: 424px) and (max-width: 575px) {
    .product-card {
        display: flex;
    }
}

.product-card.overlay-none .product-card__thumb::before {
    display: none;
}

.product-card:hover {
    box-shadow: 0px 20px 30px 0px rgba(197, 196, 201, 0.25);
}

.product-card:hover .product-card__thumb::before {
    height: 100%;
    visibility: visible;
    opacity: 1;
    z-index: 1;
}

.product-card:hover .product-card__thumb img {
    transform: scale(1.1);
}

.product-card__thumb {
    margin: 8px;
    border-radius: inherit;
    overflow: hidden;
    padding-bottom: 0;
    position: relative;
}

@media (min-width: 575px) {
    .product-card__thumb {
        max-height: 156px;
    }
}

.product-card__thumb::before {
    position: absolute;
    content: "";
    width: 100%;
    height: 0%;
    left: 0;
    bottom: 0;
    border-radius: 8px;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(0, 4, 37, 0.5) 69.5%, rgba(0, 4, 37, 0.5) 100%);
    pointer-events: none;
    visibility: hidden;
    opacity: 0;
    transition: 0.2s linear;
}

.product-card__thumb img {
    transition: 0.2s linear;
}

.product-card__wishlist {
    position: absolute;
    right: 8px;
    top: 8px;
    width: 40px;
    height: 40px;
    border-radius: 30px;
    background-color: hsl(var(--white)/0.5);
    backdrop-filter: blur(5px);
    color: hsl(var(--black));
    z-index: 2;
    transition: 0.2s linear;
}

.product-card__wishlist.style-two {
    position: relative;
    top: 0;
    right: 0;
    background-color: hsl(var(--section-bg));
    width: 32px;
    height: 32px;
    font-size: 0.75rem;
}

.product-card__wishlist.style-two:hover {
    background-color: hsl(var(--black));
    color: hsl(var(--white));
}

.product-card__wishlist:hover {
    background-color: hsl(var(--white));
}

.product-card__wishlist.active {
    background-color: hsl(var(--black));
    color: hsl(var(--white));
}

.product-card__content {
    padding: 24px 16px;
    padding-top: 16px;
}

.product-card__title {
    margin-bottom: 8px;
    text-transform: capitalize;
    font-weight: 600;
}

.product-card__title .link {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-card__author {
    font-size: 0.875rem;
    font-weight: 400;
    color: hsl(var(--black-three));
}

.product-card__author .link {
    color: inherit;
}

.product-card__prevPrice {
    font-size: 0.875rem;
    font-weight: 400;
    color: hsl(var(--gray-three));
}

.product-card__bottom {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid hsl(var(--gray-five));
}

@media screen and (max-width: 767px) {
    .product-card__bottom {
        margin-top: 8px;
        padding-top: 8px;
    }
}
/* Author Css Start */
.author-info__thumb {
    width: 114px;
    height: 114px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    background-color: hsl(var(--white));
    position: relative;
    z-index: 1;
}

.author-info__thumb::before {
    position: absolute;
    content: "";
    width: 80px;
    height: 80px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background-color: hsl(var(--section-bg));
    z-index: -1;
    border-radius: inherit;
}

/* Author Css End */
/* =========================== Product Css End ========================== */
/* ======================= Featured Products Css Start =============================== */
@media (min-width: 576px) {
    .card-wrapper {
        margin-bottom: 56px;
    }
}

@media (min-width: 576px) {
    .card-wrapper div[class*=col]:nth-child(even) .product-card {
        transform: translateY(56px);
    }
}
/* ======================= Featured Products Css End =============================== */
/* ======================= Selling Products css Start ========================= */
.selling-product::before {
    position: absolute;
    content: "";
    width: 100%;
    height: 62%;
    background-color: hsl(var(--black));
    left: 0;
    top: 0;
    z-index: -1;
}

@media (min-width: 575px) {
    .selling-product .bg--gradient, .selling-product .bg-pattern {
        top: 0;
        transform: unset;
        height: 62%;
    }
}

.selling-product .slick-arrow {
    top: 56%;
}

/* ======================= Selling Products css End ========================= */
/* ======================= To Featured Author Css Start =============================== */
.top-author .spider-net {
    left: -18% !important;
}

.follow-btn.active {
    background-color: hsl(var(--main)) !important;
    color: hsl(var(--white)) !important;
}

@media (min-width: 575px) {
    .circle-content {
        margin-left: 80px;
    }
}
/* text Circle Rotation */
.circle {
    position: relative;
    margin-bottom: 24px;
    width: 158px;
    height: 158px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: hsl(var(--white)/0.5);
    backdrop-filter: blur(6px);
    z-index: 21;
    border: 1px solid hsl(var(--white));
}

@media (min-width: 575px) {
    .circle {
        top: 64px;
        left: -80px;
        position: absolute;
    }
}

.circle.style-two {
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    margin-bottom: 0;
    position: absolute !important;
}

@media screen and (max-width: 575px) {
    .circle.style-two {
        width: 120px;
        height: 120px;
    }
}

.circle.style-two .circle__text span {
    transform-origin: 0 76px;
}

@media screen and (max-width: 575px) {
    .circle.style-two .circle__text span {
        font-size: 14px;
    }
}

@media screen and (max-width: 575px) {
    .circle.style-two .circle__text span {
        transform-origin: 0 58px;
    }
}

.circle.style-two .circle__badge {
    border-color: hsl(var(--white));
}

.circle__badge {
    border: 1px solid hsl(var(--main));
    width: 74px;
    height: 74px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: inherit;
}

.circle__text {
    position: absolute;
    width: 100%;
    height: 100%;
    color: hsl(var(--heading-color));
    font-size: 1rem;
    animation: textRotation 8s linear infinite;
}

.circle__text span {
    position: absolute;
    left: 50%;
    font-size: 1rem;
    font-family: "inter";
    transform-origin: 0 78px;
    color: hsl(var(--heading-color));
    font-weight: 500;
}

@keyframes textRotation {
    to {
        transform: rotate(360deg);
    }
}
/* text Circle Rotation */
/* ======================= To Featured Author Css End =============================== */
/* ===================== Top Performance Start ============================= */
.top-performance::before {
    position: absolute;
    content: "";
    width: 750px;
    height: 613px;
    background: linear-gradient(152deg, rgba(246, 246, 246, 0.2) 17.12%, rgba(242, 5, 135, 0.2) 105.91%);
    filter: blur(100px);
    right: -100px;
    top: 120px;
    z-index: -1;
}

.performance-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
}

.performance-content__item {
    background: #ddd;
    aspect-ratio: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50% 0px 50% 0px;
    flex-direction: column;
}

.performance-content__item:nth-child(1) {
    background-color: #FDFBEB;
    border-radius: 0px 50% 0px 50%;
}

.performance-content__item:nth-child(2) {
    background-color: #DAD1FD;
}

.performance-content__item:nth-child(3) {
    background-color: #FDCDF8;
}

.performance-content__item:nth-child(4) {
    background-color: #D0FBFE;
    border-radius: 0px 50% 0px 50%;
}

.performance-content__text {
    color: hsl(var(--heading-color));
    margin-bottom: 8px;
}

.performance-content__count {
    margin-bottom: 0;
}

/* ===================== Top Performance End ============================= */
/* =========================== Blog Section Css Start ========================== */
.blog-item {
    background-color: hsl(var(--white));
    border-radius: 16px;
    overflow: hidden;
    height: 100%;
}

.blog-item:hover .blog-item__thumb img {
    transform: scale(1);
}

.blog-item__thumb {
    max-height: 294px;
    overflow: hidden;
}

.blog-item__thumb img {
    transform: scale(1.1);
    transition: 0.2s linear;
}

.blog-item__content {
    padding: clamp(1rem, 0.391rem + 1.268vw, 1.5rem);
}

.blog-item__top {
    gap: clamp(1rem, -1.998rem + 4vw, 1.5rem);
}

.blog-item__tag {
    padding: 8px 16px;
    background-color: #F5F7F9;
}

.blog-item__title {
    margin: 24px 0 32px;
}

.blog-item__title .link {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* =========================== Blog Section Css End ========================== */
/* ======================= Become Seller Section Css start ==================== */
.seller-two {
    background-image: url(../images/shapes/wave-shape.png);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: top;
    position: relative;
    background-color: hsl(var(--white));
    padding-top: 216px;
}

@media screen and (max-width: 1199px) {
    .seller-two {
        padding-top: 120px;
        background-color: hsl(var(--section-bg));
        background-image: none;
    }
}

@media screen and (max-width: 575px) {
    .seller-two {
        padding-bottom: 60px;
    }
}

.seller-two::before {
    position: absolute;
    content: "";
    width: 835px;
    height: 682px;
    background: linear-gradient(152deg, rgba(246, 246, 246, 0.2) 17.12%, rgba(94, 53, 242, 0.2) 105.91%);
    filter: blur(100px);
    bottom: 56px;
    right: -280px;
}

.arrow-img {
    position: absolute;
    bottom: -40px;
    left: 25%;
}

.seller-item {
    padding: clamp(1.5rem, -2.38rem + 8.082vw, 4.6875rem) clamp(1.5rem, -0.174rem + 3.487vw, 2.875rem);
    border-radius: 16px;
    position: relative;
    z-index: 1;
    overflow: hidden;
    background-color: #F3EBFF;
}

.seller-item.bg-two {
    background-color: #FFCBE7;
}

.seller-item.bg-three {
    background-color: #D7FDFF;
}

.seller-item__title {
    margin-bottom: 24px;
}

.seller-item__desc {
    margin-bottom: clamp(1.5rem, 0.283rem + 2.536vw, 2.5rem);
}

.support {
    border-radius: 16px;
    background-color: #FFEFF4;
    position: relative;
    overflow: hidden;
    padding: 16px;
}

@media screen and (max-width: 767px) {
    .support {
        padding: 24px;
    }
}

.support::before {
    position: absolute;
    content: "";
    border-radius: 608px;
    width: 608px;
    height: 608px;
    transform: rotate(41deg);
    background-color: rgba(255, 80, 143, 0.3);
    filter: blur(75px);
    top: 88%;
    left: 0;
}

.support-content {
    padding-left: 24px;
}

@media screen and (max-width: 767px) {
    .support-content {
        padding-left: 0px;
    }
}

.support-content__desc {
    margin-bottom: 24px;
    font-size: clamp(1rem, 0.696rem + 0.634vw, 1.25rem);
}

.arrow-shape {
    position: absolute;
    left: 31%;
    top: 31%;
    max-width: 28%;
}

@media screen and (max-width: 991px) {
    .arrow-shape {
        display: none;
    }
}
/* ======================= Become Seller Section Css End ==================== */
/* ======================== Brand Section Css Start ========================= */
.brand {
    margin: 60px 0;
    position: relative;
    z-index: 2;
}

@media screen and (max-width: 767px) {
    .brand {
        margin: 40px;
    }
}

@media (min-width: 1200px) {
    .brand {
        margin-bottom: -70px;
        margin-top: 25px;
    }
}
/* ======================== Brand Section Css End ========================= */
/* ============================== Banner Two Css Start =========================== */
.banner-two {
    background-color: hsl(var(--gray-seven));
    padding: 160px 0;
    position: relative;
}

@media screen and (max-width: 1199px) {
    .banner-two {
        padding: 120px 0;
    }
}

@media screen and (max-width: 991px) {
    .banner-two {
        padding: 100px 0;
    }
}

@media screen and (max-width: 767px) {
    .banner-two {
        padding: 80px 0;
    }
}

.banner-two__content {
    max-width: 880px;
    margin: 0 auto;
}

@media (min-width: 992px) and (max-width: 1299px) {
    .banner-two__title {
        font-size: 60px;
    }
}

.banner-two__desc {
    margin-bottom: 32px;
    max-width: 590px;
    margin-left: auto;
    margin-right: auto;
}

@media screen and (max-width: 1199px) {
    .banner-two__desc {
        margin-bottom: 24px;
    }
}

.popular-search {
    margin-top: clamp(1.5rem, -1.543rem + 6.339vw, 4rem);
}

.search-list {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
}

.search-list__link {
    padding: 3px 10px;
    border-radius: 50px;
    border: 1px solid hsl(var(--border-color));
    outline: 1px solid transparent;
}

.search-list__link:hover {
    color: hsl(var(--main)) !important;
    font-weight: 600;
    border-color: hsl(var(--main));
    background-color: hsl(var(--main)/0.08);
    outline-color: hsl(var(--main));
}

/* Statistics */
.statistics-wrapper {
    display: flex;
    align-items: center;
    gap: 36px;
}

@media screen and (max-width: 1399px) {
    .statistics-wrapper {
        display: inline-grid;
        grid-template-columns: 1fr 1fr;
        width: 100%;
        gap: 24px;
    }
}

@media screen and (max-width: 767px) {
    .statistics-wrapper {
        gap: 14px;
    }
}

@media (min-width: 1200px) {
    .statistics-wrapper.style-right .statistics:nth-child(1) {
        margin-top: 96px;
    }
}

.statistics-wrapper.style-right .statistics:nth-child(2) {
    margin-top: 0px;
}

@media (min-width: 1200px) {
    .statistics-wrapper .statistics:nth-child(2) {
        margin-top: 96px;
    }
}
/* ============================== Banner Two Css End =========================== */
/* ============================ Popular Item Section Start =========================== */
.popular-item-card-section {
    position: relative;
    z-index: 1;
}

.popular-item-card-section::before {
    position: absolute;
    content: "";
    width: 675px;
    height: 552px;
    background: linear-gradient(152deg, rgba(246, 246, 246, 0.2) 17.12%, rgba(94, 53, 242, 0.2) 105.91%);
    filter: blur(100px);
    left: 0;
    bottom: 200px;
    z-index: -1;
}

.popular-item-card {
    background-color: hsl(var(--white));
    padding: 20px;
    box-shadow: var(--box-shadow);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0px 5px 20px 0px rgba(189, 190, 204, 0.4);
}

.popular-item-card:hover {
    box-shadow: 0px 20px 30px 0px rgba(197, 196, 201, 0.25);
}

.popular-item-card:hover .product-card__bottom {
    visibility: visible;
    opacity: 1;
}

.popular-item-card:hover .popular-item-card__thumb::before {
    height: 100%;
    visibility: visible;
    opacity: 1;
    z-index: 1;
}

.popular-item-card:hover .popular-item-card__thumb img {
    transform: scale(1.1);
}

.popular-item-card__thumb {
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    z-index: 1;
}

.popular-item-card__thumb::before {
    position: absolute;
    content: "";
    width: 100%;
    height: 0%;
    left: 0;
    bottom: 0;
    border-radius: 8px;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(0, 4, 37, 0.5) 69.5%, rgba(0, 4, 37, 0.5) 100%);
    pointer-events: none;
    visibility: hidden;
    opacity: 0;
    transition: 0.2s linear;
}

.popular-item-card__thumb img {
    transition: 0.2s linear;
}

.popular-item-card .product-card__bottom {
    position: absolute;
    bottom: 0;
    color: hsl(var(--white));
    z-index: 2;
    width: 100%;
    padding: 16px;
    padding-top: 0;
    margin-top: 0;
    border-top: 0;
    visibility: hidden;
    opacity: 0;
    transition: 0.2s linear;
}

.popular-item-card .product-card__author {
    color: hsl(var(--white));
}

.popular-item-card__content {
    margin-top: 1px;
}

.popular-item-card__title {
    text-transform: capitalize;
    margin: 5px;
}

.popular-item-card__title .link {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.btn-link:hover {
    transform: scale(1.2);
}

/* ============================ Popular Item Section End =========================== */
/* ======================= Featured Contributor Section Css Start ================== */
.featured-contributor::before {
    position: absolute;
    content: "";
    background: linear-gradient(152deg, rgba(246, 246, 246, 0.2) 17.12%, rgba(94, 53, 242, 0.2) 105.91%);
    filter: blur(100px);
    width: 752px;
    height: 614px;
    right: -224px;
    bottom: 70px;
}

.contributor-item {
    background-color: hsl(var(--black));
    border-radius: 16px;
    padding: 8px;
    overflow: hidden;
    position: relative;
}

.contributor-item:hover {
    box-shadow: 0px 20px 30px 0px rgba(197, 196, 201, 0.25);
}

.contributor-item:hover .contributor-item__thumb::before, .contributor-item:hover .more-item__item .link::before, .contributor-item:hover .follower-item__item .link::before, .more-item__item .contributor-item:hover .link::before, .follower-item__item .contributor-item:hover .link::before {
    height: 100%;
    visibility: visible;
    opacity: 1;
    z-index: 1;
}

.contributor-item:hover .contributor-item__thumb img, .contributor-item:hover .more-item__item .link img, .contributor-item:hover .follower-item__item .link img, .more-item__item .contributor-item:hover .link img, .follower-item__item .contributor-item:hover .link img {
    transform: scale(1.1);
}

.contributor-item:hover .contributor-item__link {
    visibility: visible;
    opacity: 1;
}

.contributor-item__thumb, .more-item__item .link, .follower-item__item .link {
    border-radius: 16px;
    overflow: hidden;
    position: relative;
}

.contributor-item__thumb::before, .more-item__item .link::before, .follower-item__item .link::before {
    position: absolute;
    content: "";
    width: 100%;
    height: 0%;
    left: 0;
    bottom: 0;
    border-radius: 8px;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(0, 4, 37, 0.5) 69.5%, rgba(0, 4, 37, 0.5) 100%);
    pointer-events: none;
    visibility: hidden;
    opacity: 0;
    transition: 0.2s linear;
}

.contributor-item__thumb img, .more-item__item .link img, .follower-item__item .link img {
    transition: 0.2s linear;
}

.contributor-item__link {
    position: absolute;
    z-index: 1;
    right: 16px;
    bottom: 16px;
    visibility: hidden;
    opacity: 0;
    transition: 0.2s linear;
}

.contributor-item__link:hover {
    transform: scale(1.2);
}

.contributor-item__badge {
    position: absolute;
    right: 24px;
    top: 0;
}

.contributor-info {
    padding: 12px 8px;
    padding-bottom: 20px;
}

.contributor-info__thumb {
    width: 50px;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    background-color: hsl(var(--white));
}

/* ======================= Featured Contributor Section Css Start ================== */
/* ======================== Service Section Css Start ========================== */
.service {
    background-color: #F3EBFF;
}

.service-item {
    border-radius: 16px;
    background-color: hsl(var(--white));
    overflow: hidden;
    padding: 32px;
    transition: 0.2s linear;
    position: relative;
    z-index: 1;
    height: 100%;
}

.service-item:hover {
    background-color: hsl(var(--black));
}

.service-item:hover .hover-bg {
    visibility: visible;
    opacity: 1;
}

.service-item:hover .service-item__title {
    color: hsl(var(--white));
}

.service-item:hover .service-item__desc {
    color: hsl(var(--white));
}

.service-item:hover .btn-simple {
    color: hsl(var(--white));
}

@media screen and (max-width: 1199px) {
    .service-item {
        padding: 24px;
    }
}

@media screen and (max-width: 991px) {
    .service-item {
        padding: 24px 16px;
    }
}

.service-item__icon {
    width: 80px;
    height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    background-color: #F5F7F9;
}

@media screen and (max-width: 575px) {
    .service-item__icon {
        width: 60px;
        height: 60px;
    }
}

.service-item__title {
    transition: 0.2s linear;
}

.service-item__desc {
    margin-bottom: 24px;
    transition: 0.2s linear;
}

@media screen and (max-width: 991px) {
    .service-item__desc {
        margin-bottom: 16px;
    }
}

.hover-bg-main {
    position: relative;
    z-index: 1;
}

.hover-bg-main:hover .hover-bg {
    visibility: visible;
    opacity: 1;
}

.hover-bg {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    visibility: hidden;
    opacity: 0;
    transition: 0.2s linear;
    z-index: -1;
}

.btn-simple {
    display: flex;
    align-items: center;
    gap: 8px;
    color: hsl(var(--heading-color));
    font-weight: 500;
}

.btn-simple:hover {
    color: hsl(var(--main));
}

.btn-simple:hover .icon {
    margin-left: 8px;
}

.btn-simple .icon {
    transition: 0.2s linear;
}

/* ======================== Service Section Css End ========================== */
/* ======================= Testimonial Section Css Start ============================ */
.testimonial .slick-arrow {
    top: 48%;
    margin-top: -2px;
}

@media screen and (max-width: 1199px) {
    .testimonial .slick-arrow {
        top: 52%;
    }
}

@media screen and (max-width: 991px) {
    .testimonial .slick-arrow {
        top: 45%;
    }
}

@media screen and (max-width: 767px) {
    .testimonial .slick-arrow {
        top: 50%;
    }
}

@media screen and (max-width: 424px) {
    .testimonial .slick-arrow {
        top: 53%;
    }
}

.testimonial-item {
    padding: 40px 32px;
    background-color: hsl(var(--white));
    border-radius: 16px;
    transition: 0.2s linear;
}

@media screen and (max-width: 1199px) {
    .testimonial-item {
        padding: 32px;
    }
}

@media screen and (max-width: 991px) {
    .testimonial-item {
        padding: 24px;
    }
}

@media screen and (max-width: 575px) {
    .testimonial-item {
        padding: 24px 16px;
    }
}

.testimonial-item:hover {
    background-color: hsl(var(--black));
}

.testimonial-item:hover .testimonial-item__desc {
    color: hsl(var(--white));
}

.testimonial-item:hover .quote-white {
    visibility: visible;
    opacity: 1;
}

.testimonial-item:hover .quote-dark {
    visibility: hidden;
    opacity: 0;
}

.testimonial-item:hover .client-info__name {
    color: hsl(var(--white));
}

.testimonial-item:hover .client-info__designation {
    color: hsl(var(--white)) !important;
    font-weight: 400 !important;
}

.testimonial-item__desc {
    font-size: clamp(0.9375rem, 0.557rem + 0.792vw, 1.25rem);
    margin-bottom: clamp(1.5rem, 0.283rem + 2.536vw, 2.5rem);
}

.testimonial-item__quote {
    text-align: end;
    position: relative;
    z-index: 1;
    height: 25px;
    margin-bottom: clamp(1.5rem, 0.283rem + 2.536vw, 2.5rem);
}

.testimonial-item__quote::before {
    position: absolute;
    content: "";
    width: calc(100% - 40px);
    height: 1px;
    top: 50%;
    transform: translateY(-50%);
    background: hsl(var(--gray-five));
    left: -32px;
    z-index: -1;
}

@media screen and (max-width: 991px) {
    .testimonial-item__quote::before {
        left: -24px;
    }
}

.testimonial-item__quote .quote {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
}

.testimonial-item__quote .quote-white {
    visibility: hidden;
    opacity: 0;
}

/* Client Info */
.client-info__thumb {
    width: 72px;
    height: 72px;
    border-radius: 50%;
    overflow: hidden;
}

.client-info__name {
    transition: 0.2s linear;
}

.client-info__designation {
    transition: 0.2s linear;
}

/* ======================= Testimonial Section Css End ============================ */
/* ========================= Pricing Plan Section Css Start ============================ */
.pricing-tabs {
    position: relative;
    background-color: #F5F7F9;
    padding: 8px;
    border-radius: 56px;
}

@media screen and (max-width: 1199px) {
    .pricing-tabs {
        padding: 4px;
    }
}

.pricing-tabs::before {
    position: absolute;
    content: "";
    width: calc(100% + 2px);
    height: calc(100% + 2px);
    background: var(--main-gradient);
    left: -1px;
    top: -1px;
    border-radius: inherit;
    z-index: -1;
}

.pricing-item {
    border-radius: 16px;
    background-color: #F5F7F9;
    padding: 32px 24px;
    background-color: hsl(var(--white));
    box-shadow: var(--box-shadow);
    transition: 0.2s linear;
}

.pricing-item:hover {
    background-color: hsl(var(--black));
}

.pricing-item:hover .pricing-item__top::before {
    background-color: hsl(var(--white)/0.3);
}

.pricing-item:hover .pricing-item__title {
    color: hsl(var(--white));
}

.pricing-item:hover .pricing-item__price {
    color: hsl(var(--white));
}

.pricing-item:hover .pricing-item__price .text {
    color: hsl(var(--white)) !important;
}

.pricing-item:hover .pricing-item__desc {
    color: hsl(var(--white));
}

.pricing-item:hover .text-list__item {
    color: hsl(var(--white)) !important;
}

.pricing-item:hover .btn-outline-light {
    background: var(--main-gradient);
    border-color: transparent !important;
    color: hsl(var(--white)) !important;
    transition: 0.2s linear;
}

.pricing-item:hover .btn-outline-light:hover {
    transform: scale(1.04);
}

.pricing-item__top {
    padding-bottom: clamp(1rem, -0.217rem + 2.536vw, 2rem);
    margin-bottom: clamp(1rem, -0.217rem + 2.536vw, 2rem);
    position: relative;
}

.pricing-item__top::before {
    position: absolute;
    content: "";
    width: calc(100% + 48px);
    height: 1px;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0;
    background-color: hsl(var(--gray-five));
    transition: 0.2s linear;
}

.pricing-item__icon {
    border-radius: 50%;
    border: 1px solid hsl(var(--white-one));
    background-color: hsl(var(--white));
    width: 72px;
    height: 72px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
}

.pricing-item__title {
    transition: 0.2s linear;
}

.pricing-item__price {
    transition: 0.2s linear;
}

.pricing-item__desc {
    margin-bottom: clamp(1.5rem, 0.283rem + 2.536vw, 2.5rem);
    transition: 0.2s linear;
}

.pricing-item__lists {
    margin-top: clamp(1.5rem, 0.283rem + 2.536vw, 2.5rem);
}

.text-list__item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    transition: 0.2s linear;
}

.text-list__item:last-child {
    margin-bottom: 0;
}

.text-list__item .icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 1px solid hsl(var(--white-one));
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
}

.popular-badge {
    background-color: hsl(var(--main-two));
    color: hsl(var(--heading-color));
    padding: 3px 16px;
    padding-left: 28px;
    position: relative;
    right: -24px;
    clip-path: polygon(0 0, 100% 0, 100% 50%, 100% 100%, 0 100%, 8% 52%);
    font-size: 18px;
}

@media screen and (max-width: 767px) {
    .popular-badge {
        font-size: 13px;
    }
}
/* ========================= Pricing Plan Section Css End ============================ */
/* =========================== Article Section Css Start ============================ */
.article-item-wrapper {
    overflow-x: auto;
    overflow-y: hidden;
}

.article-item {
    display: flex;
    justify-content: space-between;
    gap: clamp(1rem, -0.826rem + 3.803vw, 2.5rem);
    border: 1px solid hsl(var(--gray-five));
    border-left: 0;
    border-right: 0;
}

@media screen and (max-width: 767px) {
    .article-item {
        min-width: max-content;
    }
}

.article-item:hover .article-item__thumb {
    top: 16px;
    visibility: visible;
    opacity: 1;
}

@media screen and (max-width: 767px) {
    .article-item:hover .article-item__thumb {
        top: 0;
    }
}

.article-item__inner {
    gap: clamp(1.5rem, -2.38rem + 8.082vw, 4.6875rem);
    flex-grow: 1;
    padding: 32px 0;
}

@media screen and (max-width: 1199px) {
    .article-item__inner {
        padding: 24px 0;
    }
}

@media screen and (max-width: 767px) {
    .article-item__inner {
        padding: 16px 0;
    }
}

.article-item__center {
    gap: clamp(1.5rem, -3.521rem + 10.46vw, 5.625rem);
}

.article-item__content {
    max-width: 476px;
}

@media screen and (max-width: 991px) {
    .article-item__content {
        max-width: 330px;
    }
}

.article-item__top {
    gap: clamp(0.5rem, -0.717rem + 2.536vw, 1.5rem);
    margin-bottom: clamp(0.75rem, -0.163rem + 1.902vw, 1.5rem);
}

.article-item__tag {
    padding: 4px 16px;
    border-radius: 50px;
    border: 1px solid hsl(var(--gray-five));
    color: hsl(var(--black-three));
}

.article-item__tag:hover {
    border-color: hsl(var(--main));
    color: hsl(var(--main));
}

.article-item__thumb {
    max-width: 300px;
    max-height: 190px;
    border-radius: 8px;
    overflow: hidden;
    position: absolute;
    right: 40px;
    z-index: 1;
    top: 32px;
    transform: rotate(-15deg);
    visibility: hidden;
    opacity: 0;
    transition: 0.2s linear;
}

@media screen and (max-width: 767px) {
    .article-item__thumb {
        position: relative;
        visibility: visible;
        opacity: 1;
        top: 0px;
        transform: rotate(0);
        right: 0;
    }
}

.article-item__end {
    margin-right: 24px;
    margin-top: clamp(1rem, -0.826rem + 3.803vw, 2.5rem);
}

@media screen and (max-width: 1199px) {
    .article-item__end {
        margin-right: 0;
    }
}

.user-info__thumb {
    width: 86px;
    height: 86px;
    border-radius: 50%;
    overflow: hidden;
}

/* =========================== Article Section Css End ============================ */
/* ========================= Resource Section Css Start =========================== */
.resource {
    position: relative;
}

.resource.style-two::before {
    background: linear-gradient(152deg, rgba(246, 246, 246, 0.2) 17.12%, rgba(94, 53, 242, 0.2) 105.91%);
    bottom: auto;
}

.resource::before {
    position: absolute;
    content: "";
    background: linear-gradient(152deg, rgba(246, 246, 246, 0.2) 17.12%, rgba(5, 228, 242, 0.2) 105.91%);
    filter: blur(100px);
    width: 712px;
    height: 582px;
    left: -224px;
    bottom: 60px;
    z-index: -1;
}

.resource .slick-arrow {
    top: 96px;
    transform: unset;
}

/* ========================= Resource Section Css End =========================== */
/* ====================== Newsletter Section Css Start ===================== */
.newsletter {
    background-color: hsl(var(--black));
    padding: 52px 0;
}

.newsletter-box .btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
}

@media screen and (max-width: 767px) {
    .newsletter-box .btn {
        right: 4px;
        padding: 17px 24px;
    }
}

.newsletter-box .common-input {
    padding-right: 210px;
}

@media screen and (max-width: 767px) {
    .newsletter-box .common-input {
        padding-right: 130px;
    }
}

.newsletter-man {
    position: absolute;
    bottom: 0;
    left: clamp(0rem, -37.89rem + 43.333vw, 8.125rem);
    z-index: -1;
}

@media screen and (max-width: 1199px) {
    .newsletter-man {
        display: none;
    }
}
/* Newsletter Two Css Star */
.newsletter-two-content {
    max-width: 600px;
}

/* Newsletter Two Css End */
/* ====================== Newsletter Section Css End ===================== */
/* ======================== Breadcrumb One Section Css Start ===================== */
.breadcrumb {
    margin-bottom: 0;
    padding: 60px 0;
}

.breadcrumb-one-content {
    max-width: 704px;
}

.breadcrumb-one-content__desc {
    margin-bottom: 32px;
}

@media screen and (max-width: 991px) {
    .breadcrumb-one-content__desc {
        margin-bottom: 24px;
    }
}

.breadcrumb-one-content .search-box {
    max-width: 636px;
    margin: 0 auto;
}

/* ======================== Breadcrumb One Section Css End ===================== */
/* ======================== Breadcrumb Two Section Css End ===================== */
.breadcrumb-two {
    padding: 72px 0;
}

@media screen and (max-width: 991px) {
    .breadcrumb-two {
        padding: 60px 0;
    }
}

@media screen and (max-width: 991px) {
    .breadcrumb-content__item {
        font-size: 0.875rem;
    }
}

@media screen and (max-width: 575px) {
    .breadcrumb-tab {
        margin-bottom: 24px;
    }
}
/* Social Icons Css Start */
.social-share {
    position: relative;
}

.social-share__button {
    color: hsl(var(--body-color));
}

.social-share__icons {
    background-color: hsl(var(--white));
    padding: 12px;
    border-radius: 5px;
    position: absolute;
    right: 0;
    top: 100%;
    min-width: max-content;
    filter: drop-shadow(0px 4px 10px rgba(0, 0, 0, 0.2));
    visibility: hidden;
    opacity: 0;
    margin-top: 16px;
    transition: 0.1s linear;
}

.social-share__icons.show {
    visibility: visible;
    opacity: 1;
    margin-top: 6px;
}

.social-share__icons.left {
    right: auto;
    left: 0;
}

.social-share__icons.left::before {
    left: 8px;
    right: auto;
}

.social-share__icons::before {
    position: absolute;
    content: "";
    width: 14px;
    height: 14px;
    top: -7px;
    right: 8px;
    transform: rotate(45deg);
    background-color: hsl(var(--white));
    border-radius: 3px;
}

/* Social Icons Css End */
/* ======================== Breadcrumb Two Section Css End ===================== */
/* ======================== Breadcrumb Three Section Css Start ===================== */
.breadcrumb-three {
    padding-top: 120px;
}

@media screen and (max-width: 575px) {
    .breadcrumb-three {
        padding-top: 100px;
    }
}

.breadcrumb-three-content {
    border-radius: 8px 8px 0 0;
    position: relative;
    z-index: 1;
}

.breadcrumb-three-content::before {
    position: absolute;
    content: "";
    width: calc(100% + 104px);
    height: 100%;
    left: 50%;
    transform: translateX(-50%);
    top: 0;
    background-color: hsl(var(--white));
    z-index: -1;
}

.author-profile__thumb {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: var(--main-gradient);
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 1;
    margin-top: -60px;
}

.author-profile__thumb::before {
    position: absolute;
    content: "";
    width: calc(100% - 2px);
    height: calc(100% - 2px);
    background-color: hsl(var(--white));
    left: 1px;
    top: 1px;
    border-radius: inherit;
    z-index: -1;
}

/* ======================== Breadcrumb Three Section Css End ===================== */
/* ===================== Breadcrumb Four Start ======================== */
.process-list {
    margin-top: clamp(2rem, -1.195rem + 6.656vw, 4.625rem);
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    background-color: hsl(var(--white)/0.3);
    border: 1px solid hsl(var(--white));
    border-radius: 16px;
    padding: 12px;
    backdrop-filter: blur(5px);
    gap: 10px;
}

@media screen and (max-width: 767px) {
    .process-list {
        display: grid;
        grid-template-columns: 1fr 1fr;
    }
}

.process-list__item:last-child .process-list__link::after {
    display: none;
}

.process-list__item.activePage .process-list__link, .process-list__item:hover .process-list__link {
    color: hsl(var(--white));
}

.process-list__item.activePage .process-list__link::before, .process-list__item:hover .process-list__link::before {
    visibility: visible;
    opacity: 1;
}

.process-list__item.activePage .process-list__link::after, .process-list__item:hover .process-list__link::after {
    border-color: hsl(var(--black-three));
}

.process-list__item.activePage .process-list__link .icons .icon.white, .process-list__item:hover .process-list__link .icons .icon.white {
    visibility: visible;
    opacity: 1;
}

.process-list__item.activePage .process-list__link .icons .icon.colored, .process-list__item:hover .process-list__link .icons .icon.colored {
    visibility: hidden;
    opacity: 0;
}

.process-list__link {
    padding: 14px 26px;
    text-align: center;
    background-color: hsl(var(--white));
    border-radius: 16px;
    position: relative;
    z-index: 1;
    color: hsl(var(--body));
    border: 1px solid hsl(var(--white-one)/0.3);
    width: 100%;
    font-size: 0.875rem;
}

@media screen and (max-width: 991px) {
    .process-list__link {
        padding: 16px;
    }
}

@media screen and (max-width: 767px) {
    .process-list__link {
        padding: 16px 10px;
        font-size: 0.75rem;
    }
}

.process-list__link::before {
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    border-radius: inherit;
    background: var(--main-gradient);
    left: 0;
    top: 0;
    z-index: -1;
    visibility: hidden;
    opacity: 0;
    transition: 0.2s linear;
}

.process-list__link::after {
    position: absolute;
    content: "";
    width: 130px;
    height: 1px;
    right: -100%;
    top: 50%;
    transform: translateY(-50%);
    border: 1px dashed hsl(var(--black-three)/0.5);
}

@media screen and (max-width: 1199px) {
    .process-list__link::after {
        display: none;
    }
}

.process-list__link .icons {
    width: 26px;
    height: 26px;
    position: relative;
    margin: 0 auto;
}

.process-list__link .icons .icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    transition: 0.2s linear;
    display: flex;
    justify-content: center;
    align-items: center;
}

.process-list__link .icons .icon.white {
    visibility: hidden;
    opacity: 0;
}

.process-list__link .text {
    color: inherit;
    margin-top: 8px;
    font-size: inherit;
}

/* ===================== Breadcrumb Four End ======================== */
/* ======================== All Product Section Css Start ====================== */
/* Filter Tab Css Start */
.filter-tab {
    margin-bottom: 32px;
}

@media screen and (max-width: 1199px) {
    .filter-tab {
        gap: 24px;
    }
}

@media screen and (max-width: 1199px) {
    .filter-tab {
        gap: 16px;
    }
}

.filter-tab__right {
    gap: 24px;
}

@media screen and (max-width: 1199px) {
    .filter-tab__right {
        gap: 16px;
    }
}

@media screen and (max-width: 991px) {
    .filter-tab__button:hover {
        background-color: transparent !important;
        border-color: hsl(var(--white-one)) !important;
        color: hsl(var(--black)) !important;
    }
}

.filter-tab__button.active {
    background-color: hsl(var(--white-one)) !important;
    border-color: hsl(var(--white-one)) !important;
    color: hsl(var(--black)) !important;
}

.filter-form {
    display: none;
}

/* Filter Tab Css End */
/* List & Grid Buttons Start */
@media screen and (max-width: 991px) {
    .list-grid {
        order: -1;
    }
}

.list-grid__button {
    width: 36px;
    height: 36px;
    border: 1px solid hsl(var(--gray-five));
    border-radius: 8px;
    font-size: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: 0.2s linear;
}

.list-grid__button.active {
    border-color: hsl(var(--black)) !important;
    background: hsl(var(--black)) !important;
    color: hsl(var(--white)) !important;
}

/* List & Grid Buttons End */
/* List & Grid Card Css Start */
.list-view .list-grid-wrapper > div {
    width: 100%;
}

.list-view .list-grid-wrapper .product-card {
    display: flex;
}

.list-view .list-grid-wrapper .product-card__content {
    flex-grow: 1;
}

/* List & Grid Card Css End */
/* Filter Sidebar Css Start */
.filter-sidebar {
    background-color: hsl(var(--gray-seven));
    padding: 32px 16px;
    border-radius: 8px;
    position: relative;
}

@media screen and (max-width: 991px) {
    .filter-sidebar {
        position: fixed;
        left: 0;
        top: 0;
        z-index: 999;
        border-radius: 0;
        width: 300px;
        transform: translateX(-100%);
        transition: 0.2s linear;
        height: 100vh;
        overflow-y: auto;
        padding-top: 48px;
    }
}

.filter-sidebar.show {
    transform: translateX(0);
}

.filter-sidebar__item:last-child .filter-sidebar__content {
    margin-bottom: 0;
}

.filter-sidebar__content {
    margin-bottom: 44px;
}

@media screen and (max-width: 1199px) {
    .filter-sidebar__content {
        margin-bottom: 32px;
    }
}

@media screen and (max-width: 991px) {
    .filter-sidebar__content {
        margin-bottom: 24px;
    }
}

.filter-sidebar__button {
    margin-bottom: 16px;
    padding-bottom: 16px;
    width: 100%;
    text-align: left;
    position: relative;
}

.filter-sidebar__button.active::after {
    transform: rotate(180deg);
}

.filter-sidebar__button::before {
    position: absolute;
    content: "";
    width: calc(100% + 32px);
    height: 1px;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    background: hsl(var(--gray-five));
}

.filter-sidebar__button::after {
    position: absolute;
    content: "\f107";
    font-weight: 900;
    font-family: "Line Awesome Free";
    right: 0;
    top: 0px;
    transition: 0.2s linear;
}

.filter-sidebar-list__item {
    margin-bottom: 16px;
}

.filter-sidebar-list__item:last-child {
    margin-bottom: 0;
}

.filter-sidebar-list__text {
    font-size: 0.875rem;
    color: hsl(var(--black-three));
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: 0.2s linear;
}

.filter-sidebar-list__text:hover {
    color: hsl(var(--main));
}

.filter-sidebar-list__text:hover .form-check-label {
    color: hsl(var(--main));
}

.filter-sidebar-list__text .common-check {
    margin-bottom: 0;
}

.filter-sidebar-list__text .form-check-label {
    transition: 0.2s linear;
}

/* Filter Sidebar Css End */
/* ======================== All Product Section Css End ====================== */
/* ======================= Product Details Section Css Start ==================== */
.product-details__thumb {
    border-radius: 8px 8px 0 0;
    overflow: hidden;
}

.product-details__buttons {
    background-color: hsl(var(--section-bg));
    padding: 32px;
    border-radius: 0 0 8px 8px;
}

.product-details__desc {
    margin-top: 40px;
    margin-bottom: 24px;
}

@media screen and (max-width: 1199px) {
    .product-details__desc {
        margin-top: 32px;
    }
}

@media screen and (max-width: 991px) {
    .product-details__desc {
        margin-top: 24px;
    }
}

.product-details__item {
    margin-bottom: 24px;
}

/* Product List */
.product-list {
    list-style: disc;
    padding-left: 17px;
}

.product-list__item {
    margin-bottom: 16px;
}

.product-list__item:last-child {
    margin-bottom: 0;
}

/* More Item  */
.more-item, .follower-item {
    margin-top: 40px;
    padding-top: 40px;
    border-top: 1px solid hsl(var(--gray-five));
}

.more-item__content, .follower-item__content {
    gap: 15px;
}

.more-item__item .link:hover::before, .follower-item__item .link:hover::before {
    height: 100%;
    visibility: visible;
    opacity: 1;
    z-index: 1;
}

.more-item__item .link:hover img, .follower-item__item .link:hover img {
    transform: scale(1.1);
}

.more-item__item img, .follower-item__item img {
    width: 80px;
    height: 80px;
    border-radius: 4px;
}

/* Product Details Sidebar Css Start */
.product-sidebar {
    padding: 24px 32px;
    border-radius: 8px;
}

@media screen and (max-width: 1399px) {
    .product-sidebar {
        padding: 24px;
    }
}

@media screen and (max-width: 1199px) {
    .product-sidebar {
        padding: 24px 16px;
    }
}

.product-sidebar__top {
    padding-bottom: 24px;
    margin-bottom: 24px;
    border-bottom: 1px solid hsl(var(--gray-five));
}

.btn-has-dropdown {
    font-weight: 600;
    padding-right: 16px;
    position: relative;
}

.btn-has-dropdown::before {
    position: absolute;
    content: "\f107";
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    color: hsl(var(--heading-color));
    font-size: 1rem;
    transition: 0.2s linear;
}

.license-dropdown {
    box-shadow: var(--box-shadow);
    border: 1px solid hsl(var(--gray-five));
    padding: 16px;
    border-radius: 8px;
    max-width: 300px;
    transform: scale(0.9);
    visibility: hidden;
    opacity: 0;
    position: absolute;
    left: 0;
    top: calc(100% - 15px);
    transition: 0.2s linear;
    background-color: hsl(var(--white));
    z-index: 2;
}

.license-dropdown::before {
    position: absolute;
    content: "";
    width: 14px;
    height: 14px;
    top: -7px;
    left: 8px;
    transform: rotate(45deg);
    border: 1px solid hsl(var(--gray-five));
    background-color: hsl(var(--white));
    border-radius: 3px;
    border-bottom: 0;
    border-right: 0;
}

.license-dropdown.active {
    visibility: visible;
    opacity: 1;
    transform: scale(1);
}

.author-details {
    padding: 16px;
    border-radius: 8px;
    border: 1px solid hsl(var(--gray-five));
    margin-top: 24px;
}

.author-details__thumb {
    width: 72px;
    height: 72px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    background-color: hsl(var(--white));
}

@media screen and (max-width: 1199px) {
    .author-details__thumb {
        width: 60px;
        height: 60px;
    }
}
/* badge list Css Start */
.badge-list {
    margin-left: 80px;
}

@media screen and (max-width: 1199px) {
    .badge-list {
        margin-left: 68px;
    }
}

.badge-list__item {
    width: 48px;
    height: 52px;
    background-image: url(../images/shapes/polygon-shape.png);
    background-repeat: no-repeat;
    background-size: cover;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
}

.badge-list--sm .badge-list__item {
    width: 36px;
    height: 40px;
    background-image: url(../images/shapes/polygon-shape.png);
    background-repeat: no-repeat;
    background-size: cover;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
}

/* badge list Css End */
.meta-attribute {
    margin-top: 32px;
}

@media screen and (max-width: 1199px) {
    .meta-attribute {
        margin-top: 24px;
    }
}

.meta-attribute__item {
    display: flex;
    align-items: start;
    gap: 24px;
    margin-bottom: clamp(1rem, 0.391rem + 1.268vw, 1.5rem);
}

.meta-attribute__item .name {
    font-size: 1rem;
    color: hsl(var(--heading-color));
    width: 140px;
    flex-shrink: 0;
}

.meta-attribute__item .details {
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.meta-attribute__item .details > a {
    font-size: inherit;
}

/* Product Details Sidebar Css End */
/* Product Review Css Start */
.product-review, .user-comment {
    background-color: hsl(var(--white));
    padding: 16px clamp(1rem, 0.391rem + 1.268vw, 1.5rem);
    border: 1px solid hsl(var(--border-color));
    margin-bottom: clamp(1rem, 0.391rem + 1.268vw, 1.5rem);
    border-radius: 8px;
}

.product-review:last-child, .user-comment:last-child {
    margin-bottom: 0;
}

.product-review__top {
    padding-bottom: 16px;
    margin-bottom: clamp(1rem, 0.391rem + 1.268vw, 1.5rem);
    border-bottom: 1px solid hsl(var(--border-color));
}

.product-review__rating {
    gap: 12px;
}

@media screen and (max-width: 991px) {
    .product-review__rating {
        gap: 4px;
    }
}

@media screen and (max-width: 1199px) {
    .product-review__reason, .product-review__subject, .product-review__date, .product-review__desc {
        font-size: 0.875rem;
    }
}

@media screen and (max-width: 767px) {
    .product-review__reason, .product-review__subject, .product-review__date, .product-review__desc {
        font-size: 0.875rem;
    }
}

.product-review__subject {
    color: hsl(var(--heading-color));
    font-weight: 700;
}

.product-review__user:hover {
    text-decoration: underline;
}

.author-reply, .user-comment__profile {
    display: flex;
    flex-wrap: wrap;
    border-top: 1px solid hsl(var(--border-color));
    padding-top: clamp(1rem, 0.391rem + 1.268vw, 1.5rem);
    margin-top: clamp(1rem, 0.391rem + 1.268vw, 1.5rem);
}

.author-reply__thumb, .user-comment__thumb {
    width: 56px;
    height: 56px;
}

@media screen and (max-width: 575px) {
    .author-reply__thumb, .user-comment__thumb {
        width: 40px;
        height: 40px;
    }
}

.author-reply__content, .user-comment__info {
    width: calc(100% - 56px);
    padding-left: 16px;
}

@media screen and (max-width: 575px) {
    .author-reply__content, .user-comment__info {
        width: calc(100% - 40px);
        padding-left: 8px;
    }
}

.author-reply__name, .user-comment__name {
    font-size: 1.125rem;
    margin-bottom: 0;
}

.author-reply__response, .user-comment__purchase {
    font-weight: 600;
    font-size: 0.875rem;
    margin-bottom: 16px;
    display: block;
}

@media screen and (max-width: 767px) {
    .author-reply__response, .user-comment__purchase {
        font-weight: 500;
    }
}

.author-reply__desc {
    margin-bottom: 12px;
}

@media screen and (max-width: 767px) {
    .author-reply__desc {
        font-size: 0.875rem;
    }
}

.author-reply__desc:last-of-type {
    margin-bottom: 0;
}

.review-reply {
    display: flex;
    flex-wrap: wrap;
    margin-top: 24px;
    padding-top: 32px;
    border-top: 1px solid hsl(var(--border-color));
}

.review-reply__thumb {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    overflow: hidden;
}

.review-reply__content {
    width: calc(100% - 56px);
    padding-left: 12px;
}

.review-reply__button {
    margin-top: 12px;
}

/* Product Review Css End */
/* ======================= Product Details Section Css End ==================== */
/* ===================== Profile Section Start ============================== */
@media screen and (max-width: 1199px) {
    .profile .common-tab {
        width: 100%;
    }

    .profile .search-box.style-three {
        order: -1;
    }
}

@media screen and (max-width: 1199px) and (max-width: 575px) {
    .profile .search-box.style-three {
        width: 100%;
        max-width: unset;
    }
}

.profile-wrapper {
    display: flex;
    align-items: start;
    gap: 32px;
    overflow: hidden;
}

@media screen and (max-width: 991px) {
    .profile-wrapper {
        flex-direction: column;
    }
}

.profile-content {
    width: 100%;
}

@media (min-width: 992px) {
    .profile-content {
        width: 68%;
    }
}

.profile-content__item {
    margin-bottom: 32px;
    border-radius: 8px;
}

.profile-content__item:last-child {
    margin-bottom: 0;
}

.follower-item__item .link {
    border-radius: 8px;
}

/* profile sidebar Css Start */
.profile-sidebar {
    flex-grow: 1;
    width: 100%;
}

@media (min-width: 992px) {
    .profile-sidebar {
        max-width: 478px;
    }
}

.profile-sidebar__title {
    margin-bottom: 32px;
    padding-bottom: 32px;
    position: relative;
}

.profile-sidebar__title::before {
    position: absolute;
    content: "";
    width: calc(100% + 64px);
    height: 1px;
    left: 50%;
    bottom: 0;
    transform: translateX(-50%);
    border-top: 1px solid hsl(var(--gray-five));
}

.profile-sidebar__item {
    background-color: hsl(var(--section-bg));
    padding: 32px;
    margin-bottom: 24px;
    border-radius: 8px;
}

.profile-sidebar__item:last-child {
    margin-bottom: 0;
}

.profile-sidebar__author {
    padding-top: 32px;
    margin-top: 32px;
    position: relative;
}

.profile-sidebar__author::before {
    position: absolute;
    content: "";
    width: calc(100% + 64px);
    height: 1px;
    left: 50%;
    top: 0;
    transform: translateX(-50%);
    border-top: 1px solid hsl(var(--gray-five));
}

/* profile sidebar Css End */
/* ===================== Profile Section End ============================== */
/* ======================== Follow Css Start ============================== */
.follow-wrapper {
    background-color: hsl(var(--section-bg));
    padding: 24px 16px;
    border-radius: 8px;
    overflow-x: auto;
}

.notification {
    background: var(--main-gradient);
    padding: 3px 5px;
    line-height: 1;
    border-radius: 50px;
    color: hsl(var(--white));
    font-size: 11px;
    font-weight: 500;
    margin-left: 4px;
    min-width: 17px;
}

.follow-item {
    position: relative;
    padding-bottom: 22px;
    margin-bottom: 22px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
    min-width: max-content;
}

.follow-item:last-child {
    padding-bottom: 0;
    margin-bottom: 0;
}

.follow-item:last-child::before {
    display: none;
}

.follow-item::before {
    position: absolute;
    content: "";
    width: calc(100% + 32px);
    height: 1px;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0;
    background-color: hsl(var(--gray-five));
}

.follow-item__meta {
    min-width: max-content;
    max-width: 177px;
}

.follow-item__sales {
    min-width: max-content;
}

.follow-item__author {
    max-width: 316px;
}

/* ======================== Follow Css End ============================== */
/* ========================= Setting Css Start ======================== */
.setting-sidebar {
    background-color: hsl(var(--section-bg));
    padding: 24px;
    border-radius: 16px;
    position: sticky;
    top: 120px;
}

.setting-sidebar.top-24 {
    top: 24px;
}

.setting-sidebar-list__link {
    width: 100%;
    padding: 10px 16px;
    border-radius: 8px;
    color: hsl(var(--body-color));
}

.setting-sidebar-list__link:hover, .setting-sidebar-list__link.active {
    background-color: hsl(var(--main));
    color: hsl(var(--white));
}

/* ========================= Setting Css End ======================== */
/* ========================== Hidden Item Css Start =========================== */
.product-reject__wrapper > div {
    margin-bottom: 24px;
}

.product-reject__wrapper > div:last-child {
    margin-bottom: 0;
}

.product-reviewer__thumb {
    width: 72px;
    height: 72px;
    border-radius: 50%;
    overflow: hidden;
}

.product-reject-info {
    padding-bottom: 24px;
    margin-bottom: 24px;
    border-bottom: 1px solid hsl(var(--gray-five));
}

.product-reject-info__thumb {
    width: 72px;
    height: 72px;
    border-radius: 8px;
    overflow: hidden;
}

.response-list__item {
    margin-bottom: 32px;
}

.response-list__item:last-child {
    margin-bottom: 0;
}

/* ========================== Hidden Item Css End =========================== */
/* ========================= Earning Section Css Start ============================= */
.earning-card {
    background-color: hsl(var(--black));
    padding: 24px;
    border-radius: 8px;
    border: 1px solid hsl(var(--gray-five));
    box-shadow: var(--box-shadow);
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.earning-card__amount {
    border-color: hsl(var(--white)/0.2) !important;
}

/* ========================= Earning Section Css End ============================= */
/* ========================= Statement Section Css start =========================== */
.statement-list {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* ========================= Statement Section Css End =========================== */
/* ==================== Table Review Product Css Start ========================= */
.review-product__thumb {
    width: 40px;
    height: 40px;
    border-radius: 5px;
    overflow: hidden;
}

/* ==================== Table Review Product Css End ========================= */
/* ========================= Download Section Css Start ====================== */
.download-wrapper {
    background-color: hsl(var(--section-bg));
    padding: 24px;
    border-radius: 16px;
    overflow-x: auto;
}

.download-item {
    position: relative;
    padding-bottom: 24px;
    margin-bottom: 24px;
    min-width: max-content;
}

.download-item:last-child {
    padding-bottom: 0;
    margin-bottom: 0;
}

.download-item:last-child::before {
    display: none;
}

.download-item::before {
    position: absolute;
    content: "";
    width: calc(100% + 48px);
    left: 50%;
    bottom: 0;
    transform: translateX(-50%);
    background-color: hsl(var(--gray-five));
    height: 1px;
}

.download-item__thumb {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
}

/* ========================= Download Section Css End ====================== */
/* ============================ Cart Page Css Start ===================== */
.cart-content__bottom {
    margin-top: 64px;
}

@media screen and (max-width: 1199px) {
    .cart-content__bottom {
        margin-top: 48px;
    }
}

@media screen and (max-width: 991px) {
    .cart-content__bottom {
        margin-top: 40px;
    }
}

@media screen and (max-width: 767px) {
    .cart-content__bottom {
        margin-top: 32px;
    }
}

@media screen and (max-width: 575px) {
    .cart-content__bottom .btn {
        padding: 17px 20px;
    }
}

.cart-item {
    max-width: 342px;
}

.cart-item__thumb {
    width: 164px;
    height: 130px;
    flex-shrink: 0;
    display: flex;
    border-radius: 8px;
    overflow: hidden;
}

.cart-item__count {
    width: auto;
    display: inline-flex;
    align-items: center;
    border: 1px solid hsl(var(--gray-four));
    border-radius: 5px;
    margin: 0 auto;
}

.cart-item__count input {
    background: transparent;
    border: 0;
    text-align: center;
    width: 24px;
    color: hsl(var(--heading-color));
    font-size: 16px;
}

.cart-item__count input:focus {
    outline: none;
}

.cart-item__count button {
    width: 40px;
    height: 40px;
    font-size: 16px;
    color: hsl(var(--body-color));
    border-radius: inherit;
}

.cart-item__count button:hover {
    color: hsl(var(--main));
}

/* ============================ Cart Page Css End ===================== */
/* =================== Cart Personal Start ========================== */
.order-summary {
    background-color: hsl(var(--section-bg));
    border-radius: 8px;
    padding: 24px;
}

.billing-list__item {
    position: relative;
    padding-bottom: 16px;
    margin-bottom: 16px;
}

.billing-list__item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
}

.billing-list__item:last-child::before {
    display: none;
}

.billing-list__item::before {
    position: absolute;
    content: "";
    width: calc(100% + 48px);
    left: 50%;
    height: 1px;
    transform: translateX(-50%);
    background-color: hsl(var(--gray-five));
    bottom: 0;
}

/* =================== Cart Personal End ========================== */
/* ======================= Cart Payment Section Css Start ========================= */
.cart-payment__box {
    background-color: #544889;
    background-color: hsl(var(--section-bg));
    padding: 84px 0;
    border-radius: 24px;
}

@media screen and (max-width: 991px) {
    .cart-payment__box {
        padding: 56px 0;
    }
}

@media screen and (max-width: 575px) {
    .cart-payment__box {
        padding: 24px;
    }
}
/* Payment Method */
.payment-method {
    margin-bottom: 72px;
}

@media screen and (max-width: 991px) {
    .payment-method {
        margin-bottom: 60px;
    }
}

@media screen and (max-width: 767px) {
    .payment-method {
        margin-bottom: 40px;
    }
}

.payment-method__item .form-check-input:checked + .form-check-label::before {
    background: var(--main-gradient);
}

.payment-method__item .form-check-input:checked + .form-check-label::after {
    background: hsl(var(--white));
    width: calc(100% - 4px);
    height: calc(100% - 4px);
}

.payment-method__item .form-check-label {
    background-color: hsl(var(--gray-seven));
    position: relative;
    z-index: 1;
    border-radius: 4px;
    width: 94px;
    height: 64px;
    display: flex !important;
    justify-content: center;
    align-items: center;
    padding: 4px;
    transition: 0.2s linear;
    width: 100%;
    cursor: pointer;
}

.payment-method__item .form-check-label::before, .payment-method__item .form-check-label::after {
    border-radius: inherit;
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background: #D9D9D9;
    z-index: -1;
    transition: 0.2s linear;
}

.payment-method__item .form-check-label::after {
    background: hsl(var(--gray-seven));
    width: calc(100% - 2px);
    height: calc(100% - 2px);
}

.payment-method .slick-initialized.slick-slider .slick-slide {
    margin: 0 4px;
}

.payment-method__wrapper {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.payment-method__wrapper .payment-method__item {
    width: 100px;
}

/* Payment Method */
.cart-payment-card {
    position: relative;
    background: var(--main-gradient);
    padding: 24px;
    z-index: 1;
    border-radius: 16px;
}

@media screen and (max-width: 575px) {
    .cart-payment-card {
        padding: 24px 16px;
    }
}

.cart-payment-card::before {
    position: absolute;
    content: "";
    width: calc(100% - 2px);
    height: calc(100% - 2px);
    background-color: hsl(var(--white));
    left: 1px;
    top: 1px;
    border-radius: inherit;
    z-index: -1;
}

.total-bill {
    background-color: hsl(var(--gray-seven));
    padding: 16px;
    border-radius: 4px;
}

/* ======================= Cart Payment Section Css End ========================= */
/* ========================= Cart Thank You Section Css Start ======================= */
.cart-thank__box {
    border-radius: 24px;
    background-color: hsl(var(--section-bg));
    padding: clamp(1rem, -3.858rem + 7.843vw, 3rem);
    background-color: hsl(var(--white)/0.45);
    backdrop-filter: blur(12px);
    box-shadow: var(--box-shadow);
}

@media screen and (max-width: 575px) {
    .cart-thank__box {
        padding: 0px;
        background-color: transparent;
    }
}

.cart-thank__img img {
    max-width: 56%;
}

/* thank card */
.thank-card {
    padding: clamp(1rem, -2.643rem + 5.882vw, 2.5rem);
    border-radius: 24px;
    height: 100%;
    position: relative;
    z-index: 1;
    transition: 0.2s linear;
    background: transparent;
}

.thank-card:hover {
    box-shadow: 0px 20px 30px 0px rgba(197, 196, 201, 0.25);
    background: var(--main-gradient);
}

.thank-card::before {
    position: absolute;
    content: "";
    width: calc(100% - 2px);
    height: calc(100% - 2px);
    background: hsl(var(--white));
    left: 1px;
    top: 1px;
    border-radius: inherit;
    z-index: -1;
    transition: 0.2s linear;
}

/* List Text Start */
.list-text {
    border: 1px solid hsl(var(--gray-five));
    border-radius: 16px;
}

.list-text__item {
    border-bottom: 1px solid hsl(var(--gray-five));
}

.list-text__item .text {
    padding: 16px 24px;
    border-right: 1px solid hsl(var(--gray-five));
}

@media screen and (max-width: 1199px) {
    .list-text__item .text {
        padding: 16px;
    }
}

@media screen and (max-width: 424px) {
    .list-text__item .text {
        padding: 16px 8px;
    }
}

.list-text__item .text:first-child {
    width: 50%;
}

.list-text__item .text:last-child {
    border-right: 0;
}

.list-text__item:last-child {
    border-bottom: 0;
}

/* List Text End */
/* ========================= Cart Thank You Section Css End ======================= */
/* ======================= Blog Details Section Start ========================= */
.blog-details::before, .blog-details::after {
    position: absolute;
    content: "";
    width: 573px;
    height: 355px;
    background: linear-gradient(152deg, rgba(246, 246, 246, 0.2) 17.12%, rgba(5, 228, 242, 0.2) 105.91%);
    filter: blur(100px);
    left: 24px;
    top: 0;
    z-index: -1;
}

.blog-details::after {
    background: linear-gradient(152deg, rgba(246, 246, 246, 0.2) 17.12%, rgba(94, 53, 242, 0.2) 105.91%);
    left: auto;
    right: 40px;
}

/* blog details top Start */
.blog-details-top {
    max-width: 1040px;
}

.blog-details-top__desc {
    max-width: 746px;
}

.blog-details-top__thumb img {
    width: 40px;
    height: 40px;
    overflow: hidden;
    border-radius: 50%;
}

/* blog details top End */
/* blog details content Start */
.blog-details-content__thumb {
    border-radius: 16px;
    overflow: hidden;
    height: 100%;
    max-height: 460px;
}

.blog-details-content__thumb img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.quote-text {
    padding: 0 8px;
    padding-right: 24px;
    border-left: 4px solid hsl(var(--main));
    position: relative;
}

.quote-text__icon {
    position: absolute;
    right: 0;
    top: 0;
}

.post-tag__link {
    padding: 5px 12px;
    background-color: hsl(var(--section-bg));
}

.post-tag__link:hover {
    color: hsl(var(--white)) !important;
    background-color: hsl(var(--main));
}

/* blog details content End */
/* Advisor Start */
.advisor {
    border-radius: 8px;
}

.advisor__thumb {
    width: clamp(3.75rem, 0.707rem + 6.339vw, 6.25rem);
    height: clamp(3.75rem, 0.707rem + 6.339vw, 6.25rem);
    border-radius: 50%;
    overflow: hidden;
}

/* Advisor End */
/* ======================= Blog Details Section End ========================= */
/* ======================= Contact Page Css Start ============================= */
.contact-info__item-wrapper {
    padding-top: clamp(1.5rem, 0.891rem + 1.268vw, 2rem) !important;
    margin-top: clamp(1.5rem, 0.891rem + 1.268vw, 2rem) !important;
    border-top: 1px solid hsl(var(--gray-five));
}

/* ======================= Contact Page Css End ============================= */
/* ========================== Account Page Css Start ============================= */
.account {
    position: relative;
}

.account__img {
    position: absolute;
    right: 0;
    bottom: 0;
}

@media screen and (max-width: 991px) {
    .account__img {
        display: none;
    }
}

.account__left {
    width: 43%;
    padding: 32px;
    position: relative;
}

@media screen and (max-width: 991px) {
    .account__left {
        width: 60%;
    }
}

.account__left::before {
    position: absolute;
    content: "";
    width: 675px;
    height: 552px;
    background: linear-gradient(152deg, rgba(246, 246, 246, 0.3) 17.12%, rgba(94, 53, 242, 0.3) 105.91%);
    filter: blur(100px);
    left: -290px;
    top: 50%;
    transform: translateY(-50%);
    z-index: -1;
}

.account__right {
    flex-grow: 1;
    padding-left: 32px;
    padding-right: 32px;
    padding-bottom: 300px;
}

@media screen and (max-width: 991px) {
    .account__right {
        padding-bottom: 80px;
    }
}

@media screen and (max-width: 575px) {
    .account__right {
        padding-bottom: 60px;
    }
}

@media screen and (max-width: 575px) {
    .account__right {
        padding-left: 24px;
        padding-right: 24px;
    }
}

.account-thumb {
    max-width: 572px;
    margin-left: auto;
    margin-right: auto;
}

.account-content {
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

/* ========================== Account Page Css End ============================= */
/* ===================== Dashboard Start ======================= */
.dashboard-nav, .dashboard-body__content, .dashboard-footer {
    padding-left: 32px;
    padding-right: 32px;
}

@media screen and (max-width: 1399px) {
    .dashboard-nav, .dashboard-body__content, .dashboard-footer {
        padding-left: 24px;
        padding-right: 24px;
    }
}

.dashboard-footer {
    margin-top: auto !important;
}

.dashboard {
    background-color: hsl(var(--gray-seven));
}

.dashboard-body {
    width: calc(100% - 312px);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

@media screen and (max-width: 991px) {
    .dashboard-body {
        width: 100%;
    }
}

.dashboard-body__content {
    margin: 32px 0;
}

.dashboard-body__item {
    margin-bottom: 32px;
}

.dashboard-body__item:last-child {
    margin-bottom: 0;
}

/* Sidebar Collapse */
@media (min-width: 992px) {
    .dashboard.active .dashboard-sidebar {
        width: auto;
    }

    .dashboard.active .dashboard-sidebar .logo {
        display: none;
    }

    .dashboard.active .dashboard-sidebar .logo.favicon {
        display: block;
    }

    .dashboard.active .dashboard-sidebar .sidebar-list__link {
        width: 44px;
        height: 44px;
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .dashboard.active .dashboard-sidebar .sidebar-list__link .text {
        display: none;
    }

    .dashboard.active .bar-icon {
        display: none;
    }

    .dashboard.active .arrow-icon {
        display: block;
    }
}
/* Sidebar Collapse End */
/* Dashboard Widget Css Start */
.dashboard-widget {
    padding: 16px 24px;
    border-radius: 8px;
    background-color: transparent;
    position: relative;
    z-index: 1;
    transition: 0.2s linear;
}

@media screen and (max-width: 1399px) {
    .dashboard-widget {
        padding: 16px;
    }
}

.dashboard-widget:hover {
    box-shadow: 0px 20px 30px 0px rgba(197, 196, 201, 0.25);
    background: var(--main-gradient);
}

.dashboard-widget:hover .dashboard-widget__shape.one {
    visibility: hidden;
    opacity: 0;
}

.dashboard-widget:hover .dashboard-widget__shape.two {
    visibility: visible;
    opacity: 1;
}

.dashboard-widget::before {
    position: absolute;
    content: "";
    width: calc(100% - 2px);
    height: calc(100% - 2px);
    background: hsl(var(--white));
    left: 1px;
    top: 1px;
    border-radius: inherit;
    z-index: -1;
    transition: 0.2s linear;
}

.dashboard-widget__shape {
    position: absolute;
    right: 0;
    top: 16px;
    transition: 0.2s linear;
}

.dashboard-widget__shape.two {
    visibility: hidden;
    opacity: 0;
}

.dashboard-widget__icon {
    width: 72px;
    height: 72px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    background-color: hsl(var(--gray-seven));
}

@media (min-width: 992px) and (max-width: 1399px) {
    .dashboard-widget__icon {
        width: 60px;
        height: 60px;
    }
}

@media (min-width: 992px) and (max-width: 1399px) {
    .dashboard-widget__number {
        font-size: 22px;
    }
}
/* Dashboard Widget Css End */
/* Dashboard Card Css Start */
.dashboard-card {
    background-color: hsl(var(--white));
    border-radius: 16px;
    border: 1px solid hsl(var(--gray-five));
    height: 100%;
}

.dashboard-card__header {
    padding: 24px;
    border-bottom: 1px solid hsl(var(--gray-five));
}

.dashboard-card .select-has-icon::before {
    right: 12px;
}

.dashboard-card .select-sm {
    background-color: hsl(var(--gray-seven)) !important;
    border-color: hsl(var(--gray-five));
    padding: 6px 12px;
    width: auto;
}

/* Chart Css Start */
.apexcharts-toolbar {
    display: none !important;
}

.apexcharts-legend {
    display: none !important;
}

/* Chart Css End */
.country-list__item {
    padding: 10px 24px;
    border-bottom: 1px dashed hsl(var(--gray-five));
}

.country-list__item:last-child {
    border-bottom: 0;
}

.country-list__flag {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    overflow: hidden;
}

/* Dashboard Card Css End */
/* ===================== Dashboard End ======================= */
/* ======================== Dashboard Profile Css Start ========================= */
.profile-info {
    padding: 32px 16px;
    background-color: hsl(var(--white));
    border: 1px solid hsl(var(--gray-five));
    border-radius: 8px;
}

@media screen and (max-width: 1199px) {
    .profile-info {
        padding: 24px 16px;
    }
}
/* Cover Photo Upload Css Start */
.cover-photo .avatar-upload {
    display: block;
}

.cover-photo .avatar-upload .avatar-edit {
    top: 38px;
    bottom: auto;
    right: 32px;
}

.cover-photo .avatar-upload .avatar-edit input + label {
    width: unset;
    height: unset;
    border-radius: 8px;
    padding: 8px 12px;
    color: hsl(var(--white));
    gap: 8px;
    border: 0;
    background: hsl(var(--black)/0.5);
    backdrop-filter: blur(8px);
    font-weight: 300;
}

.cover-photo .avatar-upload .avatar-edit input + label:hover {
    background: hsl(var(--black)/0.8);
}

.cover-photo .avatar-upload .avatar-preview {
    height: 300px;
    width: 100%;
    border-radius: 0;
    background-image: url(../images/thumbs/cover-photo.png);
}

@media screen and (max-width: 991px) {
    .cover-photo .avatar-upload .avatar-preview {
        height: 250px;
    }
}

.cover-photo .avatar-upload .avatar-preview > div {
    border-radius: 0;
}

/* Cover Photo Upload Css End */
/* Image Upload Css Start */
.avatar-upload {
    position: relative;
    margin: 0 auto;
    display: inline-block;
}

.avatar-upload .avatar-edit {
    position: absolute;
    right: 8px;
    z-index: 1;
    bottom: 0;
}

.avatar-upload .avatar-edit input {
    display: none;
}

.avatar-upload .avatar-edit input + label {
    display: inline-block;
    width: 30px;
    height: 30px;
    margin-bottom: 0;
    border-radius: 100%;
    background: var(--main-gradient);
    border: 1px solid hsl(var(--white));
    cursor: pointer;
    font-weight: normal;
    transition: all 0.2s ease-in-out;
    display: flex;
    justify-content: center;
    align-items: center;
}

.avatar-upload .avatar-preview {
    width: 116px;
    height: 116px;
    position: relative;
    border-radius: 100%;
    background-image: url(../images/thumbs/profile-info-img.png);
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
}

.avatar-upload .avatar-preview > div {
    width: 100%;
    height: 100%;
    border-radius: 100%;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
}

/* Image Upload Css End */
.profile-info-list__item {
    display: grid;
    grid-template-columns: 1fr 1.4fr;
    gap: 8px;
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px dashed hsl(var(--gray-four));
}

.profile-info-list__item:last-child {
    padding-bottom: 0;
    margin-bottom: 0;
    border-bottom: 0;
}

@media screen and (max-width: 1599px) {
    .profile-info-list__item {
        font-size: 0.875rem;
    }
}

@media screen and (max-width: 424px) {
    .profile-info-list__item {
        font-size: 0.8125rem;
    }
}

.profile-info-content {
    padding: 24px;
}

/* ======================== Dashboard Profile Css End ========================= */
/* ===================== Gradient Bg Css Start ======================= */
.bg--gradient, .bg-pattern {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    object-fit: cover;
}

/* ===================== Gradient Bg Css End ======================= */
/* ======================== Element Css Start ========================== */
/* Curve Shape */
.curve-shape {
    position: absolute;
    bottom: calc(100% - 1px);
    left: 0;
}

.curve-shape.right {
    left: auto;
    right: 0;
}

@media screen and (max-width: 1399px) {
    .curve-shape {
        max-width: 250px;
    }
}

@media screen and (max-width: 991px) {
    .curve-shape {
        max-width: 140px;
    }
}
/* Curve Shape */
/* Brush Element */
.element-brush {
    position: absolute;
    z-index: -1;
    left: 50px;
    top: 64%;
    animation: upDownRotate 15s linear infinite;
}

/* Element */
.element {
    position: absolute;
    left: 90px;
    bottom: 22%;
    animation: upDownRotate 15s linear infinite;
    z-index: -1;
}

.element.two {
    left: auto;
    right: 90px;
    bottom: auto;
    top: 22%;
    animation-delay: 3s;
}

.element.three {
    left: auto;
    right: 16%;
    bottom: 14%;
    animation-delay: 5s;
}

@keyframes upDownRotate {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }

    50% {
        transform: translateY(200px) rotate(360deg);
    }

    100% {
        transform: translateY(0px) rotate(720deg);
    }
}
/* Line Vector Element */
.line-vector {
    position: absolute;
    left: 0;
    top: 0;
    z-index: -1;
    max-width: 46%;
}

.line-vector.two {
    left: auto;
    right: 0;
}

.endfooter {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%; /* 使元素宽度撑满整个视口宽度 */
}
.bodycontent{
    flex: 1; /* 让主内容区域占据所有可用空间，如果内容不足以填充屏幕，页脚会被推到底部 */
    margin-bottom: 100px;

}

.notice{
    margin: 100;
    padding: 5;
    box-sizing: border-box;

}
@media screen and (max-width: 767px) {
.notice{
    margin: 10;
    padding: 5;
    box-sizing: border-box;

}
/* ======================== Element Css End ========================== */
/*# sourceMappingURL=main.css.map */
