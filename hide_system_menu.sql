-- 隐藏dujiaoka后台左侧菜单中的"系统"选项
-- 执行此SQL语句将隐藏系统菜单及其所有子菜单

-- 方法1：隐藏系统菜单（推荐）
-- 将show字段设置为0来隐藏菜单
UPDATE `admin_menu` SET `show` = 0 WHERE `id` = 2 AND `title` = 'Admin';

-- 方法2：如果需要恢复显示系统菜单，执行以下语句
-- UPDATE `admin_menu` SET `show` = 1 WHERE `id` = 2 AND `title` = 'Admin';

-- 查看当前菜单状态
SELECT `id`, `parent_id`, `title`, `icon`, `uri`, `show` 
FROM `admin_menu` 
WHERE `id` = 2 OR `parent_id` = 2 
ORDER BY `id`;

-- 注意：
-- 1. 隐藏系统菜单后，管理员、角色、权限、菜单等功能将无法通过左侧菜单访问
-- 2. 但仍可以通过直接访问URL来使用这些功能，例如：
--    - /admin/auth/users (管理员)
--    - /admin/auth/roles (角色)
--    - /admin/auth/permissions (权限)
--    - /admin/auth/menu (菜单)
-- 3. 建议在隐藏前确保有其他方式访问这些重要功能
