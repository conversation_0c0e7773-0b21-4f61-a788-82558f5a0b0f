{% extends "base.html" %}

{% block title %}统计报表 - 鱼苗总后台管理系统{% endblock %}
{% block page_title %}统计报表{% endblock %}

{% block content %}
<!-- 时间范围选择 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3 align-items-end">
            <div class="col-md-3">
                <label for="period" class="form-label">统计周期</label>
                <select class="form-select" id="period" name="period" onchange="toggleCustomDate()">
                    <option value="today" {% if stats.period == 'today' %}selected{% endif %}>今天</option>
                    <option value="yesterday" {% if stats.period == 'yesterday' %}selected{% endif %}>昨天</option>
                    <option value="week" {% if stats.period == 'week' %}selected{% endif %}>最近7天</option>
                    <option value="month" {% if stats.period == 'month' %}selected{% endif %}>最近30天</option>
                    <option value="custom" {% if stats.period == 'custom' %}selected{% endif %}>自定义日期</option>
                </select>
            </div>
            <div class="col-md-3" id="customDateDiv" style="display: {% if stats.period == 'custom' %}block{% else %}none{% endif %};">
                <label for="date" class="form-label">选择日期</label>
                <input type="date" class="form-control" id="date" name="date" value="{{ custom_date }}">
            </div>
            <div class="col-md-3">
                <label for="database" class="form-label">数据库筛选</label>
                <select class="form-select" id="database" name="database">
                    <option value="">全部数据库</option>
                    {% for db_name in stats.database_list %}
                    <option value="{{ db_name }}" {% if stats.selected_database == db_name %}selected{% endif %}>
                        {{ db_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-2"></i>
                    查询
                </button>
            </div>
            <div class="col-md-2">
                <button type="button" class="btn btn-outline-success w-100" onclick="exportReport()">
                    <i class="fas fa-download me-2"></i>
                    导出报表
                </button>
            </div>
            <div class="col-md-2">
                <button type="button" class="btn btn-outline-info w-100" onclick="refreshData()">
                    <i class="fas fa-sync-alt me-2"></i>
                    刷新数据
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 当前筛选条件 -->
<div class="alert alert-info mb-4">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h6 class="mb-1">
                <i class="fas fa-filter me-2"></i>当前筛选条件
            </h6>
            <div class="d-flex flex-wrap gap-2">
                <span class="badge bg-primary">
                    <i class="fas fa-calendar me-1"></i>
                    {% if stats.period == 'today' %}今天
                    {% elif stats.period == 'yesterday' %}昨天
                    {% elif stats.period == 'week' %}最近7天
                    {% elif stats.period == 'month' %}最近30天
                    {% elif stats.period == 'custom' %}自定义日期
                    {% endif %}
                </span>
                {% if stats.selected_database %}
                <span class="badge bg-success">
                    <i class="fas fa-database me-1"></i>{{ stats.selected_database }}
                </span>
                {% else %}
                <span class="badge bg-secondary">
                    <i class="fas fa-database me-1"></i>全部数据库
                </span>
                {% endif %}
                <span class="badge bg-info">
                    <i class="fas fa-clock me-1"></i>
                    {{ stats.start_date|datetime }} 至 {{ stats.end_date|datetime }}
                </span>
            </div>
        </div>
        <div class="col-md-4 text-end">
            {% if stats.selected_database %}
            <a href="{{ url_for('main.statistics', period=stats.period, date=custom_date) }}"
               class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-times me-1"></i>清除数据库筛选
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- 统计概览 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="stats-number">{{ stats.transfer_count or 0 }}</div>
                        <div class="text-white-50">转账次数</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exchange-alt fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card stats-card-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="stats-number">{{ "%.2f"|format(stats.total_amount or 0) }}</div>
                        <div class="text-white-50">总转账金额</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-coins fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card stats-card-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="stats-number">{{ "%.6f"|format(stats.avg_amount or 0) }}</div>
                        <div class="text-white-50">平均转账金额</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calculator fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card stats-card-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="stats-number">
                            {% if stats.transfer_count > 0 %}
                                {{ "%.1f"|format((stats.total_amount / stats.transfer_count) * 100) }}%
                            {% else %}
                                0.0%
                            {% endif %}
                        </div>
                        <div class="text-white-50">成功率</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-percentage fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 时间范围显示 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            <strong>统计时间范围:</strong>
            {{ stats.start_date|datetime }} 至 {{ stats.end_date|datetime }}
        </div>
    </div>
</div>

<!-- 图表展示 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    每日转账趋势
                </h5>
            </div>
            <div class="card-body">
                <canvas id="dailyTrendChart" height="80"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 数据库统计 -->
{% if not stats.selected_database and stats.database_stats %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-database me-2"></i>
                    数据库统计
                </h5>
                <span class="badge bg-primary">{{ stats.database_stats|length }} 个数据库</span>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>数据库</th>
                                <th>转账次数</th>
                                <th>总金额</th>
                                <th>截流金额</th>
                                <th>正常转账</th>
                                <th>截流率</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for db_stat in stats.database_stats %}
                            <tr>
                                <td>
                                    <strong>{{ db_stat.database_name }}</strong>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ db_stat.count }}</span>
                                </td>
                                <td>
                                    <strong class="text-success">{{ "%.2f"|format(db_stat.total_amount) }} USDT</strong>
                                </td>
                                <td>
                                    <strong class="text-warning">{{ "%.2f"|format(db_stat.intercept_amount) }} USDT</strong>
                                </td>
                                <td>
                                    <strong class="text-info">{{ "%.2f"|format(db_stat.normal_amount) }} USDT</strong>
                                </td>
                                <td>
                                    {% if db_stat.total_amount > 0 %}
                                        {% set intercept_rate = (db_stat.intercept_amount / db_stat.total_amount * 100) %}
                                        <div class="progress" style="height: 20px; width: 80px;">
                                            <div class="progress-bar bg-warning" role="progressbar"
                                                 style="width: {{ intercept_rate }}%">
                                                {{ "%.1f"|format(intercept_rate) }}%
                                            </div>
                                        </div>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('main.statistics', database=db_stat.database_name, period=stats.period, date=custom_date) }}"
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> 查看详情
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- 每日详细数据 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-table me-2"></i>
                    每日详细数据
                    {% if stats.selected_database %}
                    <small class="text-muted ms-2">({{ stats.selected_database }})</small>
                    {% endif %}
                </h5>
                <span class="badge bg-primary">{{ stats.daily_stats|length }} 天</span>
            </div>
            <div class="card-body p-0">
                {% if stats.daily_stats %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>日期</th>
                                    <th>转账次数</th>
                                    <th>转账金额</th>
                                    <th>平均金额</th>
                                    <th>占比</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for daily in stats.daily_stats %}
                                <tr>
                                    <td>
                                        <strong>{{ daily.date }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ daily.count }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">{{ "%.6f"|format(daily.amount) }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            {% if daily.count > 0 %}
                                                {{ "%.6f"|format(daily.amount / daily.count) }}
                                            {% else %}
                                                0.000000
                                            {% endif %}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            {% set percentage = (daily.amount / stats.total_amount * 100) if stats.total_amount > 0 else 0 %}
                                            <div class="progress-bar" role="progressbar" 
                                                 style="width: {{ percentage }}%"
                                                 aria-valuenow="{{ percentage }}" 
                                                 aria-valuemin="0" 
                                                 aria-valuemax="100">
                                                {{ "%.1f"|format(percentage) }}%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-chart-line fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无统计数据</h5>
                        <p class="text-muted">选择的时间范围内没有转账记录</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 数据加载提示 -->
<div id="loadingOverlay" class="position-fixed top-0 start-0 w-100 h-100 d-none" 
     style="background: rgba(0,0,0,0.5); z-index: 9999;">
    <div class="d-flex justify-content-center align-items-center h-100">
        <div class="card">
            <div class="card-body text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mb-0">正在加载统计数据...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 切换自定义日期显示
    function toggleCustomDate() {
        const period = document.getElementById('period').value;
        const customDateDiv = document.getElementById('customDateDiv');
        customDateDiv.style.display = period === 'custom' ? 'block' : 'none';
    }
    
    // 导出报表
    function exportReport() {
        showLoading();
        // 模拟导出过程
        setTimeout(function() {
            hideLoading();
            showToast('报表导出完成', 'success');
        }, 2000);
    }
    
    // 刷新数据
    function refreshData() {
        showLoading();
        location.reload();
    }
    
    // 显示加载提示
    function showLoading() {
        document.getElementById('loadingOverlay').classList.remove('d-none');
    }
    
    // 隐藏加载提示
    function hideLoading() {
        document.getElementById('loadingOverlay').classList.add('d-none');
    }
    
    // 显示提示消息
    function showToast(message, type) {
        const toastContainer = document.getElementById('toastContainer') || createToastContainer();
        
        const toastId = 'toast_' + Date.now();
        const toastHtml = `
            <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'primary'} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;
        
        toastContainer.insertAdjacentHTML('beforeend', toastHtml);
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement);
        toast.show();
        
        // 自动移除
        toastElement.addEventListener('hidden.bs.toast', function() {
            toastElement.remove();
        });
    }
    
    // 创建Toast容器
    function createToastContainer() {
        const container = document.createElement('div');
        container.id = 'toastContainer';
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '1055';
        document.body.appendChild(container);
        return container;
    }
    
    // 初始化图表
    document.addEventListener('DOMContentLoaded', function() {
        // 每日趋势图表
        const dailyTrendCtx = document.getElementById('dailyTrendChart').getContext('2d');
        const dailyData = {{ stats.daily_stats | tojson }};
        
        new Chart(dailyTrendCtx, {
            type: 'line',
            data: {
                labels: dailyData.map(d => d.date),
                datasets: [{
                    label: '转账次数',
                    data: dailyData.map(d => d.count),
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    yAxisID: 'y'
                }, {
                    label: '转账金额',
                    data: dailyData.map(d => d.amount),
                    borderColor: '#764ba2',
                    backgroundColor: 'rgba(118, 75, 162, 0.1)',
                    tension: 0.4,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: '日期'
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: '转账次数'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: '转账金额 (USDT)'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });

    });
</script>
{% endblock %}
