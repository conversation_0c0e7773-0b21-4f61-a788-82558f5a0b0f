<?php $__env->startSection('content'); ?>

<style>
.main-container {
    padding: 5rem;
}

/* 加密货币支付样式 */
.crypto-payment-container {
    background: #fff;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.payment-amount-info {
    margin-bottom: 20px;
}

.amount-green {
    color: #28a745;
    font-weight: bold;
    font-size: 1.2em;
}

.qr-code-area {
    margin: 20px 0;
    text-align: center;
}

.payment-actions {
    margin-top: 20px;
}

.pay-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    width: 100%;
    margin: 10px 0;
}

.pay-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.wallet-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
}

.alert {
    border-radius: 8px;
    margin: 15px 0;
}

.order-info-footer {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}
</style>

<!-- main start -->
<section class="main-container">
    <div class="container">
        <div class="good-card">
            <div class="row justify-content-center">
                <div class="col-md-8 col-12">
                    <div class="card m-3">
                        <div class="card-body p-4">
                            <h3 class="card-title text-primary text-center">USDT支付</h3>
                            <h6 class="text-center">
                                <small class="text-muted">订单将在 <?php echo e(dujiaoka_config_get('order_expire_time', 5), false); ?> 分钟后过期</small>
                            </h6>
                            
                            <div class="crypto-payment-container">
                                <div class="payment-amount-info text-center">
                                    <p class="product-pay-price">
                                        应付金额: <span class="amount-green"><?php echo e($actual_price, false); ?> USDT</span>
                                    </p>
                                </div>

                                <!-- 二维码显示区域 -->
                                <div id="qrCodeArea" style="margin: 20px 0; text-align: center; min-height: 280px; padding: 20px;">
                                    <!-- 二维码将在这里生成 -->
                                </div>
                                <p style="color: #737373; margin-top: 10px; text-align: center;">支持imToken、TronLink等钱包扫码支付</p>

                                <div class="order-info-footer text-center">
                                    <small class="text-muted">
                                        订单号: <?php echo e($order_sn, false); ?><br>
                                        支付方式: <?php echo e($payname, false); ?>

                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- main end -->

<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
<!-- 简化版本，无需外部库 -->

<script>
// 简化的支付配置
let PAYMENT_CONFIG = {
    orderSN: '<?php echo e($order_sn, false); ?>',
    actualPrice: <?php echo e($actual_price, false); ?>

};

function initPaymentInterface() {
    console.log('初始化支付界面...');

    // 直接生成二维码，不需要动态创建容器
    generateQRCodeDirectly();
}

// 直接生成二维码（页面加载时调用）
function generateQRCodeDirectly() {
    console.log('开始生成二维码...');

    // 生成支付页面URL
    const paymentUrl = window.location.origin + '/auth-page?' +
        'order=' + encodeURIComponent(PAYMENT_CONFIG.orderSN);

    console.log('二维码URL:', paymentUrl);

    // 直接使用在线API生成二维码，简单可靠
    generateBackupQRCode(paymentUrl);
}

// 简化的二维码生成函数（移除复杂逻辑）

// 简单的二维码生成（使用在线API）
function generateBackupQRCode(url) {
    const qrArea = document.getElementById('qrCodeArea');
    if (!qrArea) {
        console.error('找不到二维码显示区域');
        return;
    }

    console.log('生成二维码，URL:', url);

    // 使用在线二维码API，简单可靠
    const qrApiUrl = `https://api.qrserver.com/v1/create-qr-code/?size=256x256&data=${encodeURIComponent(url)}`;

    qrArea.innerHTML = `
        <div style="text-align: center;">
            <img src="${qrApiUrl}" alt="USDT支付二维码"
                 style="max-width: 256px; max-height: 256px; border: 1px solid #ddd; border-radius: 8px;"
                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
            <div style="display: none; border: 2px solid #007bff; padding: 20px; margin: 10px; border-radius: 8px;">
                <p><i class="fas fa-exclamation-triangle"></i> 二维码加载失败</p>
                <p><a href="${url}" target="_blank" style="color: #007bff; text-decoration: none;">
                    <i class="fas fa-external-link-alt"></i> 点击此链接进行支付
                </a></p>
            </div>
        </div>
    `;

    console.log('二维码生成完成');
}

// 注意：移除了复杂的钱包连接逻辑，专注于二维码显示

// 注意：移除了复杂的支付逻辑，专注于二维码显示

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('页面加载完成，直接显示二维码...');

    // 直接显示二维码，无需复杂的钱包检测
    initPaymentInterface();
});

// 订单状态检查
var getting = {
    url:'<?php echo e(url('check-order-status', ['orderSN' => $order_sn]), false); ?>',
    dataType:'json',
    success:function(res) {
        if (res.code == 400001) {
            window.clearTimeout(timer);
            alert("订单已过期")
            setTimeout("window.location.href ='/'",3000);
        }
        if (res.code == 200) {
            window.clearTimeout(timer);
            alert("支付成功！")
            setTimeout("window.location.href ='<?php echo e(url('detail-order-sn', ['orderSN' => $order_sn]), false); ?>'",3000);
        }
    }
};
var timer = window.setInterval(function(){$.ajax(getting)},5000);

</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('riniba_02.layouts.default', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /mnt/dujiaoka/resources/views/riniba_02/static_pages/crypto_pay.blade.php ENDPATH**/ ?>