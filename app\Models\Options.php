<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class Options extends Model
{
    protected $table = 'options';
    protected $primaryKey = 'id';
    public $timestamps = false;
    
    protected $fillable = ['name', 'value', 'remarks', 'timestamp'];
    
    protected static function boot()
    {
        parent::boot();
        
        static::saving(function ($model) {
            $model->timestamp = time();
            
            if (!preg_match('/^[a-zA-Z0-9_]+$/', $model->name)) {
                throw new \Exception('配置项名称只能包含字母、数字和下划线');
            }
            
            Cache::forget('all_options');
            Cache::forget('option_' . $model->name);
        });
    }
    
    /**
     * 获取配置值
     */
    public static function getValue($name, $default = null)
    {
        return Cache::remember('option_' . $name, 3600, function () use ($name, $default) {
            $option = self::where('name', $name)->first();
            return $option ? $option->value : $default;
        });
    }
    
    /**
     * 设置配置值
     */
    public static function setValue($name, $value, $remarks = null)
    {
        return self::updateOrCreate(
            ['name' => $name],
            [
                'value' => $value,
                'remarks' => $remarks,
                'timestamp' => time()
            ]
        );
    }
    
    /**
     * 获取所有配置
     */
    public static function getAllOptions()
    {
        return Cache::remember('all_options', 3600, function () {
            return self::pluck('value', 'name')->toArray();
        });
    }
    
    /**
     * 批量设置配置
     */
    public static function setBatch($options)
    {
        foreach ($options as $name => $value) {
            self::setValue($name, $value);
        }
    }
    
    /**
     * 删除配置
     */
    public static function deleteOption($name)
    {
        Cache::forget('option_' . $name);
        Cache::forget('all_options');
        return self::where('name', $name)->delete();
    }
    
    /**
     * 获取配置分组
     */
    public static function getGroupedOptions()
    {
        $options = self::all();
        $grouped = [];
        
        foreach ($options as $option) {
            $parts = explode('_', $option->name);
            $group = $parts[0];
            $grouped[$group][] = $option;
        }
        
        return $grouped;
    }
}
